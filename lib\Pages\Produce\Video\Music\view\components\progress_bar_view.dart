import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/Music/view/components/range_music_component.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/utility/extension/extensions.dart';
import 'package:flutter/material.dart';

class RangerAndProgressBarView extends StatefulWidget {
  const RangerAndProgressBarView(
      {super.key,
      required this.scrollController,
      required this.itemNumber,
      required this.containerCenter,
      required this.spaceBetweenContainers,
      required this.singleLineWidth,
      required this.rangerWidth,
      required this.sliderFillerValue,
      required this.timerScrollListener,
      required this.videoLength,
      required this.currentPosition,
      required this.durationOfMusic});

  final ScrollController scrollController;
  final int itemNumber;
  final double containerCenter;
  final double spaceBetweenContainers;
  final double singleLineWidth;
  final double rangerWidth;
  final double sliderFillerValue;
  final Timer? timerScrollListener;
  final double videoLength;
  final Duration currentPosition;
  final Duration durationOfMusic;

  @override
  State<RangerAndProgressBarView> createState() => _RangerAndProgressBarViewState();
}

class _RangerAndProgressBarViewState extends State<RangerAndProgressBarView> {
  final String _sec = LocaleKeys.AddMusicPage_secondShortage;
  final String _min = LocaleKeys.AddMusicPage_minutesShortage;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          formatVideoLength((widget.videoLength)),
        ),
        SizedBox(
          height: 60,
          child: Stack(
            children: [
              ListView.builder(
                controller: widget.scrollController,
                scrollDirection: Axis.horizontal,
                itemCount: widget.itemNumber,
                itemBuilder: (context, index) {
                  // Neden -1 yazıldığını açıkla.
                  if (index == 0 || index == widget.itemNumber - 1) {
                    return SizedBox(width: widget.containerCenter);
                  }
                  bool isIndexOdd = index % 2 == 0;
                  bool isFirst = index == 1;
                  return Row(
                    children: [
                      if (!isFirst) SizedBox(width: widget.spaceBetweenContainers),
                      Container(
                        width: widget.singleLineWidth,
                        height: isIndexOdd ? 18 : 30,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(Radius.circular(4)),
                          color: AppColors.text,
                        ),
                      ),
                    ],
                  );
                },
              ),
              RangeMusicComponent(
                containerCenter: widget.containerCenter,
                rangerWidth: widget.rangerWidth,
                sliderFillerValue: widget.sliderFillerValue,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${widget.currentPosition.formatTime()} / ${widget.durationOfMusic.formatTime()}',
          style: TextStyle(
            fontSize: 16,
            color: AppColors.main,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String formatVideoLength(double videoLength) {
    int totalSeconds = videoLength.round(); // Convert double to int by rounding
    int minutes = totalSeconds ~/ 60;
    int seconds = totalSeconds % 60;

    if (minutes == 0) {
      return '$seconds ${_sec.tr()}';
    } else {
      return '$minutes ${_min.tr()} $seconds ${_sec.tr()}';
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/Streak/StreakHistoryPage/Widget/year_streak.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/streak_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StreakHistoryPage extends StatelessWidget {
  StreakHistoryPage({
    super.key,
  });

  // Texts
  final String streakHistory = LocaleKeys.Home_Streak_StreakHistory.tr();

  final List<int> yearList = StreakService().getYears();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(streakHistory),
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 50.h),
          Expanded(
            child: ListView.builder(
              itemCount: yearList.length,
              itemBuilder: (context, index) {
                return StreakYear(year: yearList[index]);
              },
            ),
          ),
        ],
      ),
    );
  }
}

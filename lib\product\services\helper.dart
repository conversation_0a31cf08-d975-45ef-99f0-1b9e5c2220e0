// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Payment/payment_page.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/PhotoManagePage/photo_manage_page.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/PhotoManagePage/photo_manager_provider.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/CameraProgress/camera_page.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/core/CostumWidgets/Dialogs/sure_dialog.dart';
import 'package:facelog/core/CostumWidgets/rate_us_bottom.dart';
import 'package:facelog/core/CostumWidgets/feed_bottom_sheet.dart';
import 'package:facelog/core/CostumWidgets/note_bottom_sheet.dart';
import 'package:facelog/core/mixins/disk_cache_mixin.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/photo/photo_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/route_manager.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:native_exif/native_exif.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:random_date/random_date.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

// ! TODO: buradaki fonksiyonlar kullanım alanlarına göre farklı dosyalara ayrılabilirler

class Helper with DiskCacheMixin {
  Future<void> getDialog({
    String? title,
    required String message,
    bool withTimer = false,
    Function? onAccept,
    acceptButtonText,
  }) async {
    await Get.dialog(
      CustomDialogWidget(
        title: title,
        contentText: message,
        withTimer: withTimer,
        onAccept: onAccept,
        acceptButtonText: acceptButtonText,
      ),
    );
  }

  void getMessage({
    String? title,
    required String message,
    StatusEnum status = StatusEnum.SUCCESS,
    IconData? icon,
    Duration? duration,
    Function? onMainButtonPressed,
    String? mainButtonText,
  }) {
    Get.snackbar(
      title ??
          (status == StatusEnum.WARNING
              ? LocaleKeys.Message_Warning.tr()
              : status == StatusEnum.INFO
                  ? LocaleKeys.Message_Info.tr()
                  : LocaleKeys.Message_Success.tr()),
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: status == StatusEnum.WARNING ? AppColors.red.withValues(alpha: 0.9) : AppColors.panelBackground.withValues(alpha: 0.9),
      animationDuration: const Duration(milliseconds: 500),
      duration: duration ?? (status == StatusEnum.WARNING ? const Duration(seconds: 4) : const Duration(seconds: 2)),
      dismissDirection: DismissDirection.horizontal,
      icon: icon != null
          ? Icon(icon)
          : (status == StatusEnum.WARNING
              ? const Icon(Icons.warning)
              : status == StatusEnum.INFO
                  ? const Icon(Icons.info)
                  : const Icon(Icons.check)),
      mainButton: onMainButtonPressed != null
          ? TextButton(
              onPressed: () {
                onMainButtonPressed();
                Get.back();
              },
              child: Text(
                mainButtonText ?? LocaleKeys.Dialog_Okay.tr(),
                style: const TextStyle(color: AppColors.white),
              ),
            )
          : null,
    );
  }

  Future<void> showEditDescriptionSheet({
    required BuildContext context,
    OneDayPhotos? oneDayPhoto,
    Function? onDispose,
  }) {
    return showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: true,
      context: context,
      builder: (BuildContext context) => NoteBottomSheet(
        oneDayPhoto: oneDayPhoto,
        onDispose: onDispose,
      ),
    ).then(
      (_) {
        oneDayPhoto?.save();
      },
    );
  }

  Future<void> showPhotoOptionsBottomSheet({
    required BuildContext context,
    required OneDayPhotos oneDayPhotos,
    required int currentPhotoIndex,
  }) {
    return showModalBottomSheet(
      context: context,
      builder: (BuildContext context) => FeedPageBottomSheet(
        oneDayPhotos: oneDayPhotos,
        currentPhotoIndex: currentPhotoIndex,
      ),
    );
  }

  Future<void> showRateUsBottomSheet(BuildContext context) {
    return Get.dialog(
      barrierDismissible: false,
      const RateUsBottomSheet(),
    );
  }

  //Download Photo
  Future<void> downloadPhoto(BuildContext context, Photo photo) async {
    if (Platform.isAndroid) {
      if (!await storageAccessRequest()) return;
    }

    Directory? directory;

    if (Platform.isAndroid) {
      // Android specific code
      directory = Directory('/storage/emulated/0/Download/Facelog/');
    } else {
      // iOS specific code
      directory = await getApplicationDocumentsDirectory();
      directory = Directory('${directory.path}/Facelog/Photos/');
    }

    await directory.create(recursive: true);

    int howManySameName = 0;
    final fileName = DateFormat('MM-dd-yyyy_HH-mm').format(photo.date!);

    // Check if there is a file with the same name
    File newFile;

    // Check if there is a file with the same name.
    while (await File('${directory.path}/Facelog_$fileName${howManySameName == 0 ? "" : "($howManySameName)"}.jpg').exists()) {
      howManySameName++;
    }

    // Copy the file
    newFile = File('${directory.path}/Facelog_$fileName${howManySameName == 0 ? "" : "($howManySameName)"}.jpg');
    await File(photo.path).copy(newFile.path);

    // TODO: IOS için localization farklı olmalı. (Download) değil Facelog/Photos a indiriliyor.
    getMessage(
      message: LocaleKeys.PhotoEvents_ScaffoldMessage_PhotoDownloaded.tr(),
      icon: Icons.download,
    );
  }

  // Download All Photos
  Future<void> downloadAllPhotos(BuildContext context) async {
    // Create folder
    final String folderName = DateTime.now().toString().substring(0, 10).replaceAll("-", "_");
    Directory newFolder;
    int howManySameName = 0;

    // Check if there is a file with the same name.
    while (await Directory('/storage/emulated/0/Download/Facelog/$folderName${howManySameName == 0 ? "" : "($howManySameName)"}').exists()) {
      howManySameName++;
    }

    newFolder = Directory('/storage/emulated/0/Download/Facelog/$folderName${howManySameName == 0 ? "" : "($howManySameName)"}');
    newFolder.createSync(recursive: true);

    final allPhotos = context.read<PhotoProvider>().allPhotosList.map((e) => e.photos).expand((element) => element).toList();

    for (var photo in allPhotos) {
      int howManySameName = 0;
      final fileName = DateFormat('MM-dd-yyyy_HH-mm').format(photo.date!);

      // Check if there is a file with the same name.
      while (await File('${newFolder.path}/Facelog_$fileName${howManySameName == 0 ? "" : "($howManySameName)"}.jpg').exists()) {
        howManySameName++;
      }

      await File(photo.path).copy('${newFolder.path}/Facelog_$fileName${howManySameName == 0 ? "" : "($howManySameName)"}.jpg');
    }

    LogService().exportPhotos(allPhotos.length);

    getMessage(
      message: LocaleKeys.PhotoEvents_ScaffoldMessage_AllPhotosDownloaded.tr(),
      icon: Icons.download,
      duration: const Duration(microseconds: 700),
    );
  }

  // Share file function.
  Future<void> shareItem(String path) async {
    await SharePlus.instance.share(
      ShareParams(
        files: [XFile(path)],
      ),
    );
  }

  Future<void> openFileManager() async {
    String? path;
    final documentsDirectory = await getApplicationDocumentsDirectory();

    if (Platform.isAndroid) {
      // Android path
      path = 'content://com.android.externalstorage.documents/document/primary%3ADownload%2FFacelog';
    } else if (Platform.isIOS) {
      // iOS path
      final facelogPath = '${documentsDirectory.path}/Facelog';

      // Ensure the Facelog directory exists
      final facelogDir = Directory(facelogPath);
      if (!await facelogDir.exists()) {
        await facelogDir.create(recursive: true);
      }

      // Use the Files app to open the directory on iOS
      path = 'shareddocuments://$facelogPath';
    } else {
      throw 'Unsupported platform';
    }

    final uri = Uri.parse(path);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      throw 'Could not open the file manager at $path';
    }
  }

  Future<void> importPhotoFinal({
    required BuildContext context,
  }) async {
    try {
      final photoProvider = context.read<PhotoProvider>();

      Directory directory = await getApplicationDocumentsDirectory();
      directory = Directory("${directory.path}/Photos");
      Directory(directory.path).createSync(recursive: true);

      // Process photos in batches to prevent memory issues
      const int batchSize = 2; // Process 2 photos at a time (reduced for quality preservation)
      final List<Photo> photosToImport = context.read<PhotoManagerProvider>().importedPhotos;

      for (int i = 0; i < photosToImport.length; i += batchSize) {
        final int endIndex = (i + batchSize < photosToImport.length) ? i + batchSize : photosToImport.length;
        final List<Photo> batch = photosToImport.sublist(i, endIndex);

        for (var currentPhoto in batch) {
          try {
            lastPhotoNumber++;

            // Verify source file exists before processing
            String sourceFilePath = currentPhoto.path;
            if (!await File(sourceFilePath).exists()) {
              debugPrint('Source file not found: $sourceFilePath');
              croppedPhotoIndexForLoading.value++;
              continue;
            }

            // eğer önceden aynı tarihte fotoğraf varsa ona ekle
            if (photoProvider.allPhotosList.isNotEmpty && (photoProvider.allPhotosList.any((element) => Helper().isSameDay(element.date, currentPhoto.date!)))) {
              await photoProvider.addPhotoInDay(
                photo: Photo(
                  date: currentPhoto.date,
                  path: "${directory.path}/$lastPhotoNumber.jpg",
                  isImported: true,
                  face: currentPhoto.face,
                ),
                dayNoteText: null,
                cacheImagePath: sourceFilePath, // Cache'den alınacak
                isImport: true,
              );
            }
            // yoksa yeni bir gün oluştur
            else {
              await photoProvider.addOnePhotoDay(
                oneDayPhoto: OneDayPhotos(
                  date: currentPhoto.date!,
                  photos: [
                    Photo(
                      date: currentPhoto.date,
                      path: "${directory.path}/$lastPhotoNumber.jpg",
                      isImported: true,
                      face: currentPhoto.face,
                    ),
                  ],
                ),
                cacheImagePath: currentPhoto.path,
                isImport: true,
              );
            }
          } catch (e) {
            debugPrint('Error importing photo: ${currentPhoto.path}, Error: $e');
          }

          croppedPhotoIndexForLoading.value++;
        }

        // Small delay between batches to allow garbage collection
        await Future.delayed(const Duration(milliseconds: 100));
      }

      SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.setInt('lastPhotoNumber', lastPhotoNumber);

      // Fotoğraf listesine tarihe göre sırala ve kaydet
      photoProvider.allPhotosList.sort((a, b) => a.date.compareTo(b.date));
      await PhotoService().updateList(photoProvider.allPhotosList);

      LogService().importPhotos(context.read<PhotoManagerProvider>().importedPhotos.length);

      // Clean up temporary directory
      Directory tempDirectory = Directory("${directory.path}/TempImportPhotos");
      if (await tempDirectory.exists()) {
        await tempDirectory.delete(recursive: true);
      }

      // TODO: burası sorun oluyor dialog açık ise
      Navigator.pop(context);
    } catch (_) {
      final errorMessage = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_Error.tr();
      getMessage(message: errorMessage);
    }
  }

  Future<void> getPhotosToImport({
    required BuildContext context,
    required bool isAddLater,
  }) async {
    late final List<XFile> selectedPhotos;

    late List<Photo> importPhotos = [];

    if (Platform.isAndroid) {
      if (!await storageAccessRequest()) return;
    }

    late final String? selectedFolder;

    // klasör seç
    selectedFolder = await FilePicker.platform.getDirectoryPath();

    if (selectedFolder == null) {
      return Helper().getMessage(
        message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AnyPhotoSelected.tr(),
        status: StatusEnum.WARNING,
      );
    }

    // Normalize the path to prevent duplication
    String normalizedPath = selectedFolder;
    // Check if the path already ends with /Download/Facelog or contains duplicate Facelog paths
    if (normalizedPath.contains('/Download/Facelog/Download/Facelog')) {
      // Remove the duplicate part
      normalizedPath = normalizedPath.replaceAll('/Download/Facelog/Download/Facelog', '/Download/Facelog');
    }
    // Also handle cases where there might be multiple consecutive /Facelog/ paths
    while (normalizedPath.contains('/Facelog/Facelog')) {
      normalizedPath = normalizedPath.replaceAll('/Facelog/Facelog', '/Facelog');
    }

    // Klasörün var olup olmadığını kontrol et
    final selectedDirectory = Directory(normalizedPath);
    if (!await selectedDirectory.exists()) {
      return Helper().getMessage(
        message: "Seçilen klasör bulunamadı: $normalizedPath",
        status: StatusEnum.WARNING,
      );
    }

    try {
      // sadece jpg ve jpeg olanaları ekle
      selectedPhotos = selectedDirectory.listSync().where((element) => element.path.endsWith('.jpg') || element.path.endsWith('.jpeg')).map((e) => XFile(e.path)).toList();
    } catch (e) {
      return Helper().getMessage(
        message: "Klasör okunamadı: $e",
        status: StatusEnum.WARNING,
      );
    }

    if (selectedPhotos.isNotEmpty) {
      //get selected image paths

      for (var i = 0; i < selectedPhotos.length; i++) {
        // * Date

        lastPhotoNumber++;

        importPhotos.add(
          Photo(
            date: await getFileDateOnFileName(selectedPhotos[i].path),
            path: selectedPhotos[i].path,
            face: null,
          ),
        );
      }

      context.read<PhotoManagerProvider>().addPhotos(importPhotos);

      if (!isAddLater) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PhotoManagePage(),
          ),
        ).then(
          (value) {
            context.read<PhotoManagerProvider>().selectedPhotos = [];
            context.read<PhotoManagerProvider>().importedPhotos = [];
          },
        );
      }
    } else {
      Helper().getMessage(message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AnyPhotoSelected.tr());
    }
  }

  Future<bool> storageAccessRequest() async {
    // android sürüm 33 den büyük ise photos izni alınmalı yoksa storage izni yeterli
    final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;
    if (androidDeviceInfo.version.sdkInt >= 33) {
      await Permission.photos.request();
      if (await Permission.photos.isGranted == false) {
        Helper().getMessage(
          message: LocaleKeys.General_AccesRequestWarning.tr(),
          status: StatusEnum.WARNING,
        );
        return false;
      }

      if (await Permission.photos.isGranted == false) {
        if (await Permission.photos.isPermanentlyDenied) {
          Helper().getDialog(
            message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AccesPhotosRequestWarning.tr(),
            onAccept: () async {
              await openAppSettings();
            },
          );
          return false;
        } else if (!await Permission.photos.isGranted) {
          Helper().getMessage(
            message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AccesPhotosRequestWarning.tr(),
            status: StatusEnum.WARNING,
          );
          return false;
        }
      }
    } else {
      await Permission.storage.request();

      if (await Permission.storage.isGranted == false) {
        if (await Permission.storage.isPermanentlyDenied) {
          Helper().getDialog(
            message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AccesStorageRequestWarning.tr(),
            onAccept: () async {
              await openAppSettings();
            },
          );
          return false;
        } else if (!await Permission.storage.isGranted) {
          Helper().getMessage(
            message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_AccesStorageRequestWarning.tr(),
            status: StatusEnum.WARNING,
          );
          return false;
        }
      }
    }

    return true;
  }

  Future<File> getImageFileFromAssets(String path) async {
    final byteData = await rootBundle.load(path);

    Directory appDocDir = await getApplicationDocumentsDirectory();
    String imagesAppDirectory = appDocDir.path;
    final file = await File('$imagesAppDirectory/$path').create(recursive: true);

    await file.writeAsBytes(byteData.buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));

    return file;
  }

  Future<void> initEntryCount(BuildContext context) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    InAppReview inAppReview = InAppReview.instance;

    int entryCount = prefs.getInt('entryCount') ?? 1;
    entryCount++;
    prefs.setInt('entryCount', entryCount);

    if ((entryCount % 15 == 0 || entryCount == 3 || entryCount == 6) && !isRated && await inAppReview.isAvailable()) {
      showRateUsBottomSheet(context);
    }
  }

  Future<void> cameraQueryAndRouter(BuildContext context) async {
    PermissionStatus camStatus = await Permission.camera.request();
    if (camStatus.isGranted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => const CameraPage(),
        ),
      );
    } else {
      await Helper().getDialog(
        message: LocaleKeys.Home_costumDialog.tr(),
        onAccept: () async {
          Navigator.pop(context);
          await openAppSettings();
        },
      );
    }
  }

  Future<void> loadExamplePhotos(BuildContext context) async {
    final manifestContent = await rootBundle.loadString('AssetManifest.json');

    final Map<String, dynamic> manifestMap = json.decode(manifestContent);

    //Path
    final photoPaths = manifestMap.keys.where((String key) => key.contains('testPhotos/')).where((String key) => key.contains('.jpg')).toList();

    //Name
    List<DateTime> randomImageDates = [];

    while (randomImageDates.length < photoPaths.length) {
      final DateTime randomDate = RandomDate.withRange(2022, 2023).random();
      randomImageDates.add(randomDate);
    }
    for (var i = 0; i < photoPaths.length; i++) {
      lastPhotoNumber++;

      Directory directory = await getApplicationDocumentsDirectory();
      directory = Directory("${directory.path}/Photos");
      Directory(directory.path).createSync(recursive: true);

      await Helper().getImageFileFromAssets(photoPaths[i]).then((file) async {
        await photoDetectFace(photoPath: photoPaths[i]);

        context.read<PhotoProvider>().addOnePhotoDay(
              oneDayPhoto: OneDayPhotos(
                date: randomImageDates[i],
                notes: i % 2 == 0
                    ? "$lastPhotoNumber  $i. fotoğrafın deneme olarak yazılmış açıklama yazısıdır.fotoğrafın deneme olarak yazılmış açıklama yazısıdır.fotoğrafın deneme olarak yazılmış açıklama yazısıdır.fotoğrafın deneme olarak yazılmış açıklama yazısıdır.fotoğrafın deneme olarak yazılmış açıklama yazısıdır."
                    : "Bu da kısa bir yazı",
                photos: [
                  Photo(
                    path: "${directory.path}/$lastPhotoNumber.jpg",
                    date: randomImageDates[i],
                    isFavorite: i % 2 == 0 ? true : false,
                    location: i % 2 == 0 ? "İstanbul" : null,
                    face: currentFace,
                  ),
                ],
              ),
              cacheImagePath: file.path,
            );
      });
    }

    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setInt('lastPhotoNumber', lastPhotoNumber);
  }

  Future<void> cancelApproveDialog(BuildContext context, bool isLoading) async {
    if (isLoading) {
      await Helper().getDialog(
          message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_CancelImportProgress.tr(),
          onAccept: () {
            Navigator.pop(context);
          });
    } else {
      // Clean up temporary directory if user cancels
      try {
        Directory directory = await getApplicationDocumentsDirectory();
        Directory tempDirectory = Directory("${directory.path}/TempImportPhotos");
        if (await tempDirectory.exists()) {
          await tempDirectory.delete(recursive: true);
        }
      } catch (e) {
        // Ignore cleanup errors
      }
      Navigator.pop(context);
    }
  }

  Color getColorForPercentage(double percentage) {
    // Yüzdeye göre kırmızıdan yeşile renk gradyanı oluşturun
    if (percentage >= 90) return Colors.green;
    if (percentage >= 80) return Colors.lightGreen;
    if (percentage >= 70) return Colors.orange;

    return Colors.red;
  }

  double calculateTwoPointDistance(Offset p1, Offset p2) {
    return sqrt(pow(p2.dx - p1.dx, 2) + pow(p2.dy - p1.dy, 2));
  }

  Future<DateTime?> getFileDateOnFileName(String photoPath) async {
    try {
      //  dosya ismini okuyarak almaya çalış
      return DateFormat('MM-dd-yyyy_HH-mm').parse(photoPath.substring(photoPath.indexOf(RegExp(r'\d{2}-\d{2}-\d{4}'))).substring(0, 16));
    } catch (_) {
      // metadata okuyarak almaya çalış
      final exif = await Exif.fromPath(photoPath);

      if (kDebugMode) {
        final attributes = await exif.getAttributes();
        debugPrint(attributes.toString());
      }

      if (await exif.getOriginalDate() != null) {
        return await exif.getOriginalDate();
      }
      await exif.close();
    }
    return null;
  }

  // Get Color Name selectableColors
  String getAppColorName() {
    //  main color ==  Color.fromARGB(255, 255, 105, 0) == orange

    if (AppColors.main == const Color.fromARGB(255, 24, 127, 218)) {
      return "blue";
    } else if (AppColors.main == const Color.fromARGB(255, 30, 184, 38)) {
      return "green";
    } else if (AppColors.main == const Color.fromARGB(255, 223, 33, 30)) {
      return "red";
    } else if (AppColors.main == const Color.fromARGB(255, 153, 31, 187)) {
      return "purple";
    } else {
      return "orange";
    }
  }

  Future<Null> mustPremiumDialog(BuildContext context) {
    bool isAccept = false;

    return getDialog(
      message: LocaleKeys.Dialog_MustPremium.tr(),
      acceptButtonText: LocaleKeys.PaymentPage_tryFreeTrials.tr(),
      onAccept: () {
        isAccept = true;

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PaymentPage(),
          ),
        );
      },
    ).then(
      (_) {
        LogService().mustPremiumDialog(isAccept);
      },
    );
  }

  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  }

  bool isBeforeDay(DateTime date1, DateTime date2) {
    return date1.year < date2.year || (date1.year == date2.year && date1.month < date2.month) || (date1.year == date2.year && date1.month == date2.month && date1.day < date2.day);
  }

  int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return (to.difference(from).inHours / 24).round();
  }
}

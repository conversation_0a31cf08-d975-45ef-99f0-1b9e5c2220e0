import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/supporter_model.dart';
import 'package:facelog/product/services/firebase/database/database_services.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SupportersAlertDialog extends StatefulWidget {
  const SupportersAlertDialog({super.key});

  @override
  State<SupportersAlertDialog> createState() => _SupportersAlertDialogState();
}

class _SupportersAlertDialogState extends State<SupportersAlertDialog> {
  @override
  void initState() {
    super.initState();

    LogService().logScreen("SupporterDIALOG");
  }

  final DatabaseService databaseService = DatabaseService();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(LocaleKeys.SettingsPage_Supporters_Thanking).tr(),
      content: SizedBox(
        height: 0.2.sh,
        width: 0.8.sw,
        child: FutureBuilder(
          future: databaseService.getSupporters(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              // İnternet bağlantını kontrol et.
              if (!isConnected) {
                return Center(child: const Text(LocaleKeys.SettingsPage_Supporters_InternetConnection).tr());
              }
              return Center(child: const Text(LocaleKeys.SettingsPage_Supporters_Error).tr());
            } else if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
              return Center(child: const Text(LocaleKeys.SettingsPage_Supporters_NoSupporters).tr());
            } else {
              List supporters = snapshot.data!.docs;
              return ListView.builder(
                itemCount: supporters.length,
                itemBuilder: (context, index) {
                  Supporter supporter = supporters[index].data();
                  return ListTile(
                    title: Text(supporter.name),
                    trailing: Text(
                      '\$${supporter.amount}',
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                  );
                },
              );
            }
          },
        ),
      ),
      actions: [
        GestureDetector(
          onTap: () {
            setState(() {});
          },
          child: Container(
            padding: const EdgeInsets.fromLTRB(20, 8, 20, 8),
            child: Icon(
              Icons.refresh,
              color: AppColors.main,
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text(LocaleKeys.SettingsPage_Supporters_Closing).tr(),
        ),
      ],
    );
  }
}

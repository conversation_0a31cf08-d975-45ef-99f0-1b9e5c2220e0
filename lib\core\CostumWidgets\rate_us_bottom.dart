import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:mailto/mailto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class RateUsBottomSheet extends StatefulWidget {
  const RateUsBottomSheet({
    super.key,
  });

  @override
  State<RateUsBottomSheet> createState() => _RateUsBottomSheetState();
}

class _RateUsBottomSheetState extends State<RateUsBottomSheet> {
  InAppReview inAppReview = InAppReview.instance;

  late bool isLaterActive;
  Timer? timer;

  @override
  void initState() {
    super.initState();
    isLaterActiveFunc();
  }

  @override
  void dispose() {
    timer?.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      alignment: Alignment.bottomCenter,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 10),
              const Text(
                LocaleKeys.SettingsPage_SettingsTiles_RateUsBottomSheet_Header,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ).tr(),
              const SizedBox(height: 10),
              Center(
                child: RatingBar.builder(
                  direction: Axis.horizontal,
                  unratedColor: AppColors.white,
                  itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                  initialRating: 0,
                  minRating: 1,
                  itemCount: 5,
                  itemSize: 40,
                  tapOnlyMode: false,
                  allowHalfRating: false,
                  glow: false,
                  itemBuilder: (context, _) => Icon(
                    Icons.star,
                    color: AppColors.main,
                  ),
                  onRatingUpdate: (rating) async {
                    LogService().rateUsBottomSheet(rating);

                    if (rating >= 4) {
                      SharedPreferences prefs = await SharedPreferences.getInstance();

                      if (await inAppReview.isAvailable()) {
                        await inAppReview.requestReview();
                        isRated = true;
                        prefs.setBool('isRated', true);
                        debugPrint('Kullanıcı uygulamayı değerlendirdi olarak işaretlendi.');
                      } else {
                        await Helper().getDialog(
                          message: LocaleKeys.RateUsDialogWarnings_RateUsInternetConnectionFail.tr(),
                        );
                      }
                      Navigator.of(context).pop();
                    } else {
                      Navigator.of(context).pop();
                      await Helper().getDialog(
                        message: LocaleKeys.RateUsDialogWarnings_message_Dictation.tr(),
                        onAccept: () async {
                          final mailtoLink = Mailto(
                            to: ['<EMAIL>'],
                            subject: LocaleKeys.RateUsDialogWarnings_message_Subject.tr(),
                            body: LocaleKeys.RateUsDialogWarnings_message_Body.tr(),
                          );
                          // ignore: deprecated_member_use
                          await launch('$mailtoLink');
                        },
                      );
                    }
                  },
                ),
              ),
              const SizedBox(height: 10),
              TextButton(
                onPressed: () {
                  if (!isLaterActive) return;
                  SharedPreferences.getInstance().then((prefs) {
                    prefs.setBool('isRated', false);
                  });
                  Navigator.of(context).pop();
                },
                child: Text(
                  LocaleKeys.SettingsPage_SettingsTiles_RateUsBottomSheet_LaterButton,
                  style: TextStyle(
                    fontSize: 16,
                    color: isLaterActive ? AppColors.main : AppColors.grey,
                  ),
                  textAlign: TextAlign.center,
                ).tr(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void isLaterActiveFunc() {
    isLaterActive = false;
    Timer.periodic(const Duration(seconds: 3), (timer) {
      if (mounted) {
        setState(() {
          isLaterActive = true;
        });
      }
    });
  }
}

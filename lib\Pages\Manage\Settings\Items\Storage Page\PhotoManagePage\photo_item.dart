import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/PhotoManagePage/photo_manager_provider.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PhotoItem extends StatefulWidget {
  const PhotoItem({
    super.key,
    required this.photo,
  });

  final Photo photo;

  @override
  State<PhotoItem> createState() => _PhotoItemState();
}

class _PhotoItemState extends State<PhotoItem> {
  // Texts
  final String _noDate = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_DateNotFound.tr();

  late final photoManagerProviderRead = context.read<PhotoManagerProvider>();

  Widget _getAnalysisStatusWidget() {
    final photoManagerProvider = context.watch<PhotoManagerProvider>();

    // Şu anda bu fotoğraf analiz ediliyor mu?
    final isCurrentlyAnalyzing = photoManagerProvider.currentlyAnalyzingPhoto == widget.photo;

    if (isCurrentlyAnalyzing) {
      return const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
            ),
          ),
          SizedBox(width: 4),
          Text(
            'Analiz...',
            style: TextStyle(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ],
      );
    }

    // Eğer totalAccuracy null ise, henüz analiz edilmemiş demektir
    if (widget.photo.totalAccuracy == null) {
      return const SizedBox.shrink(); // Hiçbir şey gösterme
    }

    // Analiz edilmiş, sonucu göster
    if (widget.photo.face == null) {
      return Text(
        LocaleKeys.CameraPages_Camera_FaceDetection_FaceNotDetected.tr(),
        style: const TextStyle(
          color: AppColors.red,
          fontWeight: FontWeight.bold,
          fontSize: 11,
        ),
      );
    } else {
      return Text(
        '%${widget.photo.totalAccuracy!.toStringAsFixed(0)}',
        style: TextStyle(
          color: Helper().getColorForPercentage(widget.photo.totalAccuracy!),
          fontWeight: FontWeight.bold,
          fontSize: 15,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Texts
    late final String photoDate = DateFormat('dd/MM/yyyy').format(
      widget.photo.date!,
    );

    late final bool isSelected = context.watch<PhotoManagerProvider>().selectedPhotos.contains(widget.photo);

    return GestureDetector(
      onTap: () {
        if (photoManagerProviderRead.selectedPhotos.isNotEmpty) {
          photoManagerProviderRead.selectPhoto(widget.photo);
        } else {
          showDatePicker(
            context: context,
            initialDate: DateTime.now(),
            firstDate: DateTime(1950),
            lastDate: DateTime.now(),
          ).then((date) {
            if (date != null) {
              setState(() {
                widget.photo.date = date;
              });
            }
          });
        }
      },
      onLongPress: () {
        photoManagerProviderRead.selectPhoto(widget.photo);
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.main : Colors.transparent,
            width: isSelected ? 2 : 0,
          ),
          borderRadius: AppColors.borderRadiusAll,
          color: AppColors.panelBackground,
        ),
        margin: const EdgeInsets.all(2),
        child: Column(
          children: [
            Stack(
              children: [
                ClipRRect(
                  borderRadius: AppColors.borderRadiusTop / 1.5,
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: Image.file(
                      File(widget.photo.path),
                      fit: BoxFit.cover,
                      cacheHeight: 600,
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  top: 0,
                  child: GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (_) => Material(
                          type: MaterialType.transparency,
                          child: GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Center(
                              child: ClipRRect(
                                borderRadius: AppColors.borderRadiusAll,
                                child: SizedBox(
                                  width: MediaQuery.of(context).size.width * 0.85,
                                  child: Image.file(
                                    File(widget.photo.path),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      color: AppColors.transparent,
                      child: const Icon(
                        Icons.fullscreen,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ),
                // Analiz durumu göstergesi
                Positioned(
                  left: 3,
                  top: 3,
                  child: Container(
                    padding: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                      borderRadius: AppColors.borderRadiusAll,
                      color: AppColors.black.withValues(alpha: 0.8),
                    ),
                    child: _getAnalysisStatusWidget(),
                  ),
                ),
              ],
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  widget.photo.date != null ? photoDate : _noDate,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text.withValues(alpha: 0.8),
                  ),
                ),
                if (widget.photo.date == null) ...[
                  const SizedBox(width: 5),
                  const Icon(
                    Icons.error,
                    size: 20,
                    color: AppColors.red,
                  )
                ]
              ],
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}

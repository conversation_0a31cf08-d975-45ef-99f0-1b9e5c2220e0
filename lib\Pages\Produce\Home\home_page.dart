import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/Videos/video_list_view.dart';
import 'package:facelog/Pages/Manage/Payment/payment_page.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/streak_widget.dart';
import 'package:facelog/Pages/Produce/Home/Widgets/total_photo_count.dart';
import 'package:facelog/Pages/Produce/Home/PhotoFix/photo_fix_controller.dart';
import 'package:facelog/Pages/Produce/Home/PhotoFix/photo_fix_widgets.dart';
import 'package:facelog/Pages/Produce/Home/Debug/debug_widgets.dart';
import 'package:facelog/Pages/Produce/Home/Debug/debug_service.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/Pages/Produce/Video/core/video_variables.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/Pages/Produce/Home/Widgets/photo_slider.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/Pages/Manage/Settings/Main/settings_view.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // Texts
  final String _tryFreePremium = LocaleKeys.Home_FreePremium_ButtonText.tr();
  final String _tryFreeDialogMessage = LocaleKeys.Home_FreePremium_DialogMessage.tr();
  final String _tryFreeDialogButton = LocaleKeys.Home_FreePremium_DialogButtonText.tr();
  final String _tryFreeDialogTitle = LocaleKeys.Home_FreePremium_Congratulations.tr();
  final String _cannotVerified = LocaleKeys.PremiumVerificationError.tr();

  late final PhotoProvider photoProvider = context.read<PhotoProvider>();
  late final PhotoFixController photoFixController = PhotoFixController(photoProvider: photoProvider);
  int selectedTab = 1;
  late bool hasPremiumAlert;

  bool isLoadingPhotos = false;
  bool isFixingPhotos = false;
  bool showFixButton = false;

  // Progress tracking için yeni değişkenler
  int currentFixingPhoto = 0;
  int totalFixingPhotos = 0;

  bool testIsLoading = false;

  @override
  void initState() {
    super.initState();
    Helper().initEntryCount(context);
    premiumAlert();
    hasPremiumAlert = false;

    photoSize = isLandscape ? 0.58.sw : 0.7.sh;

    LogService().logScreen("HomePage");

    // Kırpılmamış fotoğraf kontrolü
    _checkForUncroppedPhotos();
  }

  void _checkForUncroppedPhotos() {
    showFixButton = photoFixController.hasUncroppedPhotos();
    if (mounted) setState(() {});
  }

  Future<void> _handleFixPhotos() async {
    setState(() {
      isFixingPhotos = true;
      currentFixingPhoto = 0;
      totalFixingPhotos = 0;
    });

    final success = await photoFixController.fixUncroppedPhotos(
      onProgress: (current, total) {
        setState(() {
          currentFixingPhoto = current;
          totalFixingPhotos = total;
        });
      },
    );

    // İşlem tamamlandı - UI'ı güncelle
    setState(() {
      showFixButton = success ? false : photoFixController.hasUncroppedPhotos();
      isFixingPhotos = false;
      currentFixingPhoto = 0;
      totalFixingPhotos = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: appbarWidget(),
        body: isLoadingPhotos ? const Center(child: CircularProgressIndicator()) : body(),
        floatingActionButton: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              children: [
                if (!isFreeTrailUsed) ...[
                  Container(
                    margin: const EdgeInsets.only(left: 40),
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                      borderRadius: AppColors.borderRadiusAll,
                      color: AppColors.white,
                    ),
                    child: TextButton(
                      onPressed: () async {
                        await Helper()
                            .getDialog(
                          title: _tryFreeDialogTitle,
                          message: _tryFreeDialogMessage,
                          acceptButtonText: _tryFreeDialogButton,
                          onAccept: () async {
                            isFreeTrailUsed = true;
                            isPremium = true;

                            final SharedPreferences prefs = await SharedPreferences.getInstance();
                            final String purchaseDate = (DateTime.now().add(const Duration(days: 15))).toIso8601String();

                            prefs.setBool('isFreeTrailUsed', isFreeTrailUsed);
                            await prefs.setString('purchaseDate', purchaseDate);
                            await prefs.setBool('isPremium', isPremium!);
                            setState(() {});
                          },
                        )
                            .then((_) {
                          LogService().tryFreePremiumDialog();
                        });
                      },
                      child: Text(
                        _tryFreePremium,
                        style: const TextStyle(color: AppColors.black),
                      ),
                    ),
                  ),
                ],
                const Spacer(),
                SizedBox(
                  height: 64,
                  width: 64,
                  child: FloatingActionButton(
                    heroTag: "camera",
                    onPressed: () async {
                      if (kDebugMode) return await Helper().cameraQueryAndRouter(context);

                      // ignore: dead_code
                      HapticFeedback.vibrate();

                      if (photoProvider.allPhotosList.isNotEmpty && Helper().isSameDay(photoProvider.allPhotosList.last.date, DateTime.now())) {
                        if (isPremium! || photoProvider.allPhotosList.length < 10) {
                          // Kullanıcı premium ise, günde 5 fotoğraf çekebilir
                          if (photoProvider.allPhotosList.last.photos.length < 5) {
                            await Helper().cameraQueryAndRouter(context);
                          } else {
                            await Helper().getDialog(
                              message: LocaleKeys.CameraPages_AlreadyTook.tr(),
                            );
                          }
                        } else {
                          // Kullanıcı premium değilse, günde 1 fotoğraf çekebilir
                          if (photoProvider.allPhotosList.last.photos.isNotEmpty) {
                            await Helper().getDialog(
                              message: LocaleKeys.Dialog_TakeMorePhotoPremium.tr(),
                              acceptButtonText: LocaleKeys.PaymentPage_tryFreeTrials.tr(),
                              onAccept: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const PaymentPage(),
                                  ),
                                );
                              },
                            );
                          } else {
                            await Helper().cameraQueryAndRouter(context);
                          }
                        }
                      } else {
                        // Yeni bir gün veya fotoğraf listesi boş ise, fotoğraf çekme işlemini başlat
                        await Helper().cameraQueryAndRouter(context);
                      }
                    },
                    child: const Icon(
                      Icons.photo_camera,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 200.h,
            ),
          ],
        ),
      ),
    );
  }

  Widget body() {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: 20.h),
          const PhotoSlider(),
          const TotalPhotoCount(),
          if (showFixButton && !isFixingPhotos) ...[
            SizedBox(height: 20.h),
            PhotoFixWidgets.buildFixPhotosButton(
              onPressed: () => _handleFixPhotos(),
            ),
          ],
          if (isFixingPhotos) ...[
            SizedBox(height: 20.h),
            PhotoFixWidgets.buildFixingPhotosIndicator(
              currentFixingPhoto: currentFixingPhoto,
              totalFixingPhotos: totalFixingPhotos,
            ),
          ],
          if (kDebugMode)
            DebugWidgets.buildDebugButtons(
              context: context,
              photoProvider: photoProvider,
              isLoadingPhotos: isLoadingPhotos,
              testIsLoading: testIsLoading,
              setLoadingPhotos: (loading) => setState(() => isLoadingPhotos = loading),
              setTestIsLoading: (loading) => setState(() => testIsLoading = loading),
              onImportUncroppedPhotos: () => DebugService.importUncroppedPhotos(
                photoProvider: photoProvider,
                setLoadingPhotos: (loading) => setState(() => isLoadingPhotos = loading),
                onCheckForUncroppedPhotos: _checkForUncroppedPhotos,
              ),
              onTestRender: ({required qualityIndex, required videoLength}) => DebugService.testRenderFunction(
                context: context,
                qualityIndex: qualityIndex,
                videoLength: videoLength,
                onLoadingStart: () => setState(() => isVideoExportProgressLoading = true),
                onLoadingEnd: () => setState(() => isVideoExportProgressLoading = false),
              ),
              getQualityIndex: () => qualityList[activeQualityIndex],
              getVideoLength: () => videoLengthInputController.text.isNotEmpty ? videoLengthInputController.text : videoSpeedList[activeSpeedIndex].toString(),
            ),
          if (!kDebugMode) SizedBox(height: 500.h),
          const StreakWidget(),
        ],
      ),
    );
  }

  AppBar appbarWidget() {
    return AppBar(
      leading: kDebugMode
          ? DebugWidgets.buildPremiumDebugButton(
              isPremium: isPremium!,
              onPremiumChanged: (newStatus) async {
                setState(() => isPremium = newStatus);
                await DebugService.togglePremiumStatus(newStatus);
              },
            )
          : null,
      actions: <Widget>[
        if (hasPremiumAlert) ...[
          GestureDetector(
            onTap: () {
              Helper().getDialog(message: _cannotVerified);
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              color: AppColors.transparent,
              child: const Icon(
                Icons.notifications,
                color: AppColors.red,
                size: 24,
              ),
            ),
          ),
        ],
        GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) => const VideoListView(),
                transitionsBuilder: (context, animation, secondaryAnimation, child) {
                  const begin = Offset(0.0, 1.0);
                  const end = Offset.zero;
                  const curve = Curves.fastOutSlowIn;

                  var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

                  return SlideTransition(
                    position: animation.drive(tween),
                    child: child,
                  );
                },
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            color: AppColors.transparent,
            child: Icon(
              Icons.video_collection_outlined,
              color: AppColors.main,
              size: 24,
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            LogService().logScreen("SettingsPage");

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SettingsPage(),
              ),
            ).then(
              (_) {
                setState(() {});
              },
            );
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            color: AppColors.transparent,
            child: const Icon(
              Icons.menu,
              size: 28,
            ),
          ),
        ),
      ],
      automaticallyImplyLeading: false,
      title: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const PaymentPage()),
          );
        },
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'Facelog',
                style: TextStyle(
                  color: AppColors.text,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (isPremium!)
                TextSpan(
                  text: ' PRO',
                  style: TextStyle(
                    color: AppColors.main,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void premiumAlert() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    hasPremiumAlert = prefs.getBool('premiumAlert') ?? false;
  } // Hemen initialize olmuyor.
}

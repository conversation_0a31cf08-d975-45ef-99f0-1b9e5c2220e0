import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/streak_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GetFreezeRight extends StatefulWidget {
  const GetFreezeRight({
    super.key,
  });

  @override
  State<GetFreezeRight> createState() => _GetFreezeRightState();
}

class _GetFreezeRightState extends State<GetFreezeRight> {
  // Texts
  final String _earnFreeze = LocaleKeys.Home_Streak_EarnFreeze.tr();

  final int streakCount = StreakHelper().calculateStreak();

  late final bool isTargetStreak = (streakCount == 1 || streakCount == 5 || streakCount == 10 || (isPremium! && streakCount % 5 == 0) || streakCount % 10 == 0);

  @override
  void initState() {
    super.initState();

    if (isTargetStreak) {
      freezeCount++;

      SharedPreferences.getInstance().then((prefs) {
        prefs.setInt("freezeCount", freezeCount);
      });

      LogService().getFreezeRight();
    }
  }

  @override
  Widget build(BuildContext context) {
    return isTargetStreak
        ? Text(
            _earnFreeze,
            style: TextStyle(
              fontSize: 40.sp,
            ),
          )
        : const SizedBox.shrink();
  }
}

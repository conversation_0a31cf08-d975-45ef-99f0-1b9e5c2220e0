import 'dart:io';
import 'package:flutter/foundation.dart';

mixin MemoryManagementMixin {
  /// Checks available memory and returns true if safe to continue processing
  Future<bool> isMemorySafeForProcessing() async {
    try {
      // On Android, we can check available memory
      if (Platform.isAndroid) {
        final ProcessResult result = await Process.run('cat', ['/proc/meminfo']);
        final String memInfo = result.stdout.toString();

        // Extract available memory (simplified)
        final RegExp availableRegex = RegExp(r'MemAvailable:\s+(\d+)\s+kB');
        final match = availableRegex.firstMatch(memInfo);

        if (match != null) {
          final int availableKB = int.parse(match.group(1)!);
          final int availableMB = availableKB ~/ 1024;

          // Return false if less than 200MB available
          return availableMB > 200;
        }
      }

      // For iOS or if we can't determine memory, assume it's safe
      return true;
    } catch (e) {
      debugPrint('Error checking memory: $e');
      return true; // Default to safe if we can't check
    }
  }

  /// Forces garbage collection (best effort)
  void forceGarbageCollection() {
    // Small delay to allow GC to work
    Future.delayed(const Duration(milliseconds: 50));
  }

  /// Calculate optimal batch size based on available memory
  int calculateOptimalBatchSize({
    int defaultBatchSize = 2, // Kalite korunması için varsayılan değer düşürüldü
    int maxBatchSize = 3, // Maksimum değer de düşürüldü
    int minBatchSize = 1,
  }) {
    // This is a simplified calculation
    // In a real implementation, you might want to check actual memory usage
    return defaultBatchSize;
  }
}

import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/PhotoManagePage/photo_manager_provider.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/Pages/Manage/Settings/Items/TutorialPage/tutorial_page.dart';
import 'package:facelog/product/services/notification_services.dart';
import 'package:facelog/product/state/Provider/navbar_provider.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/config/init_app.dart';
import 'package:facelog/product/config/language/product_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'product/state/Provider/theme_provider.dart';

bool isResponsiveTestOn = false;

void main() async {
  await initApp();

  final prefs = await SharedPreferences.getInstance();

  runApp(
    MultiProvider(
      providers: [
        // Providers
        ChangeNotifierProvider(create: (context) => NavbarProvider()),
        ChangeNotifierProvider(create: (context) => PhotoProvider()),
        ChangeNotifierProvider(create: (context) => ThemeProvider(prefs)),
        // TODO: bu providerın burada durmasına yok. kullanılacak sayfada ayarlanmalı
        ChangeNotifierProvider(create: (context) => PhotoManagerProvider()),
      ],
      child:
          // isResponsiveTestOn
          //     ? DevicePreview(
          //         enabled: !kReleaseMode,
          //         builder: (context) => ProductLocalization(
          //           child: FacelogMain(
          //             isFirstLogin: isFirstLogin,
          //           ),
          //         ),
          //       )
          //     :
          ProductLocalization(
        child: FacelogMain(
          isFirstLogin: isFirstLogin,
        ),
      ),
    ),
  );
}

class FacelogMain extends StatefulWidget {
  final bool isFirstLogin;

  const FacelogMain({
    super.key,
    this.isFirstLogin = true,
  });

  @override
  State<FacelogMain> createState() => _FacelogMainState();
}

class _FacelogMainState extends State<FacelogMain> {
  @override
  void initState() {
    super.initState();

    // Theme
    AppColors.isDark = context.read<ThemeProvider>().themeMode == ThemeMode.dark;

    futureFunctions();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1080, 2400),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          localizationsDelegates: context.localizationDelegates,
          supportedLocales: context.supportedLocales,
          locale: context.locale,
          title: 'Facelog',
          theme: AppColors().appTheme,
          darkTheme: AppColors().appTheme,
          themeMode: context.watch<ThemeProvider>().themeMode,
          debugShowCheckedModeBanner: true,
          showPerformanceOverlay: false,
          home: widget.isFirstLogin ? const TutorialPage() : const NavBarAndPages(isFirst: true),
          // builder: isResponsiveTestOn ? DevicePreview.appBuilder : null,
        );
      },
    );
  }

  // TODO: cache de birikenleri kontrol et. işlemleri buna grek kalmayacak şekilde yapmak daha iyi olur.
  void futureFunctions() async {
    final Directory cacheDir = await getApplicationCacheDirectory();
    Directory(cacheDir.path).deleteSync(recursive: true);

    await NotificationServices().checkNotificationStatus(context);
  }
}

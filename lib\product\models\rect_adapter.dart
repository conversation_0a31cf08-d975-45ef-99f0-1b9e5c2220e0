import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'rect_adapter.g.dart';

@HiveType(typeId: 6)
class RectAdapter extends TypeAdapter<Rect> {
  @override
  final typeId = 6;

  @override
  Rect read(BinaryReader reader) {
    final left = reader.readDouble();
    final top = reader.readDouble();
    final right = reader.readDouble();
    final bottom = reader.readDouble();
    return Rect.fromLTRB(left, top, right, bottom);
  }

  @override
  void write(BinaryWriter writer, Rect obj) {
    writer.writeDouble(obj.left);
    writer.writeDouble(obj.top);
    writer.writeDouble(obj.right);
    writer.writeDouble(obj.bottom);
  }
}

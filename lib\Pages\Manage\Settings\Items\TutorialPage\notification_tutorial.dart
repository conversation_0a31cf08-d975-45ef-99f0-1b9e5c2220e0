import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Main/components/notification_widget.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificaitonTutorialPage extends StatelessWidget {
  NotificaitonTutorialPage({super.key});

  final String _notificationDescription = LocaleKeys.TutorialPage_NotificationTutorialDescription.tr();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const NotificationWidget(
                isTutorial: true,
              ),
              SizedBox(height: 0.1.sh),
              AutoSizeText(
                _notificationDescription,
                style: const TextStyle(
                  color: AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

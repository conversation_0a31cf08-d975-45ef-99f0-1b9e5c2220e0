import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'photo_manage_page.dart';

final class PhotoAnalysisLoadingWidget extends StatefulWidget {
  const PhotoAnalysisLoadingWidget({
    super.key,
    this.onCancel,
  });

  final VoidCallback? onCancel;

  @override
  State<PhotoAnalysisLoadingWidget> createState() => _PhotoAnalysisLoadingWidgetState();
}

class _PhotoAnalysisLoadingWidgetState extends State<PhotoAnalysisLoadingWidget> {
  @override
  void initState() {
    super.initState();
    photoAnalysisProgressIndex.addListener(_progressListener);
  }

  @override
  void dispose() {
    photoAnalysisProgressIndex.removeListener(_progressListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ModalBarrier(
          color: AppColors.black.withValues(alpha: 0.5),
          dismissible: false,
        ),
        Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                value: photoAnalysisProgressIndex.value / photoAnalysisAllPhotosCount,
                backgroundColor: AppColors.white,
                color: AppColors.main,
              ),
              const SizedBox(height: 20),
              Text(
                '${photoAnalysisProgressIndex.value} / $photoAnalysisAllPhotosCount işleniyor',
                style: const TextStyle(
                  color: AppColors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Fotoğraf analizi devam ediyor...',
                style: const TextStyle(
                  color: AppColors.white,
                  fontSize: 14,
                ),
              ),
              if (widget.onCancel != null) ...[
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: widget.onCancel,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.red,
                    foregroundColor: AppColors.white,
                  ),
                  child: const Text('İptal Et'),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  void _progressListener() {
    if (mounted) {
      setState(() {});
    }
  }
}

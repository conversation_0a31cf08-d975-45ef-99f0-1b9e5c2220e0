import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'facecontourtype_adapter.g.dart';

@HiveType(typeId: 10)
class FaceContourTypeAdapter extends TypeAdapter<FaceContourType> {
  @override
  final typeId = 10;

  @override
  FaceContourType read(BinaryReader reader) {
    final index = reader.readInt();
    return FaceContourType.values[index];
  }

  @override
  void write(BinaryWriter writer, FaceContourType obj) {
    writer.writeInt(obj.index);
  }
}

import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_statistics_container.dart';
// ignore: unused_import
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/photo/photo_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';

class PhotoFixService {
  final PhotoProvider photoProvider;

  PhotoFixService({required this.photoProvider});

  /// Kullanıcının kırpılmamış fotoğrafı olup olmadığını kontrol eder
  bool hasUncroppedPhotos() {
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        // isCropped null ise veya false ise, bu fotoğraf kırpılmamış demektir
        if (photo.isCropped == null || photo.isCropped == false) {
          return true;
        }
      }
    }
    return false;
  }

  /// Sadece kırpılmamış fotoğrafları düzelten fonksiyon (manuel çağırım için)
  Future<bool> fixUncroppedPhotosManually({
    Function(int current, int total)? onProgress,
  }) async {
    if (!hasUncroppedPhotos()) {
      return false; // Düzeltilecek fotoğraf yok
    }

    // Önce toplam kırpılmamış fotoğraf sayısını hesapla
    int totalUncroppedPhotos = 0;
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        if (photo.isCropped == null || photo.isCropped == false) {
          totalUncroppedPhotos++;
        }
      }
    }

    int processedCount = 0;

    // Kırpılmamış fotoğrafları tespit et ve kırp
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        // isCropped null ise veya false ise, bu fotoğrafı kırp
        if (photo.isCropped == null || photo.isCropped == false) {
          processedCount++;

          // Progress callback'i çağır
          onProgress?.call(processedCount, totalUncroppedPhotos);

          try {
            // Fotoğrafı yüz tespit fonksiyonu ile işle
            await photoDetectFace(photoPath: photo.path);

            // Eğer yüz tespit edildi ve kırpıldıysa
            if (currentFace != null) {
              photo.face = currentFace;
              photo.totalAccuracy = totalAccuracy;
              photo.isCropped = true;

              // Fotoğrafı Hive'a kaydet
              await photo.save();

              debugPrint("Fotoğraf kırpıldı: ${photo.path}");
            } else {
              // Yüz tespit edilemeyen fotoğrafı olduğu gibi bırak (silme)
              // Sadece isCropped = true olarak işaretle ki tekrar işlenmesin
              photo.isCropped = true;
              await photo.save();
              debugPrint("Yüz tespit edilemeyen fotoğraf işaretlendi: ${photo.path}");
            }
          } catch (e) {
            // Debug log için önce hata mesajını yazdır
            debugPrint("Manuel fix - Fotoğraf işlenirken hata: ${photo.path} - $e");

            // Firebase Analytics için güvenli log
            try {
              LogService().logError("Manual photo fix failed: ${photo.path} - ${e.toString()}");
            } catch (logError) {
              debugPrint("Log service error: $logError");
            }

            // Hata durumunda fotoğrafı olduğu gibi bırak
            // Sadece isCropped = true olarak işaretle ki tekrar işlenmesin
            photo.isCropped = true;
            try {
              await photo.save();
            } catch (saveError) {
              debugPrint("Photo save error: $saveError");
            }
            debugPrint("Hatalı fotoğraf işaretlendi: ${photo.path}");
          }
        }
      }
    }

    debugPrint("Manuel fotoğraf kırpma işlemi tamamlandı");

    // Fotoğraf listesini güncelle
    await PhotoService().updateList(photoProvider.allPhotosList);
    // UI'ı güncelle - tüm fotoğrafları yeniden yükle
    await photoProvider.getAllPhotosInProvider();

    return true; // İşlem başarılı
  }
}

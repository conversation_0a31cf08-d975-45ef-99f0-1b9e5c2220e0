import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/photo/photo_service.dart';
import 'package:flutter/material.dart';

class PhotoProvider with ChangeNotifier {
  List<OneDayPhotos> allPhotosList = [];
  PhotoService photoService = PhotoService();

  // TODO: başlanıçta tüm fotoğraflarınl istesini ve fotoğraf sayısısnı buraya değişkeno lrak da kaydetsek iyi olur mu
  // late final List<Photo> allPhotos = context.read<PhotoProvider>().oneDayPhotosList.map((e) => e.photos).expand((element) => element).toList();

  // Functions ------------------------------
  // Get All
  Future<void> getAllPhotosInProvider() async {
    await photoService.getAllPhotosInHive().then(
      (value) {
        allPhotosList = value;

        notifyListeners();
      },
    );
  }

  // Delete All
  Future<void> deletePhotoList({bool isImported = false}) async {
    if (allPhotosList.isEmpty) return;

    if (isImported) {
      late final List<Photo> allPhotos = allPhotosList.map((e) => e.photos).expand((element) => element).toList();
      final List<Photo> allImportedPhotos = allPhotos.where((element) => element.isImported!).toList();

      if (allImportedPhotos.isEmpty) return;

      for (final photo in allImportedPhotos) {
        await deletePhoto(photo: photo);
      }
    } else {
      await photoService.deleteAllPhotos();

      int endIndex = allPhotosList.first.photos.first.path.indexOf("/Photos") + "/Photos".length;
      String basePath = allPhotosList.first.photos.first.path.substring(0, endIndex);

      Directory(basePath).deleteSync(recursive: true);

      allPhotosList = [];
      currentFace = null;

      // TODO: kafa hizalama göze göre olduğunda bunu kontrol et
      // referanceFace = null;
      // await FaceDetailService().deleteReferanceFace();
    }

    LogService().deleteAllPhotos(isImported: isImported);

    notifyListeners();
  }

  // Add One Day
  Future<void> addOnePhotoDay({
    required OneDayPhotos oneDayPhoto,
    required String cacheImagePath,
    bool isImport = false,
  }) async {
    File(cacheImagePath).copy(oneDayPhoto.photos.first.path);

    // if it is imported, doesnt delete the original file
    if (!isImport) {
      await File(cacheImagePath).delete();
    }
    await photoService.addOnePhotoDay(oneDayPhoto);
    allPhotosList.add(oneDayPhoto);

    notifyListeners();
  }

  // Add Photo in Day
  Future<void> addPhotoInDay({
    required Photo photo,
    required String cacheImagePath,
    bool isImport = false,
    required String? dayNoteText,
  }) async {
    await File(cacheImagePath).copy(photo.path);

    // if it is imported, doesnt delete the original file
    if (!isImport) {
      await File(cacheImagePath).delete();
    }

    // fotoğrafın günü ile aynı oneDayPhoto u getir
    final OneDayPhotos oneDayPhoto = allPhotosList.firstWhere((element) => (Helper().isSameDay(element.date, photo.date!)));

    oneDayPhoto.photos.add(photo);
    if (dayNoteText != null) {
      oneDayPhoto.notes = dayNoteText;
    }
    oneDayPhoto.save();

    notifyListeners();
  }

  // Delete
  Future<void> deletePhoto({
    required Photo photo,
  }) async {
    // listedeki seçili fotoğrafı sil. eğer liste boş ise oneday i de sil
    final OneDayPhotos currentPhotoOneDay = allPhotosList.firstWhere((element) => element.photos.contains(photo));

    if (currentPhotoOneDay.photos.length == 1) {
      // Hive den sil
      currentPhotoOneDay.delete();
      // Listeden günü sil
      allPhotosList.remove(currentPhotoOneDay);
    } else {
      // Listeden Fotoğrafı sil
      currentPhotoOneDay.photos.remove(photo);
      // allPhotosList güncelle
      allPhotosList[allPhotosList.indexWhere((element) => element.date == currentPhotoOneDay.date)] = currentPhotoOneDay;

      // Hive güncelle
      currentPhotoOneDay.save();
    }

    // eğer sonuncu fotoğrafı siliyorsa
    if (allPhotosList.isEmpty) {
      // TODO: kafa hizalama göze göre olduğunda bunu kontrol et
      // referanceFace = null;
      // await FaceDetailService().deleteReferanceFace();
      currentFace = null;
    }

    // Dosyayı sil
    await File(photo.path).delete();

    notifyListeners();
  }

  // Change Favorite State
  void photoChanceFavoriteStates({
    required BuildContext context,
    required OneDayPhotos oneDayPhotos,
    required int selectedPhotoIndex,
    required AnimationController likeAnimationController,
    bool isFromUndo = false,
  }) {
    oneDayPhotos.photos[selectedPhotoIndex].isFavorite = !oneDayPhotos.photos[selectedPhotoIndex].isFavorite;

    if (oneDayPhotos.photos[selectedPhotoIndex].isFavorite) {
      LogService().likePhoto();
    } else {
      LogService().unlikePhoto();
    }

    oneDayPhotos.save();

    // isFromUndo ise snackbar gösterme
    if (oneDayPhotos.photos[selectedPhotoIndex].isFavorite && !isFromUndo) {
      likeAnimationController.forward();
      Future.delayed(const Duration(milliseconds: 600), () {
        likeAnimationController.reverse();
      });
    }

    isFromUndo
        ? null
        : Helper().getMessage(
            message: oneDayPhotos.photos[selectedPhotoIndex].isFavorite ? LocaleKeys.PhotoEvents_ScaffoldMessage_AddedFavorites.tr() : LocaleKeys.PhotoEvents_ScaffoldMessage_RemovedFavorites.tr(),
            mainButtonText: "Undo",
            onMainButtonPressed: () {
              photoChanceFavoriteStates(
                context: context,
                oneDayPhotos: oneDayPhotos,
                selectedPhotoIndex: selectedPhotoIndex,
                likeAnimationController: likeAnimationController,
                isFromUndo: true,
              );
            },
          );

    notifyListeners();
  }
}

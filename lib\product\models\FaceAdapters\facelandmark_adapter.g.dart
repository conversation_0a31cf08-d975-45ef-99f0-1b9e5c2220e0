// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'facelandmark_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FaceLandmarkAdapterAdapter extends TypeAdapter<FaceLandmarkAdapter> {
  @override
  final int typeId = 8;

  @override
  FaceLandmarkAdapter read(BinaryReader reader) {
    return FaceLandmarkAdapter();
  }

  @override
  void write(BinaryWriter writer, FaceLandmarkAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FaceLandmarkAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// ignore_for_file: file_names

import 'package:flutter/material.dart';
import 'package:hive_flutter/adapters.dart';

part 'refrance_parts.g.dart';

@HiveType(typeId: 2)
class ReferanceParts extends HiveObject {
  @HiveField(0)
  Offset eye1;
  @HiveField(1)
  Offset eye2;
  @HiveField(2)
  Offset mouth;

  ReferanceParts({
    required this.eye1,
    required this.eye2,
    required this.mouth,
  });
}

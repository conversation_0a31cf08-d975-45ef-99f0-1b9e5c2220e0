import 'dart:math';

import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'facecontour_adapter.g.dart';

@HiveType(typeId: 11)
class FaceContourAdapter extends TypeAdapter<FaceContour> {
  @override
  final typeId = 11;

  @override
  FaceContour read(BinaryReader reader) {
    final type = reader.read() as FaceContourType;
    final points = (reader.readList()).cast<Point<int>>();
    return FaceContour(type: type, points: points);
  }

  @override
  void write(BinaryWriter writer, FaceContour obj) {
    writer.write(obj.type);
    writer.writeList(obj.points);
  }
}

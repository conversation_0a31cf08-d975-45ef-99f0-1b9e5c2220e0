import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Items/ThemeAndColor/theme_color_selector.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/route_manager.dart';
import 'package:provider/provider.dart';

class ThemeSwitchTile extends StatefulWidget {
  const ThemeSwitchTile({super.key});

  @override
  State<ThemeSwitchTile> createState() => ThemeSwitchTileState();
}

class ThemeSwitchTileState extends State<ThemeSwitchTile> {
  @override
  Widget build(BuildContext context) {
    context.watch<ThemeProvider>().themeMode;

    return Ink(
      decoration: BoxDecoration(
        color: AppColors.panelBackground,
      ),
      child: InkWell(
        onTap: () async {
          return Get.dialog(
            const ThemeColorSelector(),
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 5,
            horizontal: 10,
          ),
          child: Row(
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.dark_mode_outlined,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    LocaleKeys.SettingsPage_SettingsTiles_Theme_Header.tr(),
                    style: TextStyle(
                      fontSize: 45.sp,
                    ),
                  ).tr(),
                ],
              ),
              const Spacer(),
              Container(
                width: 35,
                height: 35,
                decoration: BoxDecoration(
                  color: AppColors.main,
                  border: Border.all(color: Colors.black),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(width: 15),
              Switch.adaptive(
                value: context.read<ThemeProvider>().themeMode == ThemeMode.dark,
                thumbIcon: context.read<ThemeProvider>().themeMode == ThemeMode.dark
                    ? WidgetStateProperty.all(
                        const Icon(
                          Icons.brightness_2,
                          color: AppColors.black,
                        ),
                      )
                    : WidgetStateProperty.all(
                        const Icon(
                          Icons.wb_sunny,
                          color: AppColors.white,
                        ),
                      ),
                trackOutlineColor: context.read<ThemeProvider>().themeMode == ThemeMode.dark ? WidgetStateProperty.all(AppColors.transparent) : WidgetStateProperty.all(AppColors.dirtyRed),
                inactiveThumbColor: AppColors.dirtyRed,
                inactiveTrackColor: AppColors.white,
                onChanged: (_) {
                  changeThemeFunc();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void changeThemeFunc() {
    final newThemeMode = context.read<ThemeProvider>().themeMode == ThemeMode.dark ? ThemeMode.light : ThemeMode.dark;
    context.read<ThemeProvider>().setTheme(newThemeMode);

    LogService().changeTheme(newThemeMode.toString());
  }
}

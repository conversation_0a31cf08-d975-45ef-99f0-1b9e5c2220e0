import 'dart:async';
import 'dart:math';
import 'package:facelog/Pages/Produce/CameraPreview/camera_mixin.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sensors_plus/sensors_plus.dart';

// TODO: AccelometerCheckWidget ın set state i<PERSON><PERSON>i sonucu her seferinde top ve left değerleri sıfır oluyordu. bunun olmaması için buraya yazdım ama yanlış bir yol gibi duruyor. Şimdilik sorunu çözdü. Daha sonra tekrar bakılmalı.
double top = 0;
double left = 0;

final class AccelometerCheckWidget extends StatefulWidget {
  final void Function()? onAccelometerReady;
  final VoidCallback? onCameraRestart;
  const AccelometerCheckWidget({
    super.key,
    this.onAccelometerReady,
    this.onCameraRestart,
  });

  @override
  State<AccelometerCheckWidget> createState() => _AccelometerCheckWidgetState();
}

class _AccelometerCheckWidgetState extends State<AccelometerCheckWidget> with PhotoSaveMixin {
  Color color = AppColors.red;

  StreamSubscription? _accelerometerSubscription;
  late AccelerometerEvent event;

  final double accelometerSize = 100;

  @override
  void initState() {
    super.initState();
    accelometerTimerCount = 3;

    _accelerometerSubscription = accelerometerEventStream().listen(
      (accelometerEvent) {
        if (!isPhotoChecking) {
          setState(
            () {
              event = accelometerEvent;
              takePhotoForAccelometer();
            },
          );
        }
      },
    );
  }

  @override
  void dispose() {
    _accelerometerSubscription?.cancel();
    if (accelometerTimer != null) accelometerTimer!.cancel();
    super.dispose();
  }

  void setColor(AccelerometerEvent event) {
    double horizontal = isLandscape ? -(event.y * 12) + (0.5.sw - 70) : -(event.x * 12) + (0.5.sw - 50);
    double vertical = isLandscape ? -(event.z * 12) + 150 : -(event.z * 12) + 125;

    var horizontalDiff = isLandscape ? horizontal.abs() - (0.5.sw - 70) : horizontal.abs() - (0.5.sw - 50);
    var verticalDiff = isLandscape ? vertical.abs() - 150 : vertical.abs() - 125;

    if (horizontalDiff.abs() < 12 && verticalDiff.abs() < 12) {
      color = AppColors.lightGreen;
      if (isAutoTake && accelometerTimer == null || (accelometerTimer != null && !accelometerTimer!.isActive)) {
        accelometerTimer = Timer.periodic(
          const Duration(seconds: 1),
          (_) {
            if (isAutoTake) {
              accelometerTimerCount--;
            }
          },
        );
      }
    } else {
      color = AppColors.red;

      if (accelometerTimer != null) accelometerTimer!.cancel();
      accelometerTimerCount = 3;
    }
  }

  void setPosition(AccelerometerEvent event) {
    left = isLandscape ? (event.z * 5) : -(event.x * 5);
    top = isLandscape ? (event.y * 5) : -(event.z * 5);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: [
          Container(
            height: accelometerSize,
            width: accelometerSize,
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.red,
                width: 2.0,
              ),
              borderRadius: AppColors.borderRadiusCircular,
            ),
          ),
          AnimatedPositioned(
            top: top + accelometerSize * 0.3,
            left: left + accelometerSize * 0.3,
            duration: const Duration(milliseconds: 100),
            child: ClipOval(
              child: Container(
                width: accelometerSize * 0.4,
                height: accelometerSize * 0.4,
                color: color,
              ),
            ),
          ),
          Container(
            height: accelometerSize * 0.42,
            width: accelometerSize * 0.42,
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.green,
                width: 2.0,
              ),
              borderRadius: AppColors.borderRadiusCircular,
            ),
          ),
          if ((color == AppColors.lightGreen || color == AppColors.green) && isAutoTake && accelometerTimerCount > 0)
            Positioned(
              right: isLandscape ? 150 : null,
              top: isLandscape ? null : 100,
              child: Transform.rotate(
                angle: isLandscape ? pi / 2 : 0,
                child: Text(
                  accelometerTimerCount.toString(),
                  style: const TextStyle(
                    fontSize: 125,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> takePhotoForAccelometer() async {
    if (accelometerTimerCount == 0) {
      if (accelometerTimer != null) accelometerTimer!.cancel();
      color = AppColors.green;

      await takePhoto(context, onCameraRestart: widget.onCameraRestart);
    } else {
      setColor(event);
      setPosition(event);
    }
  }
}

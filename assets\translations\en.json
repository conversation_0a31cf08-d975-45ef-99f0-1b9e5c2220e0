{"TutorialPage": {"GeneralButtonText": {"Next": "Next", "Skip": "<PERSON><PERSON>", "Done": "Done"}, "WarningBoxTutorialDescription": "You can backup your photos or import old photos by going to the Storage page.", "NotificationTutorialDescription": "Remember to take a photo every day by turning on notifications."}, "Home": {"Latest": "Latest", "Total": "Total", "photo": "photo", "costumDialog": "We need access to your camera to take photo.", "NoPhotos": "You did not take any photo yet.", "FreePremium": {"ButtonText": "Try Free Pro", "Congratulations": "Congratulations", "DialogMessage": "Want to start a 15-day free premium trial?", "DialogButtonText": "Start"}, "Streak": {"Day": "Day", "Continue": "Continue", "StreakHistory": "Streak History", "UsedFreeze": "freezing rights exercised", "EarnFreeze": "You've won a serial freeze!"}, "PhotoFix": {"Title": "Fix Old Photos", "Description": "Some of your old photos haven't been through the face cropping process. Click the button to fix these photos.", "ButtonText": "Fix Photos", "ProgressTitle": "Fixing Photos...", "ProgressDescription": "This process may take some time. Please wait.", "SuccessMessage": "Photos fixed successfully!", "NoPhotosMessage": "No photos found to fix.", "ErrorMessage": "An error occurred while fixing photos."}}, "FeedPage": {"AppBar": {"Feed": {"Favorite": "Favorite", "All": "All"}, "Title": "Feed"}, "Body": {"SeeMore": "See More", "SeeLess": "See Less", "Empty": "There is no photos to show", "NoDescription": "There is nothing to remember"}, "BottomSheet": {"Edit": "Edit description"}}, "VideoPreferencesPage": {"AppBar": {"Title": "Video Settings", "ExportButton": "Export"}, "Body": {"VideoFeatures": {"Header": "Video Features", "SliderQuality": {"Header": "Quality"}, "SliderLength": {"Header": "Video Length"}, "LengthInfo": "second for per photo"}, "AudioSource": {"Header": "Audio Source", "Permission": "You must give permission to access your audio files."}, "AddMusic": "Add Music"}, "AnyPhoto": "You don't have any photo", "Watermark": "Watermark", "WannaRemoveWatermark": "Remove watermark", "IsIncludeNonFaces": "Include non-faces", "AtLeastThreePhoto": "You must have at least three photos to make a video.", "CancelExportProgress": "Video extraction will be canceled.", "VideoPropertiesList": {"Header": "Video Properties", "VideoPropertiesItems": {"Duration": "Duration: ", "PerPhotoDuration": "Per Photo Duration: ", "PhotoCount": "Photo Count: ", "DayCount": "Day Count: ", "Resolution": "Resolution: ", "FirstPhoto": "First Photo: ", "LastPhoto": "Last Photo: ", "GoToFolder": "Go to Folder: "}}}, "VideoPreview": {"AppBarHeader": "Preview", "Done": "Done"}, "Calendar": {"AppBar": "Calendar"}, "PaymentPage": {"BecomeProMemeber": "Become a Pro member.", "Package": {"Popular": "Recommended", "DiscountWord": "OFF", "Annual": "Annual", "Monthly": "Monthly", "Month": "month", "Year": "year"}, "Features": {"Feature1": "High Quality Video", "Feature2": "Remove watermark", "Feature3": "Back Up on Your Device", "Feature4": "Add Location to Photos", "Feature5": "Shot photo up to 5 times a day"}, "tryFreeTrials": "Upgrade to Pro", "FetchingProducts": "Fetching products...", "legalText": "Cancel before the free trial ends and you won't be charged. Limit one free trial for all Facelog subscriptions. Subscriptions renew automatically at the end of the billing period. Cancel anytime. Selecting a plan and completing checkout is your acceptance of Facelog's Terms of Service and Privacy Policy.", "billedmonthly": "billed monthly", "billedyearly": "billed annualy", "TheMarketNotAvailable": "The market is not available. Please try again later."}, "CameraPages": {"AlreadyTook": "You can take max 5 photos eveyday.", "Camera": {"TakePhoto": "Take Photo", "LocationService": {"LocationLoading": "Loading...", "Unknown": "Unknown", "AddLocation": "Add Location", "AccessLocationDialog": {"OpenCamera": "Enable Location", "ExplanationOfPermission": "Enable location services to add location.", "GoSettingsButton": "Go To Settings"}, "Tutorial": {"ChangeCamera": "Camera Direction", "ChangeCameraDesc": "You can switch between front and rear camera.", "AutoTake": "Auto Take", "AutoTakeDesc": "When you hold the phone straight, the accelometer turns green. It counts down from 3 and takes a photo.", "GhostImage": "Ghost Image", "GhostImageDesc": "It shows the ghost of the previous day. It helps you take more harmonious photos.", "ReferanceParts": "Face References", "ReferancePartsDesc": "It shows the face references (mouth and eyes) that you set. You can align your head accordingly.", "Grid": "Grid View", "GridDesc": "When aligning your head, you can use the grid view to center it."}}, "PhotoPreview": {"Header": "Review Photo", "Confirm": "Confirm", "AddNote": "Add note", "Tutorial": {"Note": "Take Note", "NoteDesc": "Write down everything you want to remember here. You can make your memories more meaningful by adding notes to your photos.", "Location": "Location", "LocationDesc": "If you want to remember where you are, you can add location to your photos.", "Statistics": "Statistics", "StatisticsDesc": "Your face is analyzed and shown how close the data is to our ideal face model. By following the data, you can take the most accurate photo.", "ReferanceParts": "Face References"}}, "GhostImage": "To see a ghost photo, there must be a photo already taken.", "RefParts": "There must be a photo taken beforehand to see the reference eye and mouth.", "ErrorCamera": "An error has occurred. Please try again.", "SaveFaceParts": "Save", "SetReferanceFace": "Set Face References", "RefPartsDescTutorial": "Drag the mouth and eye icons to your own face. You can use these shapes as a reference to take more accurate photos in future shots.", "FaceDetection": {"FaceNotDetected": "Face not detected!", "FaceDetectionError": "Face detection error!", "PhotoNotCropped": "Photo could not be cropped!", "Statistics": "Statistics", "Rotation": "Rotation", "Size": "Size", "Distance": "Distance to center", "Side": "Side", "RightLeft": "Right/Left", "UpDown": "Up/Down"}}}, "PhotoPage": {"NoDescripiton": "There is nothing to remember"}, "SettingsPage": {"HeaderSettings": "SETTINGS", "HeaderAbout": "About", "SettingsTiles": {"DailyReminder": {"Header": "Daily Reminder", "TimePiclerText": "Select Time", "InvalidDateTime": "Error: Invalid Date Time", "AccessNotificaitonDialog": "You must give permission to open notifications. Do you want to go to settings?"}, "Theme": {"Header": "Theme & Color", "SelectColor": {"Title": "Choose color", "SelectedColors": "Selected Colors:", "Colors": {"Main": "Main", "Light": "Light", "Dark": "Dark"}}}, "Language": {"Header": "Language", "SelectingPopUp": {"Title": "Select Language", "Languages": {"English": "English", "Turkish": "Türkçe"}}}, "Storage": {"AppBarTitle": "Storage", "Body": {"WarningBoxImportPhotos": {"description": {"Title": "Import photos from your device.", "Content": "With this action, the photos in the file you selected will be visible in the application."}, "ButtonText": "Import Photos", "CancelImportProgress": "The import will be canceled."}, "WarningBoxExportPhotos": {"description": {"Title": "Back up your memory on your device.", "Content": "With this process, all your data in the Facelog will be saved on your device. In this way, you can easily view and organize your memory files."}, "ButtonText": "Save Memories", "SurePopUp": {"Title": "Warning", "Content": "All your photos from the app will be downloaded to your device.", "ActionButtons": {"No": "Cancel", "Yes": "Accept"}}}, "WarningBoxDeleteAllPhotos": {"description": {"Title": "You may lose your data by doing this.", "Content": "With this process, all your data in the Facelog will be permanently deleted. We recommend that you be careful when progress this procedure."}, "FirstButtonText": "Delete All Photos", "SecondButtonText": "Delete Imported Photos", "SurePopUp": {"Title": "Warning", "MessageAll": "All your photos in the app will be deleted.", "MessageImported": "All your imported photos in the app will be deleted.", "ActionButtons": {"No": "Cancel", "timerAccept": "Accept in", "Yes": "Accept"}}, "PermanentlyDeleteWarning": "This action cannot be undone. It will cause your photo to be permanently deleted."}, "PhotoManager": {"Title": "Photo Manager", "NoDateWarning": "Your undated photos will be dated to today.", "AnyPhotoSelected": "No photo selected.", "AccesStorageRequestWarning": "You must grant storage access to continue.", "AccesPhotosRequestWarning": "You must grant access to the photos to continue.", "DateNotFound": "Date not found", "UndatedPhotosSet": "Undated photos set to today.", "YouMustAnalyze": "You should analyze the photos before proceeding.", "Error": "An error has occurred. Please try again later."}}}, "Orientation": {"Title": "Choose picture orientation", "Reccomendation": "Recommended", "Save": "Save", "OrientatiionPopUp": {"Warning": "Warning", "Content": "If you continue, all photos will be deleted. Do you agree?"}}, "Supporters": "Supporters", "RateUsBottomSheet": {"Header": "Support our app! 📈", "LaterButton": "Later"}, "Contact": {"ContactUsHeader": "Contact Us", "AskAQuestion": "Ask a Question", "ReportAProblem": "Report a Problem", "IdeasAndFeedback": "Ideas & Feedback"}, "About": {"Header": "About", "TermsOfService": "Terms of Service", "PrivacyPolicy": "Privacy Policy"}, "ShareUs": "Share Facelog"}, "Supporters": {"Thanking": "Thank You!", "InternetConnection": "Please check your internet connection.", "Error": "A problem has occurred. Please try again later.", "NoSupporters": "No supporters yet.", "Closing": "Close"}, "version": "Version"}, "PhotoEvents": {"Delete": "Delete", "Download": "Download", "Share": "Share", "ScaffoldMessage": {"photoDeleted": "Photo deleted", "photoCouldntDeleted": "Photo could not be deleted. Please try again later.", "AllPhotosDownloaded": "All photos downloaded on Download folder.", "PhotoDownloaded": "Downloaded on Download folder.", "YouDontHaveAnyPhoto": "You dont have any photo", "YouDontHaveAnyImportedPhoto": "You have no imported photos", "AllPhotosDeleted": "All photos deleted!", "ImportedPhotosDeleted": "All imported photos deleted!", "AddedFavorites": "Added to favorites.", "RemovedFavorites": "Removed from favorites."}, "LocationService": {"ButtonText": "Go to Maps", "ForwardMap": "You will be redirected to Maps.", "FailForwarding": "Maps could not be opened. Please try again."}}, "RateUsDialogWarnings": {"RateUsInternetConnectionFail": "Please check your internet connection and try again to rate us.", "message": {"Dictation": "Provide us with feedback to improve Facelog. You will be redirected to Gmail.", "Subject": "Facelog Feedback", "Body": "Your Feedback..."}}, "Dialog": {"Okay": "Okay", "Cancel": "Cancel", "ShareFacelog": "I am inviting you to Facelog.", "TakeMorePhotoPremium": "You must be a premium member to take more photos in one day.", "MustPremium": "You must be a premium member to use this feature."}, "Message": {"Warning": "Warning", "Info": "Info", "Success": "Success"}, "General": {"NoteBottomSheetNoteSomethingHintText": "Note something...", "AppError": "An error has occurred. Please try again.", "AccesRequestWarning": "You must give permission to continue."}, "VideoListPage": {"title": "Videos", "dialogMessage": "Are you sure you want to delete selected videos?", "approve": "Approve", "selectVideo": "Select", "noVideo": "You don't have any videos.", "DeleteSuccessfully": "Video successfully deleted.", "DeleteUnsuccessfully": "Video could not be deleted."}, "PremiumVerificationError": "Your premium subscription cannot be verified. Please check your internet connection and try again.", "AddMusicPage": {"Title": "Select Music Part", "DoneButton": "Done", "minutesShortage": "min", "secondShortage": "sec", "warning": "The duration of the video must be longer than or equal to the duration of the selected music."}, "ProWarning": "Your Pro subscription cannot be verified. Please make sure your internet connection is active and log in to the app again."}
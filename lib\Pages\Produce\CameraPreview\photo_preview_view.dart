import 'dart:io';
import 'dart:math';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:facelog/Pages/Produce/CameraPreview/camera_mixin.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_painter.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_statistics_container.dart';
import 'package:facelog/Pages/Produce/CameraProgress/components/face_parts.dart';
import 'package:facelog/Pages/Produce/CameraProgress/components/set_face_parts.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

class PhotoPreviewPage extends StatefulWidget {
  final XFile picture;

  const PhotoPreviewPage({
    super.key,
    required this.picture,
  });

  @override
  State<PhotoPreviewPage> createState() => _PhotoPreviewPageState();
}

class _PhotoPreviewPageState extends State<PhotoPreviewPage> with PhotoSaveMixin {
  // Texts
  final String _addNote = LocaleKeys.CameraPages_Camera_PhotoPreview_AddNote.tr();
  final String _addLocation = LocaleKeys.CameraPages_Camera_LocationService_AddLocation.tr();
  final String _loadingLocation = LocaleKeys.CameraPages_Camera_LocationService_AddLocation.tr();
  final String _unknownLocation = LocaleKeys.CameraPages_Camera_LocationService_Unknown.tr();
  final String _note = LocaleKeys.CameraPages_Camera_PhotoPreview_Tutorial_Note.tr();
  final String _noteDesc = LocaleKeys.CameraPages_Camera_PhotoPreview_Tutorial_NoteDesc.tr();
  final String _location = LocaleKeys.CameraPages_Camera_PhotoPreview_Tutorial_Location.tr();
  final String _locationDesc = LocaleKeys.CameraPages_Camera_PhotoPreview_Tutorial_LocationDesc.tr();
  final String _statistics = LocaleKeys.CameraPages_Camera_PhotoPreview_Tutorial_Statistics.tr();
  final String _statisticsDesc = LocaleKeys.CameraPages_Camera_PhotoPreview_Tutorial_StatisticsDesc.tr();

  late final PhotoProvider photoProvider = context.read<PhotoProvider>();
  bool isLocationOn = false;
  String? address;
  bool isLocationLoading = false;

  bool isLoading = false;

  String? dayNoteText;

  // Showcase Keys
  late BuildContext contextForShowcase;
  final GlobalKey statisticsKey = GlobalKey();
  final GlobalKey locationKey = GlobalKey();
  final GlobalKey noteKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    LogService().logScreen("PhotoPreviewPage");
  }

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      disableMovingAnimation: true,
      builder: (BuildContext context) {
        contextForShowcase = context;
        return Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            leading: IconButton(
              padding: const EdgeInsets.only(left: 10),
              icon: const Icon(
                Icons.arrow_back_ios,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            actions: [
              IconButton(
                icon: const Icon(
                  Icons.question_mark,
                ),
                onPressed: () {
                  ShowCaseWidget.of(contextForShowcase).startShowCase(
                    [
                      statisticsKey,
                      locationKey,
                      noteKey,
                    ],
                  );
                },
              ),
              const SizedBox(width: 5)
            ],
            title: const Text(LocaleKeys.CameraPages_Camera_PhotoPreview_Header).tr(),
            backgroundColor: AppColors.background,
          ),
          body: Column(
            children: [
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: AppColors.highBorderRadiusAll,
                    child: SizedOverflowBox(
                      size: Size(1.sw, 0.76.sh),
                      child: Image.file(
                        File(widget.picture.path),
                      ),
                    ),
                  ),
                  if (isRefPartsActive && photoProvider.allPhotosList.isNotEmpty) ...[
                    eyes1(),
                    eyes2(),
                    mouth(),
                  ],
                  if (isGhostImageActive && photoProvider.allPhotosList.isNotEmpty)
                    ClipRRect(
                      borderRadius: AppColors.highBorderRadiusAll,
                      child: SizedOverflowBox(
                        size: Size(1.sw, 0.76.sh),
                        child: Transform.scale(
                          // TODO: burada 1.8 responsive sorun oluşturmuyor gibi duruyor ancak emulatorun kameralarıyla emin olamadım. teste çıkınca gerçek cihazlarda dikkat et.
                          scale: isLandscape ? 1.8 : 1,
                          child: Transform.rotate(
                            angle: isLandscape ? pi / 2 : 0,
                            child: Opacity(
                              opacity: 0.4,
                              child: Image.file(
                                File(photoProvider.allPhotosList.last.photos.last.path),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  if (isGridActive)
                    Positioned.fill(
                      child: Image.asset(
                        AssetsPath.grid,
                        scale: 1,
                      ),
                    ),
                  // Debug modda yüzün etrafına kare çiziyor
                  if (kDebugMode) ...[
                    if (currentFace != null)
                      Center(
                        child: CustomPaint(
                          size: Size(0.93.sw, 0.76.sh),
                          painter: FacePainter(face: currentFace),
                        ),
                      ),
                    // Debug modunda crop versiyonu seçimi
                    Positioned(
                      top: 10,
                      left: 10,
                      right: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: CropVersion.values.map((version) {
                              final isSelected = selectedCropVersion == version;
                              return Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 2),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      selectedCropVersion = version;
                                    });
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('Crop Version: ${version.name.toUpperCase()} seçildi'),
                                        duration: const Duration(seconds: 1),
                                      ),
                                    );
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: isSelected ? Colors.blue : Colors.transparent,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: isSelected ? Colors.blue : Colors.white.withValues(alpha: 0.5),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      version.name.toUpperCase(),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ],
                  if (currentFace != null && isStatisticsActive)
                    PhotoStatisticsContainer(
                      detectedFace: currentFace!,
                    ),
                  Positioned(
                    bottom: 0,
                    child: Container(
                      width: 1.sw,
                      padding: const EdgeInsets.all(5),
                      child: Row(
                        children: [
                          statisticsButton(),
                          const Spacer(),
                          ghostImageButton(),
                          const SizedBox(width: 10),
                          refPartsButton(),
                          const SizedBox(width: 10),
                          gridButton(),
                        ],
                      ),
                    ),
                  ),
                  if (isLocationFeatureActive) addLocationButton(),
                  addNoteButton(),
                ],
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Transform.rotate(
                        angle: isLandscape ? pi / 2 : 0,
                        child: const Icon(
                          Icons.restart_alt,
                          size: 60,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        if (isLoading) return;
                        isLoading = true;
                        if (photoProvider.allPhotosList.isEmpty) {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SetFaceParts(
                                photoPath: widget.picture.path,
                                address: address,
                                dayNoteText: dayNoteText,
                              ),
                            ),
                          );
                        } else {
                          await savePhoto(
                            address: address,
                            context: context,
                            photoPath: widget.picture.path,
                            dayNoteText: dayNoteText,
                          );
                        }
                        isLoading = false;
                      },
                      child: Container(
                        color: AppColors.transparent,
                        child: Row(
                          children: [
                            Text(
                              LocaleKeys.CameraPages_Camera_PhotoPreview_Confirm,
                              style: TextStyle(
                                color: AppColors.text,
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                              ),
                            ).tr(),
                            const SizedBox(width: 10),
                            const Icon(
                              Icons.send,
                              size: 40,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
            ],
          ),
        );
      },
    );
  }

  Container ghostImageButton() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.background.withValues(alpha: 0.7),
      ),
      child: IconButton(
        onPressed: () async {
          if (photoProvider.allPhotosList.isEmpty) {
            await Helper().getDialog(
              message: LocaleKeys.CameraPages_Camera_GhostImage.tr(),
            );
          } else {
            setState(() {
              isGhostImageActive = !isGhostImageActive;
            });
            SharedPreferences prefs = await SharedPreferences.getInstance();
            prefs.setBool('isGhostImageActive', isGhostImageActive);
            LogService().userIsGhostImageActive();
          }
        },
        icon: Transform.rotate(
          angle: isLandscape ? pi / 2 : 0,
          child: Icon(
            (isGhostImageActive && photoProvider.allPhotosList.isNotEmpty) ? Icons.group_outlined : Icons.group_off_outlined,
            size: 28,
          ),
        ),
      ),
    );
  }

  Container refPartsButton() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.background.withValues(alpha: 0.7),
      ),
      child: IconButton(
        onPressed: () async {
          if (photoProvider.allPhotosList.isEmpty) {
            await Helper().getDialog(
              message: LocaleKeys.CameraPages_Camera_RefParts.tr(),
            );
          } else {
            setState(() {
              isRefPartsActive = !isRefPartsActive;
            });
            SharedPreferences prefs = await SharedPreferences.getInstance();
            prefs.setBool('isRefPartsActive', isRefPartsActive);
            LogService().userIsRefPartsActive();
          }
        },
        icon: Transform.rotate(
          angle: isLandscape ? pi / 2 : 0,
          child: Icon(
            (isRefPartsActive && photoProvider.allPhotosList.isNotEmpty) ? Icons.visibility_outlined : Icons.visibility_off_outlined,
            size: 28,
          ),
        ),
      ),
    );
  }

  Container gridButton() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.background.withValues(alpha: 0.7),
      ),
      child: IconButton(
        onPressed: () async {
          setState(() {
            isGridActive = !isGridActive;
          });
          SharedPreferences prefs = await SharedPreferences.getInstance();
          prefs.setBool('isGridActive', isGridActive);
          LogService().userIsGridActive();
        },
        icon: Transform.rotate(
          angle: isLandscape ? pi / 2 : 0,
          child: Icon(
            isGridActive ? Icons.grid_on : Icons.grid_off,
            size: 28,
          ),
        ),
      ),
    );
  }

  Future<void> getLocation() async {
    if (isPremium == false) {
      Helper().mustPremiumDialog(context);
    } else {
      if (isLocationOn) {
        setState(() {
          isLocationOn = false;
        });
      } else {
        // * Location açık mı kapalı mı bak.

        setState(() {
          isLocationLoading = true;
        });

        address = await getLocationPermissionAndService(
          context: context,
          accuracy: LocationAccuracy.medium,
        );

        setState(() {
          isLocationLoading = false;
        });

        if (address != null) {
          LogService().addLocation();

          setState(() {
            isLocationOn = true;
          });
        }
      }
    }
  }

  Widget statisticsButton() {
    return Showcase(
      key: statisticsKey,
      title: _statistics,
      description: _statisticsDesc,
      overlayOpacity: 0.7,
      targetBorderRadius: AppColors.highBorderRadiusAll,
      targetPadding: AppColors.showcasePadding,
      child: GestureDetector(
        onTap: () async {
          if (currentFace == null) return;
          setState(() {
            isStatisticsActive = !isStatisticsActive;
          });
          SharedPreferences prefs = await SharedPreferences.getInstance();
          prefs.setBool('isStatisticsActive', isStatisticsActive);
          LogService().userIsStatisticsActive();
        },
        child: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: AppColors.highBorderRadiusAll,
            color: AppColors.background.withValues(alpha: 0.7),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.analytics_outlined,
                size: 25,
              ),
              const SizedBox(width: 3),
              Text(
                totalAccuracy == null ? LocaleKeys.CameraPages_Camera_FaceDetection_FaceNotDetected : '%${totalAccuracy!.toStringAsFixed(0)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: totalAccuracy == null ? AppColors.red : Helper().getColorForPercentage(totalAccuracy!),
                ),
              ).tr(),
            ],
          ),
        ),
      ),
    );
  }

  Widget addLocationButton() {
    return Positioned(
      bottom: isLocationFeatureActive ? 60 : 105,
      right: 5,
      child: Showcase(
        key: locationKey,
        title: _location,
        description: _locationDesc,
        overlayOpacity: 0.7,
        targetBorderRadius: AppColors.highBorderRadiusAll,
        targetPadding: AppColors.showcasePadding,
        child: GestureDetector(
          onTap: () async {
            await getLocation();
          },
          child: Container(
            padding: const EdgeInsets.all(7),
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: AppColors.highBorderRadiusAll,
              color: AppColors.background.withValues(alpha: 0.7),
            ),
            child: Row(
              children: [
                Icon(
                  isLocationOn ? Icons.location_on : Icons.location_off,
                  size: 25,
                ),
                const SizedBox(width: 5),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 0.87.sw),
                  child: AutoSizeText(
                    isLocationLoading
                        ? _loadingLocation
                        : isLocationOn
                            ? address ?? _unknownLocation
                            : _addLocation,
                    minFontSize: 10,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget addNoteButton() {
    return Positioned(
      bottom: isLocationFeatureActive ? 105 : 60,
      right: 5,
      child: Showcase(
        key: noteKey,
        title: _note,
        description: _noteDesc,
        overlayOpacity: 0.7,
        targetBorderRadius: AppColors.highBorderRadiusAll,
        targetPadding: AppColors.showcasePadding,
        child: GestureDetector(
          onTap: () async {
            void onDipose(String? newtext) {
              dayNoteText = newtext;
              LogService().addNote(dayNoteText!.length.toString());
            }

            bool isLastPhotoToday = photoProvider.allPhotosList.isEmpty ? false : Helper().isSameDay(photoProvider.allPhotosList.last.date, DateTime.now());

            await Helper().showEditDescriptionSheet(
              context: context,
              onDispose: onDipose,
              oneDayPhoto: isLastPhotoToday ? photoProvider.allPhotosList.last : null,
            );
          },
          child: Container(
            padding: const EdgeInsets.all(7),
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: AppColors.highBorderRadiusAll,
              color: AppColors.background.withValues(alpha: 0.7),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.edit_note,
                  size: 25,
                ),
                const SizedBox(width: 5),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 0.87.sw),
                  child: AutoSizeText(
                    _addNote,
                    minFontSize: 10,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

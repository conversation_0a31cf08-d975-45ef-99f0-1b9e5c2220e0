import 'package:facelog/Pages/Display/Calendar/calendar_page.dart';
import 'package:facelog/Pages/Display/Feed/feed_page.dart';
import 'package:facelog/Pages/Produce/Home/home_page.dart';
import 'package:facelog/Pages/Produce/Home/version_upgrade_fix.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/video_export_page.dart';
import 'package:facelog/core/CostumWidgets/NavBar/scroll_to_hide_widget.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/state/Provider/navbar_provider.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class NavBarAndPages extends StatefulWidget {
  final bool isFirst;

  const NavBarAndPages({
    super.key,
    required this.isFirst,
  });

  @override
  State<NavBarAndPages> createState() => _NavBarAndPagesState();
}

class _NavBarAndPagesState extends State<NavBarAndPages> {
  late final PhotoProvider photoProvider = context.read<PhotoProvider>();

  late ScrollController feedPageScrollController;
  late ScrollController hideNavBarController;

  bool isLoadingPhotos = true;

  @override
  void initState() {
    super.initState();
    feedPageScrollController = ScrollController();
    hideNavBarController = ScrollController();

    getPhotos();
  }

  @override
  void dispose() {
    feedPageScrollController.dispose();
    hideNavBarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> screens = [
      if (isFeedPageFeatureActive) FeedPage(controller: feedPageScrollController),
      const CalendarPage(),
      isLoadingPhotos ? const Center(child: CircularProgressIndicator()) : const HomePage(),
    ];

    return IgnorePointer(
      ignoring: isLoadingPhotos,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        extendBody: true,
        body: screens[context.watch<NavbarProvider>().currentIndex],
        bottomNavigationBar: SafeArea(
          child: ScrollToHideWidget(
            height: 245.h,
            feedPageScrollController: feedPageScrollController,
            hideNavBarController: hideNavBarController,
            child: ClipRRect(
              borderRadius: AppColors.borderRadiusAll,
              child: BottomNavigationBar(
                currentIndex: context.read<NavbarProvider>().currentIndex,
                onTap: (index) {
                  if (index == (isFeedPageFeatureActive ? 3 : 2)) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const VideoPreferencesView(),
                      ),
                    );
                  } else {
                    setState(() {
                      context.read<NavbarProvider>().currentIndex = index;
                    });
                  }
                },
                items: [
                  if (isFeedPageFeatureActive)
                    BottomNavigationBarItem(
                      icon: Icon(
                        Icons.person_2_outlined,
                      ),
                      label: 'Feed',
                    ),
                  BottomNavigationBarItem(
                    icon: Icon(
                      Icons.calendar_month,
                    ),
                    label: 'Calendar',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(
                      Icons.home_outlined,
                    ),
                    label: 'Home',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(
                      Icons.videocam_outlined,
                    ),
                    label: 'Video',
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> getPhotos() async {
    if (!widget.isFirst) {
      setState(() {
        isLoadingPhotos = false;
      });
      return;
    }

    await photoProvider.getAllPhotosInProvider();

    await VerisonFix(context: context).fixAll();

    setState(() {
      isLoadingPhotos = false;
    });
  }
}

import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'offset_adapter.g.dart';

@HiveType(typeId: 3)
class OffsetAdapter extends TypeAdapter<Offset> {
  @override
  final int typeId = 3; // You can choose any unique positive integer here

  @override
  Offset read(BinaryReader reader) {
    // Read data from the binary reader and return an Offset instance
    return Offset(reader.readDouble(), reader.readDouble());
  }

  @override
  void write(BinaryWriter writer, Offset obj) {
    // Write data to the binary writer
    writer.writeDouble(obj.dx);
    writer.writeDouble(obj.dy);
  }
}

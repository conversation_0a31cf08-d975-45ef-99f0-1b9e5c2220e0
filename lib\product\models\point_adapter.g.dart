// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'point_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PointAdapterAdapter extends TypeAdapter<PointAdapter> {
  @override
  final int typeId = 9;

  @override
  PointAdapter read(BinaryReader reader) {
    return PointAdapter();
  }

  @override
  void write(BinaryWriter writer, PointAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PointAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

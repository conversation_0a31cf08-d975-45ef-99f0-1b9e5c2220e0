import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/config/language/locales_enum.dart';
import 'package:facelog/product/config/language/product_localization.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/notification_services.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageSelectionPopup extends StatefulWidget {
  const LanguageSelectionPopup({super.key});

  @override
  LanguageSelectionPopupState createState() => LanguageSelectionPopupState();
}

class LanguageSelectionPopupState extends State<LanguageSelectionPopup> {
  late Locales _selectedLanguage;
  late Locales oldSelectedLanguage;

  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSelectedLanguage();
  }

  Future<void> _loadSelectedLanguage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      // Shared preferences'ten seçilen dilin yüklenmesi
      _selectedLanguage = Locales.values.firstWhere((locale) => locale.toString() == (prefs.getString('selected_language') ?? "${context.locale == const Locale('tr', 'TR') ? Locales.tr : Locales.en}")); // TODO: Şimdilik işe yaradığı için değişmedi fakat mutlaka düzeltilmeli.
      oldSelectedLanguage = _selectedLanguage;
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? const Center(child: CircularProgressIndicator())
        : AlertDialog(
            title: const Text(LocaleKeys.SettingsPage_SettingsTiles_Language_SelectingPopUp_Title).tr(),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: Locales.values
                  .map(
                    (locale) => ListTile(
                      title: Text(
                        locale == Locales.en ? 'English' : 'Turkish',
                        style: TextStyle(
                          fontWeight: locale == _selectedLanguage ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      onTap: () {
                        _selectedLanguage = locale;

                        _saveSelectedLanguage();
                      },
                      leading: Radio(
                        value: locale,
                        groupValue: _selectedLanguage,
                        onChanged: (value) {
                          _selectedLanguage = value as Locales;

                          _saveSelectedLanguage();
                        },
                      ),
                    ),
                  )
                  .toList(),
            ),
          );
  }

  Future<void> _saveSelectedLanguage() async {
    await ProductLocalization.updateLanguage(
      context: context,
      value: _selectedLanguage,
    );

    ProductLocalization.saveSelectedLanguage(_selectedLanguage);
    LogService().changeLanguage(_selectedLanguage.toString());
    // ? Dil değişince bildirim mesajının değişmesi için
    if (isNotificationsEnabled) {
      NotificationServices().setDailyNotification(context);
    }
  }
}

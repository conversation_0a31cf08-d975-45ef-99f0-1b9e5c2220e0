{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Facelog",
            "request": "launch",
            "type": "dart"
        },

        // Multiple Run Devices For Test (For Islam)
        {
            "name": "RealDevice",
            "request": "launch",
            "type": "dart",
            "deviceId": "a2d32e43"
        }, 
        {
            "name": "Pixel2",
            "request": "launch",
            "type": "dart",
            "deviceId": "emulator-5556"
        },    
        {
            "name": "Resizable",
            "request": "launch",
            "type": "dart",
            "deviceId": "emulator-5558"
        },    
        {
            "name": "Pixel8",
            "request": "launch",
            "type": "dart",
            "deviceId": "emulator-5554"
        },   
        {
            "name": "Tablet",
            "request": "launch",
            "type": "dart",
            "deviceId": "emulator-5556"
        }, 
        //--------------------------------


        {
            "name": "Facelog (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ],

    "compounds": [
        {
            "name": "MultipleTest",
            "configurations": ["RealDevice","Pixel2","Pixel8","Resizable","Tablet"]
        }
    ]
}
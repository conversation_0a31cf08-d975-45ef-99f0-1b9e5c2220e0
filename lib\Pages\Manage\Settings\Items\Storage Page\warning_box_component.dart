import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class WarningBox extends StatefulWidget {
  const WarningBox({
    super.key,
    this.textColor,
    this.buttonColor,
    required this.headerText,
    required this.firstButtonText,
    this.secondButtonText,
    required this.description,
    required this.onFirstButtonClick,
    this.onSecondButtonClick,
  });

  final Color? textColor;
  final Color? buttonColor;
  final String headerText;
  final String description;
  final String firstButtonText;
  final String? secondButtonText;
  final VoidCallback onFirstButtonClick;
  final VoidCallback? onSecondButtonClick;

  @override
  State<WarningBox> createState() => _WarningBoxState();
}

class _WarningBoxState extends State<WarningBox> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 0.9.sw,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(
          color: widget.buttonColor ?? AppColors.onBackground,
        ),
        color: AppColors.panelBackground,
        borderRadius: AppColors.borderRadiusAll,
      ),
      child: Column(
        children: [
          SizedBox(
            width: 0.8.sw,
            child: Text(
              widget.headerText,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 20.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(height: 0.015.sh),
          Text(
            widget.description,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 0.02.sh),
          GestureDetector(
            onTap: widget.onFirstButtonClick,
            child: SizedBox(
              height: 0.05.sh,
              width: 0.7.sw,
              child: TextButton(
                onPressed: widget.onFirstButtonClick,
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all<Color>(widget.buttonColor ?? AppColors.main),
                ),
                child: Text(
                  widget.firstButtonText,
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                    color: widget.textColor ?? AppColors.white,
                  ),
                ),
              ),
            ),
          ),
          if (widget.secondButtonText != null) ...[
            SizedBox(height: 0.01.sh),
            GestureDetector(
              onTap: widget.onFirstButtonClick,
              child: SizedBox(
                height: 0.05.sh,
                width: 0.7.sw,
                child: TextButton(
                  onPressed: widget.onSecondButtonClick,
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all<Color>(widget.buttonColor ?? AppColors.main),
                  ),
                  child: Text(
                    widget.secondButtonText!,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 16,
                      color: widget.textColor ?? AppColors.white,
                    ),
                  ),
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }
}

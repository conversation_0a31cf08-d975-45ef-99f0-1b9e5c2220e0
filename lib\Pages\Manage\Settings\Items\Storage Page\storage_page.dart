import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Payment/payment_page.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'warning_box_component.dart';

class StoragePage extends StatefulWidget {
  const StoragePage({super.key});

  @override
  State<StoragePage> createState() => _StoragePageState();
}

class _StoragePageState extends State<StoragePage> {
  // Texts
  final String _importPhotosTitle = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description_Title.tr();
  final String _importPhotosContent = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description_Content.tr();
  final String _importPhotosButtonText = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_ButtonText.tr();

  final String _exportPhotosTitle = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_description_Title.tr();
  final String _exportPhotosContent = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_description_Content.tr();
  final String _exportPhotosButtonText = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_ButtonText.tr();
  final String _exportPhotosSurePopUpContent = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp_Content.tr();

  final String _deleteAllPhotosTitle = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_description_Title.tr();
  final String _deleteAllPhotosContent = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_description_Content.tr();
  final String _deleteAllPhotosFirstButtonText = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_FirstButtonText.tr();
  final String _deleteAllPhotosSecondButtonText = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SecondButtonText.tr();
  final String _deleteAllPhotosMessageAll = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_MessageAll.tr();
  final String _deleteAllPhotosMessageImported = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_MessageImported.tr();

  final String _mustPremium = LocaleKeys.Dialog_MustPremium.tr();
  final String _freeTrail = LocaleKeys.PaymentPage_tryFreeTrials.tr();
  final String _noPhotos = LocaleKeys.Home_NoPhotos.tr();
  final String _allPhotosDownloaded = LocaleKeys.PhotoEvents_ScaffoldMessage_AllPhotosDownloaded.tr();
  final String _anyPhoto = LocaleKeys.PhotoEvents_ScaffoldMessage_YouDontHaveAnyPhoto.tr();
  final String _anyImportedPhoto = LocaleKeys.PhotoEvents_ScaffoldMessage_YouDontHaveAnyImportedPhoto.tr();
  final String _deleteImportedPhotos = LocaleKeys.PhotoEvents_ScaffoldMessage_ImportedPhotosDeleted.tr();

  final String _appbarTitle = LocaleKeys.SettingsPage_SettingsTiles_Storage_AppBarTitle.tr();

  bool isLoadingPhotos = false;

  @override
  void initState() {
    super.initState();

    LogService().logScreen("StoragePage");
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          Helper().cancelApproveDialog(context, isLoadingPhotos);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            padding: const EdgeInsets.only(left: 10),
            icon: const Icon(
              Icons.arrow_back_ios,
            ),
            onPressed: () {
              Helper().cancelApproveDialog(context, isLoadingPhotos);
            },
          ),
          title: Text(
            _appbarTitle,
          ),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Center(
                child: Column(
                  children: [
                    SizedBox(height: 0.02.sh),
                    WarningBox(
                      textColor: AppColors.white,
                      buttonColor: AppColors.deepBlue,
                      headerText: _importPhotosTitle,
                      description: _importPhotosContent,
                      firstButtonText: _importPhotosButtonText,
                      onFirstButtonClick: () async {
                        if (!isPremium!) {
                          await Helper().getDialog(
                            message: _mustPremium,
                            acceptButtonText: _freeTrail,
                            onAccept: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const PaymentPage(),
                                ),
                              );
                            },
                          );
                        } else {
                          setState(() {
                            isLoadingPhotos = true;
                          });

                          await Helper().getPhotosToImport(
                            context: context,
                            isAddLater: false,
                          );

                          setState(() {
                            isLoadingPhotos = false;
                          });
                        }
                      },
                    ),
                    SizedBox(height: 0.02.sh),
                    WarningBox(
                      buttonColor: AppColors.main,
                      headerText: _exportPhotosTitle,
                      description: _exportPhotosContent,
                      firstButtonText: _exportPhotosButtonText,
                      onFirstButtonClick: () async {
                        if (context.read<PhotoProvider>().allPhotosList.isEmpty) {
                          Helper().getMessage(
                            message: _noPhotos,
                            status: StatusEnum.WARNING,
                          );
                        } else if (isPremium!) {
                          await Helper().getDialog(
                            message: _exportPhotosSurePopUpContent,
                            onAccept: () async {
                              setState(() {
                                isLoadingPhotos = true;
                              });

                              await Helper().downloadAllPhotos(context);

                              setState(() {
                                isLoadingPhotos = false;
                              });

                              if (mounted) Navigator.pop(context);

                              //? TODO Belki indirme işlemini gösteren bir indirme çubuğu ekleyebiliriz.
                            },
                          );
                        } else {
                          await Helper().getDialog(
                            message: _mustPremium,
                            acceptButtonText: _freeTrail,
                            onAccept: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const PaymentPage(),
                                ),
                              );
                            },
                          );
                        }
                      },
                    ),
                    SizedBox(height: 0.02.sh),
                    WarningBox(
                      textColor: AppColors.white,
                      buttonColor: AppColors.red,
                      headerText: _deleteAllPhotosTitle,
                      description: _deleteAllPhotosContent,
                      firstButtonText: _deleteAllPhotosFirstButtonText,
                      secondButtonText: _deleteAllPhotosSecondButtonText,
                      onFirstButtonClick: () async {
                        if (context.read<PhotoProvider>().allPhotosList.isEmpty) {
                          Helper().getMessage(
                            message: _anyPhoto,
                            status: StatusEnum.WARNING,
                          );
                        } else {
                          await Helper().getDialog(
                            message: _deleteAllPhotosMessageAll,
                            withTimer: true,
                            onAccept: () async {
                              context.read<PhotoProvider>().deletePhotoList();

                              Helper().getMessage(
                                message: _allPhotosDownloaded,
                                icon: Icons.delete,
                              );

                              Navigator.pushAndRemoveUntil(
                                context,
                                MaterialPageRoute(builder: (context) => const NavBarAndPages(isFirst: false)),
                                (route) => false,
                              );
                            },
                          );
                        }
                      },
                      onSecondButtonClick: () async {
                        late final List<Photo> allPhotos = context.read<PhotoProvider>().allPhotosList.map((e) => e.photos).expand((element) => element).toList();
                        final List<Photo> allImportedPhotos = allPhotos.where((element) => element.isImported!).toList();

                        if (context.read<PhotoProvider>().allPhotosList.isEmpty) {
                          Helper().getMessage(
                            message: _anyPhoto,
                            status: StatusEnum.WARNING,
                          );
                        } else if (allImportedPhotos.isEmpty) {
                          Helper().getMessage(
                            message: _anyImportedPhoto,
                            status: StatusEnum.WARNING,
                          );
                        } else {
                          await Helper().getDialog(
                            message: _deleteAllPhotosMessageImported,
                            withTimer: true,
                            onAccept: () {
                              context.read<PhotoProvider>().deletePhotoList(isImported: true);
                              Navigator.pop(context);

                              Helper().getMessage(
                                message: _deleteImportedPhotos,
                                icon: Icons.delete,
                              );
                            },
                          );
                        }
                      },
                    ),
                    SizedBox(height: 0.02.sh),
                  ],
                ),
              ),
            ),
            if (isLoadingPhotos) ...[
              ModalBarrier(
                color: AppColors.black.withValues(alpha: 0.5),
                dismissible: false,
              ),
              Center(
                child: CircularProgressIndicator(
                  backgroundColor: AppColors.white,
                  color: AppColors.main,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

class SliderLabelWidget extends StatefulWidget {
  const SliderLabelWidget({
    super.key,
    required this.labels,
    required this.header,
    required this.activeIndex,
    required this.onChanged,
  });

  final List<String> labels;
  final String header;
  final int activeIndex;
  final ValueChanged<int> onChanged;

  @override
  State<SliderLabelWidget> createState() => SliderLabelWidgetState();
}

class SliderLabelWidgetState extends State<SliderLabelWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Text(
                '${widget.header}: ${widget.labels[widget.activeIndex]}', // Display the selected value here
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            ),
            const Spacer(),
          ],
        ),
        SfSliderTheme(
          data: SfSliderThemeData(
            activeLabelStyle: TextStyle(
              color: AppColors.text,
              fontSize: 12,
            ),
            inactiveLabelStyle: const TextStyle(
              color: AppColors.dirtyWhite,
              fontSize: 12,
            ),
            labelOffset: const Offset(0, 10),
            thumbColor: AppColors.white,
            overlayColor: AppColors.text.withValues(alpha: 0.1),
          ),
          child: SfSlider(
            inactiveColor: AppColors.grey,
            min: 0,
            max: widget.labels.length - 1.0,
            value: widget.activeIndex.toDouble(),
            interval: 1,
            showLabels: true,
            labelFormatterCallback: (dynamic actualValue, String formattedText) {
              return labelFormatterCallback(widget.labels, actualValue);
            },
            onChanged: (dynamic value) async {
              int activeIndex = value.toInt();
              setState(() {
                handleSliderChange(value, activeIndex);
              });
            },
          ),
        ),
      ],
    );
  }

  void handleSliderChange(double value, int activeIndex) async {
    if (!isPremium! && widget.labels[value.toInt()] == '1080p') {
      Helper().mustPremiumDialog(context);

      activeIndex = widget.labels.indexOf('720p');
    }
    widget.onChanged(activeIndex);
  }

  String labelFormatterCallback(List<String> labels, dynamic actualValue) {
    for (int i = 0; i < labels.length; i++) {
      if (actualValue == i) {
        return labels[i];
      }
    }
    return actualValue.toString();
  }
}

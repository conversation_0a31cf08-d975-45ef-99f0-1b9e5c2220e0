import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PhotoCountWidget extends StatelessWidget {
  PhotoCountWidget({
    super.key,
  });

  // Text
  final String photoNumberText = LocaleKeys.Home_photo.tr();

  @override
  Widget build(BuildContext context) {
    final int photoCount = context.read<PhotoProvider>().allPhotosList.expand((element) => element.photos).length;

    return Text(
      '$photoCount $photoNumberText',
      style: const TextStyle(
        fontSize: 17,
      ),
    );
  }
}

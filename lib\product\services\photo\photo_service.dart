// ignore_for_file: file_names

import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:hive_flutter/hive_flutter.dart';

class PhotoService {
  static const String _boxName = 'OneDayPhotos';

  Future<Box<OneDayPhotos>> get _box async {
    return await Hive.openBox<OneDayPhotos>(_boxName);
  }

  // Functions ------------------------------
  // Get All
  Future<List<OneDayPhotos>> getAllPhotosInHive() async {
    final box = await _box;
    return box.values.toList();
  }

  // Add
  Future<void> addOnePhotoDay(OneDayPhotos oneDayPhoto) async {
    final box = await _box;
    await box.add(oneDayPhoto);
  }

  // Delete All
  Future<void> deleteAllPhotos() async {
    final box = await _box;
    await box.clear();
  }

  // Sort by date
  Future<void> updateList(List<OneDayPhotos> newAllPhotosList) async {
    final box = await _box;
    await box.clear();
    for (var oneDayPhotos in newAllPhotosList) {
      await box.add(oneDayPhotos);
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/streak_page.dart';
import 'package:facelog/Pages/Produce/Video/Service/ffmpeg_debug_service.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/notification_services.dart';
import 'package:facelog/product/services/streak_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DebugWidgets {
  static Widget buildDebugButtons({
    required BuildContext context,
    required PhotoProvider photoProvider,
    required bool isLoadingPhotos,
    required bool testIsLoading,
    required Function(bool) setLoadingPhotos,
    required Function(bool) setTestIsLoading,
    required Future<void> Function() onImportUncroppedPhotos,
    required Future<void> Function({
      required String qualityIndex,
      required String videoLength,
    }) onTestRender,
    required String Function() getQualityIndex,
    required String Function() getVideoLength,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      padding: EdgeInsets.all(40.w),
      child: Column(
        children: [
          Wrap(
            spacing: 16.w,
            runSpacing: 16.h,
            alignment: WrapAlignment.center,
            children: [
              _buildDebugButton(
                icon: Icons.delete_outline,
                tooltip: 'Delete All',
                onPressed: () async {
                  await photoProvider.deletePhotoList();
                  StreakService().deleteAll();
                },
                heroTag: "deleteAll-Test",
              ),
              _buildDebugButton(
                icon: Icons.add_photo_alternate_outlined,
                tooltip: 'Load Examples',
                onPressed: () async {
                  setLoadingPhotos(true);
                  await Helper().loadExamplePhotos(context);
                  setLoadingPhotos(false);
                },
                heroTag: "loadExample",
              ),
              _buildDebugButton(
                icon: testIsLoading ? Icons.hourglass_top : Icons.video_camera_front_outlined,
                tooltip: 'Crop Test',
                onPressed: () async {
                  if (testIsLoading) return;

                  setTestIsLoading(true);

                  // Test crop loading sayfasına git
                  await onTestRender(
                    qualityIndex: getQualityIndex(),
                    videoLength: getVideoLength(),
                  );

                  setTestIsLoading(false);
                },
                heroTag: "faceAlignTest",
              ),
              _buildDebugButton(
                icon: Icons.notifications_outlined,
                tooltip: 'Test Notification',
                onPressed: () async {
                  NotificationServices().pushNotification(
                    RemoteMessage(
                      notification: RemoteNotification(
                        title: context.locale == const Locale('tr', 'TR') ? '⚠️ Fotoğraf Zamanı ' : '⚠️Create Yourself!',
                        body: context.locale == const Locale('tr', 'TR') ? 'Bugüne dair bir anı bırakmayı unutma!' : 'Don\'t forget to leave a memory of today!',
                      ),
                    ),
                  );
                },
                heroTag: "notification",
              ),
              _buildDebugButton(
                icon: Icons.message_outlined,
                tooltip: 'Test Message',
                onPressed: () async {
                  Helper().getMessage(
                    message: "This is a test message.",
                    icon: Icons.message,
                  );
                },
                heroTag: "message",
              ),
              _buildDebugButton(
                icon: Icons.timeline_outlined,
                tooltip: 'Streak Page',
                onPressed: () async {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const StreakPage(),
                    ),
                  );
                },
                heroTag: "streakPage",
              ),
              _buildDebugButton(
                icon: Icons.smart_display_outlined,
                tooltip: 'FFmpeg Test',
                onPressed: () async {
                  try {
                    Helper().getMessage(
                      message: "FFmpeg debug test başlatılıyor...",
                      icon: Icons.video_call,
                    );

                    await FFmpegDebugService().testFFmpegVideoExport(context);

                    Helper().getMessage(
                      message: "FFmpeg debug test tamamlandı! Cache'e kaydedildi.",
                      icon: Icons.check_circle,
                    );
                  } catch (e) {
                    Helper().getMessage(
                      message: "FFmpeg debug test hatası: $e",
                      icon: Icons.error,
                    );
                  }
                },
                heroTag: "ffmpegDebugTest",
              ),
              _buildDebugButton(
                icon: Icons.cleaning_services_outlined,
                tooltip: 'Clear Debug Files',
                onPressed: () async {
                  await FFmpegDebugService().clearDebugFiles();
                  Helper().getMessage(
                    message: "Debug dosyaları temizlendi.",
                    icon: Icons.delete_sweep,
                  );
                },
                heroTag: "clearDebugFiles",
              ),
              _buildDebugButton(
                icon: Icons.folder_open_outlined,
                tooltip: 'Import Uncropped Photos',
                onPressed: onImportUncroppedPhotos,
                heroTag: "importUncropped",
              ),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildDebugButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    required String heroTag,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: 140.w,
            height: 140.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.main.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              size: 25,
              color: AppColors.main,
            ),
          ),
        ),
      ),
    );
  }

  static Widget buildPremiumDebugButton({
    required bool isPremium,
    required Function(bool) onPremiumChanged,
  }) {
    return GestureDetector(
      onTap: () {
        onPremiumChanged(!isPremium);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        color: AppColors.transparent,
        child: Icon(
          Icons.workspace_premium_outlined,
          size: 24,
          color: isPremium ? AppColors.main : AppColors.text,
        ),
      ),
    );
  }
}

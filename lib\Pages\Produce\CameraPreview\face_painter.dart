import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

class FacePainter extends CustomPainter {
  final Face? face;

  FacePainter({
    this.face,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = AppColors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    if (face != null) {
      var newBoxDivideed = face!.boundingBox;
      // TODO: burada orannnn vermek responsive değil. Ancak burası zaten kullanıcıya gösterilmediği için şimdilik sorun değil
      // fotoğrafın gösterildiği kısma göre fotoğrafın gerçek çözünürlüğüne göre oran verilmeli
      const screenSizeRatio = 2.95;

      newBoxDivideed = Rect.fromLTWH(
        newBoxDivideed.left / screenSizeRatio.toDouble(),
        newBoxDivideed.top / screenSizeRatio.toDouble(),
        newBoxDivideed.width / screenSizeRatio.toDouble(),
        newBoxDivideed.height / screenSizeRatio.toDouble(),
      );

      canvas.drawRect(
        newBoxDivideed,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

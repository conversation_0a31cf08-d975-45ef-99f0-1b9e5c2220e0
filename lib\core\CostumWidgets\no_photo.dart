import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';

final class AnyPhotosWidget extends StatelessWidget {
  const AnyPhotosWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: const Text(
        LocaleKeys.Home_NoPhotos,
        style: TextStyle(
          color: AppColors.grey,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ).tr(),
    );
  }
}

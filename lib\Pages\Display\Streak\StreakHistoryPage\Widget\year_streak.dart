import 'package:facelog/Pages/Display/Streak/StreakHistoryPage/Widget/year_day_dot.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/models/streak_model.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/material.dart';

class StreakYear extends StatelessWidget {
  final int year;

  const StreakYear({
    super.key,
    required this.year,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Year Title
        Text(
          year.toString(),
          style: const TextStyle(
            fontSize: 50,
            fontWeight: FontWeight.bold,
          ),
        ),
        // Year Days
        Container(
          padding: const EdgeInsets.all(8),
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.text),
            borderRadius: BorderRadius.circular(8),
          ),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(0),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 25,
              crossAxisSpacing: 1,
              mainAxisSpacing: 1,
            ),
            itemCount: Helper().daysBetween(DateTime(year, 1, 1), DateTime(year + 1, 1, 1)),
            itemBuilder: (context, index) {
              final date = DateTime(year, 1, 1).add(Duration(days: index));
              final streak = streakList.firstWhere(
                (streak) => Helper().isSameDay(streak.date, date),
                orElse: () => StreakModel(date: date, status: StreakStatus.empty),
              );
              return AnnualStreakCalendarDot(status: streak.status);
            },
          ),
        ),
      ],
    );
  }
}

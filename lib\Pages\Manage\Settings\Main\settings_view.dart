import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Payment/payment_page.dart';
import 'package:facelog/Pages/Manage/Settings/Items/AdvacedSettingsPage/advaced_settings_page.dart';
import 'package:facelog/Pages/Manage/Settings/Items/ThemeAndColor/theme_switch.dart';
import 'package:facelog/Pages/Manage/Settings/Items/streak_history_item.dart';
import 'package:facelog/Pages/Manage/Settings/Main/components/buy_me_coffe.dart';
import 'package:facelog/Pages/Manage/Settings/Main/components/notification_widget.dart';
import 'package:facelog/Pages/Manage/Settings/Main/components/social_media_icons.dart';
import 'package:facelog/Pages/Manage/Settings/Main/components/supporters_dialog_view.dart';
import 'package:facelog/core/CostumWidgets/select_orientation_view.dart';
import 'package:facelog/Pages/Manage/Settings/Main/components/language_pop.dart';
import 'package:facelog/Pages/Manage/Settings/Items/TutorialPage/tutorial_page.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/Pages/Manage/Settings/Items/About%20Page/about_page.dart';
import 'package:facelog/Pages/Manage/Settings/Items/HelpPage/contact_us_page.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/storage_page.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/theme_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  final String link = 'https://play.google.com/store/apps/details?id=com.app.facelog';

  @override
  Widget build(BuildContext context) {
    context.watch<ThemeProvider>().themeMode;

    return Scaffold(
      appBar: AppBar(
        forceMaterialTransparency: true,
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) => const PaymentPage(),
                  transitionsBuilder: (context, animation, secondaryAnimation, child) {
                    const begin = Offset(0.0, 1.0);
                    const end = Offset.zero;
                    const curve = Curves.fastOutSlowIn;

                    var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

                    return SlideTransition(
                      position: animation.drive(tween),
                      child: child,
                    );
                  },
                ),
              );
            },
            child: Container(
              color: AppColors.transparent,
              padding: const EdgeInsets.all(15),
              child: Icon(
                Icons.workspace_premium_outlined,
                color: AppColors.main,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              LogService().againTutorialButton();

              Navigator.pushReplacement(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) => const TutorialPage(),
                  transitionsBuilder: (context, animation, secondaryAnimation, child) {
                    const begin = Offset(0.0, 1.0);
                    const end = Offset.zero;
                    const curve = Curves.fastOutSlowIn;

                    var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

                    return SlideTransition(
                      position: animation.drive(tween),
                      child: child,
                    );
                  },
                ),
              );
            },
            child: Container(
              color: AppColors.transparent,
              padding: const EdgeInsets.all(15),
              child: Icon(
                Icons.question_mark,
                color: AppColors.lightMain,
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          children: [
            header(
              title: LocaleKeys.SettingsPage_HeaderSettings.tr(),
              context: context,
            ),
            const NotificationWidget(),
            const ThemeSwitchTile(),
            settingsListItem(
              context: context,
              subTitle: LocaleKeys.SettingsPage_SettingsTiles_Language_Header.tr(),
              iconType: Icons.translate,
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) => const LanguageSelectionPopup(),
                );
              },
            ),
            settingsListItem(
              context: context,
              subTitle: LocaleKeys.SettingsPage_SettingsTiles_Storage_AppBarTitle.tr(),
              iconType: Icons.storage,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const StoragePage(),
                  ),
                );
              },
            ),
            settingsListItem(
              context: context,
              subTitle: LocaleKeys.SettingsPage_SettingsTiles_Orientation_Title.tr(),
              iconType: Icons.aspect_ratio,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SelectOrientationPage(
                      isTutorialPage: false,
                    ),
                  ),
                );
              },
            ),
            settingsListItem(
              isLast: true,
              context: context,
              // TODO
              subTitle: "Advaced Settings",
              iconType: Icons.accessible_forward_rounded,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AdvacedSettingsPage(),
                  ),
                );
              },
            ),
            header(
              title: LocaleKeys.SettingsPage_HeaderAbout.tr(),
              context: context,
            ),
            const StreakHistoryItem(),
            settingsListItem(
              context: context,
              subTitle: LocaleKeys.SettingsPage_SettingsTiles_Supporters.tr(),
              iconType: Icons.sports_kabaddi_outlined,
              onTap: () {
                supportersBoard(context);
              },
            ),
            if (!isRated)
              settingsListItem(
                context: context,
                subTitle: LocaleKeys.SettingsPage_SettingsTiles_RateUsBottomSheet_Header.tr(),
                iconType: Icons.star_border,
                onTap: () {
                  Helper().showRateUsBottomSheet(context);

                  LogService().rateUsButton();
                },
              ),
            settingsListItem(
              context: context,
              subTitle: LocaleKeys.SettingsPage_SettingsTiles_ShareUs.tr(),
              iconType: Icons.share,
              onTap: () async {
                await Clipboard.setData(ClipboardData(text: link));
                SharePlus.instance.share(ShareParams(
                  text: '$link \n ${LocaleKeys.Dialog_ShareFacelog.tr()}',
                ));

                LogService().shareFacelog();
              },
            ),
            settingsListItem(
              context: context,
              subTitle: LocaleKeys.SettingsPage_SettingsTiles_Contact_ContactUsHeader.tr(),
              iconType: Icons.mail,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ContactUsPage(),
                  ),
                );
              },
            ),
            settingsListItem(
              isLast: true,
              context: context,
              subTitle: LocaleKeys.SettingsPage_SettingsTiles_About_Header.tr(),
              iconType: Icons.info_rounded,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AboutPage(),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            const Center(
              child: PackageInfoWidget(),
            ),
            SizedBox(height: 0.03.sh),
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SocialMediaIcons(link: 'https://www.instagram.com/facelogapp/', iconType: FontAwesomeIcons.instagram, name: 'Instagram'),
                SocialMediaIcons(link: 'https://www.facebook.com/facelogapp/', iconType: FontAwesomeIcons.facebook, name: 'Facebook'),
                SocialMediaIcons(link: 'https://www.tiktok.com/@facelogapp/', iconType: FontAwesomeIcons.tiktok, name: 'TikTok'),
              ],
            ),
            const SizedBox(height: 10),
            const BuyMeACoffee(),
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }

  Future<void> supportersBoard(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => const SupportersAlertDialog(),
    );
  }

  Widget header({
    required String title,
    required BuildContext context,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 10.0),
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20.0,
        ),
      ),
    );
  }

  Widget settingsListItem({
    required BuildContext context,
    required String subTitle,
    required IconData iconType,
    VoidCallback? onTap,
    bool isLast = false,
    bool isFirst = false,
  }) {
    return Ink(
      decoration: BoxDecoration(
        color: AppColors.panelBackground,
        borderRadius: isLast
            ? AppColors.borderRadiusBottom
            : isFirst
                ? AppColors.borderRadiusTop
                : null,
      ),
      child: InkWell(
        borderRadius: isLast
            ? AppColors.borderRadiusBottom
            : isFirst
                ? AppColors.borderRadiusTop
                : null,
        onTap: onTap ??
            () {
              // İsteğe bağlı son öğe tıklama işlemleri
              // TODO: Burayı düzenleyelim.
            },
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 10,
          ),
          child: Row(
            children: [
              Icon(
                iconType,
              ),
              const SizedBox(width: 10),
              Text(
                subTitle,
                style: TextStyle(
                  fontSize: 45.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PackageInfoWidget extends StatefulWidget {
  const PackageInfoWidget({
    super.key,
  });

  @override
  State<PackageInfoWidget> createState() => _PackageInfoWidgetState();
}

class _PackageInfoWidgetState extends State<PackageInfoWidget> {
  String _version = '';
  String _versionCode = '';

  @override
  void initState() {
    super.initState();
    _loadVersionInfo();
  }

  Future<void> _loadVersionInfo() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();

    setState(() {
      _version = packageInfo.version;
      _versionCode = packageInfo.buildNumber;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      // write from package info plus
      kDebugMode ? '$_version+$_versionCode' : _version,
      style: const TextStyle(
        color: AppColors.dirtyWhite,
      ),
    );
  }
}

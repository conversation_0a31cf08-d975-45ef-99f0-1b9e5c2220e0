import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:facelog/product/services/notification_services.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:firebase_app_installations/firebase_app_installations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

bool isFist = true;

Future<void> firebaseMessageListen() async {
  if (isConnected) {
    // Firebase init
    if (isFist) {
      isFist = false;

      FirebaseFirestore.instance.settings = const Settings(persistenceEnabled: true);
      await FirebaseMessaging.instance.requestPermission();

      // get token
      if (kDebugMode) {
        final String? token = await FirebaseMessaging.instance.getToken();
        final String id = await FirebaseInstallations.instance.getId();
        String token2 = await FirebaseInstallations.instance.getToken();
        debugPrint("""
          Token: $token
          Id: $id
          Token2: $token2
        """);
        if (token != null) {
          await Clipboard.setData(ClipboardData(text: token));
        }
      }
    }

    // ! Eğer mesaja tıklayınca belli bir şey yaptırmak istersek buralar kullanılacak
    //uygulama önde açıken bildirim geldiğinde
    FirebaseMessaging.onMessage.listen((notification) {
      NotificationServices().pushNotification(notification);
    });
    // //uygulama arkada açıkken bildirime tıklanınca yönlendirmesi için
    // FirebaseMessaging.onMessageOpenedApp.listen((notification) {
    // });
    // //uygulama kapalıysa bildirime tıklanınca yönlendirmesi için
    // FirebaseMessaging.instance.getInitialMessage().then(
    //   (notification) {
    //     if (notification != null) {
    //     }
    //   },
    // );
  }
}

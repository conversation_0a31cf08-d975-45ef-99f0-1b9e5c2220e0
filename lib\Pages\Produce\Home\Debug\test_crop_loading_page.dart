// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/Pages/Produce/Home/Debug/debug_service.dart';
import 'package:facelog/Pages/Produce/Video/Preview/preview_video_view.dart';

class TestCropLoadingPage extends StatefulWidget {
  final String qualityIndex;
  final String videoLength;

  const TestCropLoadingPage({
    super.key,
    required this.qualityIndex,
    required this.videoLength,
  });

  @override
  State<TestCropLoadingPage> createState() => _TestCropLoadingPageState();
}

class _TestCropLoadingPageState extends State<TestCropLoadingPage> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  String currentStatus = "Fotoğraflar hazırlanıyor...";
  String progressText = "0/0";
  bool isCompleted = false;

  @override
  void initState() {
    super.initState();

    // Rotation animasyonu
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // Pulse animasyonu
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Test crop işlemini başlat
    _startTestCropProcess();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  /// Test crop işlemini başlatır ve progress'i takip eder
  Future<void> _startTestCropProcess() async {
    try {
      // Timeout ile gerçek test crop işlemini çağır (maksimum 90 saniye)
      await DebugService.performTestCropAndVideo(
        context: context,
        qualityIndex: widget.qualityIndex,
        videoLength: widget.videoLength,
        onStatusUpdate: (status) {
          if (mounted) {
            setState(() {
              currentStatus = status;
            });
          }
        },
        onProgressUpdate: (progress) {
          if (mounted) {
            setState(() {
              progressText = progress;
            });
          }
        },
      ).timeout(const Duration(seconds: 90));

      // İşlem tamamlandığında kontrol et
      if (mounted) {
        setState(() {
          currentStatus = "Tamamlandı!";
          isCompleted = true;
        });

        // 1 saniye bekle, sonra video preview sayfasına git
        await Future.delayed(const Duration(milliseconds: 1000));

        if (mounted && Navigator.canPop(context)) {
          // Video preview sayfasına yönlendir (null geçerek faceLogExportVideo path'ini kullanmasını sağla)
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPreviewPage(savedVideoModel: null),
            ),
          );
        }
      }
    } on TimeoutException {
      if (mounted) {
        setState(() {
          currentStatus = "İşlem çok uzun sürdü, iptal ediliyor...";
        });

        await Future.delayed(const Duration(seconds: 2));
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          currentStatus = "Hata oluştu: $e";
        });

        await Future.delayed(const Duration(seconds: 3));
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.main,
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.main,
                AppColors.main.withOpacity(0.8),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Geri buton (sadece hata durumunda)
              if (!isCompleted && currentStatus.contains("Hata"))
                Positioned(
                  top: 40.h,
                  left: 16.w,
                  child: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                      size: 24.sp,
                    ),
                  ),
                ),

              const Spacer(),

              // Ana loading animasyonu
              Column(
                children: [
                  // Dönen ikon
                  AnimatedBuilder(
                    animation: _rotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _rotationAnimation.value * 2 * math.pi,
                        child: AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _pulseAnimation.value,
                              child: Container(
                                width: 120.w,
                                height: 120.w,
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 2,
                                  ),
                                ),
                                child: Icon(
                                  isCompleted ? Icons.check_circle : Icons.content_cut,
                                  size: 60.sp,
                                  color: Colors.white,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),

                  SizedBox(height: 40.h),

                  // Progress text
                  Text(
                    progressText,
                    style: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),

                  SizedBox(height: 16.h),

                  // Status text
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 32.w),
                    child: Text(
                      currentStatus,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: Colors.white.withOpacity(0.9),
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),

              const Spacer(),

              // İptal butonu (işlem devam ederken)
              if (!isCompleted && !currentStatus.contains("Hata") && !currentStatus.contains("Tamamlandı"))
                Padding(
                  padding: EdgeInsets.only(bottom: 20.h),
                  child: TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
                      backgroundColor: Colors.white.withOpacity(0.2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: Text(
                      "İptal",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

              // Alt bilgi
              Padding(
                padding: EdgeInsets.only(bottom: 40.h),
                child: Text(
                  "Crop Test Modu\nOrijinal fotoğraflar değiştirilmez",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withOpacity(0.7),
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Supporter {
  String name;
  int amount;

  Supporter({
    required this.name,
    required this.amount,
  });

  Supporter.fromJson(Map<String, Object?> json)
      : this(
          name: json['name']! as String,
          amount: json['amount']! as int,
        );

  Supporter copyWith({
    String? name,
    int? amount,
  }) {
    return Supporter(
      name: name ?? this.name,
      amount: amount ?? this.amount,
    );
  }

  Map<String, Object?> toJson() {
    return {
      'name': name,
      'amount': amount,
    };
  }
}

import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/camera_mixin.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_statistics_container.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image/image.dart' as img;
import 'package:image_editor/image_editor.dart';
import 'dart:ui' as ui;

bool isLoadingProgress = false;
Face? currentFace;
Offset? photoCenter;

// Debug modunda crop fonksiyonu seçimi için
enum CropVersion { v1, v2, v3, v4, v5, v6, v7, v8, v9, v10 }

CropVersion selectedCropVersion = CropVersion.v1;

final FaceDetectorOptions options = FaceDetectorOptions(enableLandmarks: true, enableTracking: false, enableClassification: false, enableContours: false, performanceMode: FaceDetectorMode.fast);
final FaceDetector faceDetector = FaceDetector(options: options);

/// Landscape modda çekilen fotoğrafları döndürür
Future<void> _rotateImageIfLandscape({required String imagePath, required int angle}) async {
  if (isLandscape) {
    final File file = File(imagePath);
    Uint8List? imageBytes = await file.readAsBytes();

    final ImageEditorOption option = ImageEditorOption();
    option.addOption(RotateOption(angle));

    imageBytes = await ImageEditor.editImage(image: imageBytes, imageEditorOption: option);

    await file.writeAsBytes(imageBytes!);
  }
}

Future<void> photoDetectFace({String? photoPath}) async {
  try {
    isLoadingProgress = true;

    // Landscape modda çekilen fotoğrafları düzelt (face detection için)
    if (photoPath == null) {
      await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: -90);
    }

    late final img.Image decodedImage;
    late final Uint8List imageBytes;

    // Orijinal kaliteyi korumak için sıkıştırma yapmıyoruz
    final File imageFile = File(photoPath ?? cameraPhoto!.path);
    imageBytes = await imageFile.readAsBytes();
    decodedImage = img.decodeImage(imageBytes)!;
    photoCenter = Offset(decodedImage.width / 2, decodedImage.height / 2);

    final List<Face> faces = await faceDetector.processImage(InputImage.fromFile(File(photoPath ?? cameraPhoto!.path)));

    if (faces.isNotEmpty) {
      if (faces.length > 1) {
        // Birden fazla yüz varsa, fotoğrafın merkezine en yakın olanını seç
        double minDistance = double.infinity;

        for (var face in faces) {
          double distance = (face.boundingBox.center - photoCenter!).distance;
          if (distance < minDistance) {
            minDistance = distance;
            currentFace = face;
          }
        }
      } else {
        currentFace = faces.first;
      }

      await cropPhoto(faceDetails: currentFace, photoPath: photoPath ?? cameraPhoto!.path, imageBytes: imageBytes, decodedImage: decodedImage);

      // Fotoğrafı orijinal durumuna döndür
      if (photoPath == null) {
        await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: 90);
      }
    } else {
      if (photoPath == null) {
        Helper().getMessage(message: LocaleKeys.CameraPages_Camera_FaceDetection_FaceNotDetected.tr(), status: StatusEnum.WARNING);
      }

      currentFace = null;

      // Fotoğrafı orijinal durumuna döndür
      if (photoPath == null) {
        await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: 90);
      }
    }

    faceDetector.close();

    if (currentFace != null) {
      calculateTotalPercentage();
    } else {
      totalAccuracy = null;
    }

    isLoadingProgress = false;
  } catch (e) {
    if (photoPath == null) {
      Helper().getMessage(message: LocaleKeys.CameraPages_Camera_FaceDetection_FaceDetectionError.tr(), status: StatusEnum.WARNING);
    }
    debugPrint(e.toString());
    faceDetector.close();
    isLoadingProgress = false;
  }
}

Future<void> cropPhoto({required Face? faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  croppedPhotoIndexForLoading.value++;
  if (faceDetails == null) return;

  try {
    late Uint8List croppedImage;

    // Seçilen crop versiyonuna göre farklı fonksiyonu çağır
    switch (selectedCropVersion) {
      case CropVersion.v1:
        croppedImage = await _processCropV1(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v2:
        croppedImage = await _processCropV2(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v3:
        croppedImage = await _processCropV3(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v4:
        croppedImage = await _processCropV4(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v5:
        croppedImage = await _processCropV5(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v6:
        croppedImage = await _processCropV6(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v7:
        croppedImage = await _processCropV7(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v8:
        croppedImage = await _processCropV8(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v9:
        croppedImage = await _processCropV9(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v10:
        croppedImage = await _processCropV10(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
    }

    // Kırpılan fotoğrafı dosyaya kaydet
    await File(photoPath).writeAsBytes(croppedImage);
  } catch (e) {
    Helper().getMessage(message: LocaleKeys.CameraPages_Camera_FaceDetection_PhotoNotCropped.tr(), status: StatusEnum.WARNING);
  }
}

// V1: Göz merkezli - Hassas hizalama, geniş crop alanı
Future<Uint8List> _processCropV1({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  // Gözlerin konumunu al
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Yüz tespit edilemedi.");
  }

  // Gözler arasındaki mesafeyi hesapla
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));

  // Gözlerin orta noktasını bul
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // Gözlerin eğim açısını hesapla (derece cinsinden) - gözleri düz yapmak için
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // Standart gözler arası mesafe (hedef değer)
  const double standardEyeDistance = 200.0;

  // Ölçeklendirme oranı - gözler arası mesafeyi standardize et
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;

  // Standart kırpma alanı - gözler arası mesafenin katları
  const double cropMultiplier = 4.0; // Gözler arası mesafenin 4 katı alan
  final double standardCropSize = standardEyeDistance * cropMultiplier;

  // Hedef canvas boyutu (kare)
  final int targetCanvasSize = standardCropSize.toInt();

  // Kırpma alanını göz merkezine göre hesapla (daha geniş margin ile)
  final double margin = standardCropSize * 1.2; // %120 margin - daha geniş alan
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // Kırpma alanının sınırları kontrol et
  if (cropLeft + cropWidth > decodedImage.width) {
    cropWidth = decodedImage.width - cropLeft;
  }
  if (cropTop + cropHeight > decodedImage.height) {
    cropHeight = decodedImage.height - cropTop;
  }

  // ImageEditorOption oluştur
  final option = ImageEditorOption();

  // 1. Önce kırpma işlemi - geniş alan al
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));

  // 2. Boyutlandırma - gözler arası mesafeyi standardize et
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();

  option.addOption(ScaleOption(scaledWidth, scaledHeight));

  // 3. Döndürme işlemi - gözleri tam düz yap
  option.addOption(
    RotateOption(
      -angle.toInt(), // Gözleri düz hale getir
    ),
  );

  // İşlemleri uygula
  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;

  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

/// Kırpılan fotoğrafı siyah arka plan üzerinde ortalar
Future<Uint8List> mergeBackground({required Uint8List editedImageBytes, required Size canvasSize}) async {
  final ui.Image inputImage = await decodeImageFromList(editedImageBytes);

  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);

  // Siyah arka plan çiz
  canvas.drawRect(Offset.zero & canvasSize, Paint()..color = Colors.black);

  // Resmi yerleştir
  double width = inputImage.width.toDouble();
  double height = inputImage.height.toDouble();
  double x = (canvasSize.width - width) / 2;
  double y = (canvasSize.height - height) / 2;

  // Orijinal görüntüyü ortala ve oranını koruyarak çiz
  canvas.drawImageRect(inputImage, Rect.fromLTWH(0, 0, inputImage.width.toDouble(), inputImage.height.toDouble()), Rect.fromLTWH(x, y, width, height), Paint());

  final picture = recorder.endRecording();
  final finalImage = await picture.toImage(canvasSize.width.toInt(), canvasSize.height.toInt());

  // ui.Image'i img.Image'e dönüştür
  final byteData = await finalImage.toByteData(format: ui.ImageByteFormat.rawRgba);
  final pixels = byteData!.buffer.asUint8List();
  final image = img.Image.fromBytes(width: finalImage.width, height: finalImage.height, bytes: pixels.buffer, numChannels: 4);

  // JPEG olarak kodla
  final jpegBytes = img.encodeJpg(image, quality: 90);

  return Uint8List.fromList(jpegBytes);
}

// V2: V1 temel + daha geniş margin (%150)
Future<Uint8List> _processCropV2({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Daha geniş margin - %150
  final double margin = standardCropSize * 1.5;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V3: V1 temel + çok geniş margin (%200)
Future<Uint8List> _processCropV3({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Çok geniş margin - %200
  final double margin = standardCropSize * 2.0;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V4: V1 temel + adaptif margin (çözünürlüğe göre)
Future<Uint8List> _processCropV4({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Adaptif margin - fotoğraf boyutuna göre
  final double imageSize = math.sqrt(decodedImage.width * decodedImage.height);
  double marginMultiplier;
  if (imageSize > 3000) {
    // Yüksek çözünürlük
    marginMultiplier = 2.5;
  } else if (imageSize > 2000) {
    // Orta çözünürlük
    marginMultiplier = 1.8;
  } else {
    // Düşük çözünürlük
    marginMultiplier = 1.2;
  }

  final double margin = standardCropSize * marginMultiplier;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V5: V1 temel + göz mesafesine göre adaptif margin
Future<Uint8List> _processCropV5({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Göz mesafesine göre adaptif margin
  double marginMultiplier;
  if (eyeDistance < 80) {
    // Küçük yüzler
    marginMultiplier = 3.0;
  } else if (eyeDistance < 150) {
    // Orta boyut yüzler
    marginMultiplier = 2.0;
  } else {
    // Büyük yüzler
    marginMultiplier = 1.5;
  }

  final double margin = standardCropSize * marginMultiplier;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V6: V1 temel + sabit geniş margin (%250)
Future<Uint8List> _processCropV6({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Sabit çok geniş margin - %250
  final double margin = standardCropSize * 2.5;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V7: V1 temel + sabit ultra geniş margin (%300)
Future<Uint8List> _processCropV7({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Ultra geniş margin - %300
  final double margin = standardCropSize * 3.0;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V8: V1 temel + hibrit margin (çözünürlük + göz mesafesi)
Future<Uint8List> _processCropV8({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Hibrit margin - hem çözünürlük hem göz mesafesi
  final double imageSize = math.sqrt(decodedImage.width * decodedImage.height);
  double resolutionMultiplier = imageSize > 2500 ? 2.2 : 1.6;
  double eyeMultiplier = eyeDistance < 100 ? 2.5 : 1.8;
  final double marginMultiplier = (resolutionMultiplier + eyeMultiplier) / 2;

  final double margin = standardCropSize * marginMultiplier;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V9: V1 temel + maksimum geniş margin (%400)
Future<Uint8List> _processCropV9({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Maksimum geniş margin - %400
  final double margin = standardCropSize * 4.0;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

// V10: V1 temel + dinamik margin (en akıllı)
Future<Uint8List> _processCropV10({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // V1'in aynı hesaplamaları
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // V1'in standart değerleri
  const double standardEyeDistance = 200.0;
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;
  const double cropMultiplier = 4.0;
  final double standardCropSize = standardEyeDistance * cropMultiplier;
  final int targetCanvasSize = standardCropSize.toInt();

  // Dinamik margin - tüm faktörleri birleştir
  final double imageSize = math.sqrt(decodedImage.width * decodedImage.height);
  double marginMultiplier = 1.2; // Base

  // Çözünürlük faktörü
  if (imageSize > 3500)
    marginMultiplier += 1.5;
  else if (imageSize > 2500)
    marginMultiplier += 1.0;
  else if (imageSize > 1500) marginMultiplier += 0.5;

  // Göz mesafesi faktörü
  if (eyeDistance < 60)
    marginMultiplier += 1.0;
  else if (eyeDistance < 100) marginMultiplier += 0.5;

  final double margin = standardCropSize * marginMultiplier;
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // V1'in aynı işlem sırası
  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();
  option.addOption(ScaleOption(scaledWidth, scaledHeight));
  option.addOption(RotateOption(-angle.toInt()));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

/// Yüz hizalama doğruluğunu hesapla
void calculateTotalPercentage() {
  double accuracyPercentage({required double value, required double acceptableDifference, required double fitValue}) {
    double error = (fitValue - value).abs() / acceptableDifference;
    double percentage = 100 - (error * 10);

    if (percentage > 100) {
      percentage = 100;
    } else if (percentage < 0) {
      percentage = 0;
    }

    return percentage;
  }

  // Yüz rotasyon ve pozisyon değerleri
  final double headRotationDifferanceSide = currentFace!.headEulerAngleZ!;
  final double headRotationDifferanceRightLeft = currentFace!.headEulerAngleY!;
  final double headRotationDifferanceTopBottom = currentFace!.headEulerAngleX!;
  final double boxSizeDifferent = Offset(currentFace!.boundingBox.size.height, currentFace!.boundingBox.size.width).distance;
  final double boxDistanceToCenter = Helper().calculateTwoPointDistance(currentFace!.boundingBox.center, photoCenter!);

  // Kabul edilebilir fark değerleri
  const double acceptableDifferenceSide = 3;
  const double acceptableDifferenceRightLeft = 3;
  const double acceptableDifferenceTopBottom = 2;
  const double acceptableDifferenceSize = 30;
  const double acceptableDifferenceDistance = 30;

  // İdeal değerler
  const double fitValueSide = 0;
  const double fitValueRightLeft = 0;
  const double fitValueTopBottom = -12;
  const double fitValueSize = 950;
  const double fitValueDistance = 0;

  // Doğruluk yüzdelerini hesapla
  final sideRatio = accuracyPercentage(value: headRotationDifferanceSide, acceptableDifference: acceptableDifferenceSide, fitValue: fitValueSide);
  final rightLeftRatio = accuracyPercentage(value: headRotationDifferanceRightLeft, acceptableDifference: acceptableDifferenceRightLeft, fitValue: fitValueRightLeft);
  final topBottomRatio = accuracyPercentage(value: headRotationDifferanceTopBottom, acceptableDifference: acceptableDifferenceTopBottom, fitValue: fitValueTopBottom);
  final sizeRatio = accuracyPercentage(value: boxSizeDifferent, acceptableDifference: acceptableDifferenceSize, fitValue: fitValueSize);
  final distanceRatio = accuracyPercentage(value: boxDistanceToCenter, acceptableDifference: acceptableDifferenceDistance, fitValue: fitValueDistance);

  // Toplam doğruluk yüzdesini hesapla
  totalAccuracy = (sideRatio + rightLeftRatio + topBottomRatio + sizeRatio + distanceRatio) / 5;
}

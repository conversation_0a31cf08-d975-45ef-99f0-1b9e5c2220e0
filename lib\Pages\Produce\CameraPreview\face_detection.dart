import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/camera_mixin.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_statistics_container.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image/image.dart' as img;
import 'package:image_editor/image_editor.dart';
import 'dart:ui' as ui;

bool isLoadingProgress = false;
Face? currentFace;
Offset? photoCenter;

// Debug modunda crop fonksiyonu seçimi için
enum CropVersion { v1, v2, v3, v4, v5, v6, v7, v8, v9, v10 }

CropVersion selectedCropVersion = CropVersion.v1;

final FaceDetectorOptions options = FaceDetectorOptions(enableLandmarks: true, enableTracking: false, enableClassification: false, enableContours: false, performanceMode: FaceDetectorMode.fast);
final FaceDetector faceDetector = FaceDetector(options: options);

/// Landscape modda çekilen fotoğrafları döndürür
Future<void> _rotateImageIfLandscape({required String imagePath, required int angle}) async {
  if (isLandscape) {
    final File file = File(imagePath);
    Uint8List? imageBytes = await file.readAsBytes();

    final ImageEditorOption option = ImageEditorOption();
    option.addOption(RotateOption(angle));

    imageBytes = await ImageEditor.editImage(image: imageBytes, imageEditorOption: option);

    await file.writeAsBytes(imageBytes!);
  }
}

Future<void> photoDetectFace({String? photoPath}) async {
  try {
    isLoadingProgress = true;

    // Landscape modda çekilen fotoğrafları düzelt (face detection için)
    if (photoPath == null) {
      await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: -90);
    }

    late final img.Image decodedImage;
    late final Uint8List imageBytes;

    // Orijinal kaliteyi korumak için sıkıştırma yapmıyoruz
    final File imageFile = File(photoPath ?? cameraPhoto!.path);
    imageBytes = await imageFile.readAsBytes();
    decodedImage = img.decodeImage(imageBytes)!;
    photoCenter = Offset(decodedImage.width / 2, decodedImage.height / 2);

    final List<Face> faces = await faceDetector.processImage(InputImage.fromFile(File(photoPath ?? cameraPhoto!.path)));

    if (faces.isNotEmpty) {
      if (faces.length > 1) {
        // Birden fazla yüz varsa, fotoğrafın merkezine en yakın olanını seç
        double minDistance = double.infinity;

        for (var face in faces) {
          double distance = (face.boundingBox.center - photoCenter!).distance;
          if (distance < minDistance) {
            minDistance = distance;
            currentFace = face;
          }
        }
      } else {
        currentFace = faces.first;
      }

      await cropPhoto(faceDetails: currentFace, photoPath: photoPath ?? cameraPhoto!.path, imageBytes: imageBytes, decodedImage: decodedImage);

      // Fotoğrafı orijinal durumuna döndür
      if (photoPath == null) {
        await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: 90);
      }
    } else {
      if (photoPath == null) {
        Helper().getMessage(message: LocaleKeys.CameraPages_Camera_FaceDetection_FaceNotDetected.tr(), status: StatusEnum.WARNING);
      }

      currentFace = null;

      // Fotoğrafı orijinal durumuna döndür
      if (photoPath == null) {
        await _rotateImageIfLandscape(imagePath: cameraPhoto!.path, angle: 90);
      }
    }

    faceDetector.close();

    if (currentFace != null) {
      calculateTotalPercentage();
    } else {
      totalAccuracy = null;
    }

    isLoadingProgress = false;
  } catch (e) {
    if (photoPath == null) {
      Helper().getMessage(message: LocaleKeys.CameraPages_Camera_FaceDetection_FaceDetectionError.tr(), status: StatusEnum.WARNING);
    }
    debugPrint(e.toString());
    faceDetector.close();
    isLoadingProgress = false;
  }
}

Future<void> cropPhoto({required Face? faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  croppedPhotoIndexForLoading.value++;
  if (faceDetails == null) return;

  try {
    late Uint8List croppedImage;

    // Seçilen crop versiyonuna göre farklı fonksiyonu çağır
    switch (selectedCropVersion) {
      case CropVersion.v1:
        croppedImage = await _processCropV1(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v2:
        croppedImage = await _processCropV2(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v3:
        croppedImage = await _processCropV3(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v4:
        croppedImage = await _processCropV4(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v5:
        croppedImage = await _processCropV5(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v6:
        croppedImage = await _processCropV6(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v7:
        croppedImage = await _processCropV7(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v8:
        croppedImage = await _processCropV8(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v9:
        croppedImage = await _processCropV9(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
      case CropVersion.v10:
        croppedImage = await _processCropV10(faceDetails: faceDetails, photoPath: photoPath, decodedImage: decodedImage, imageBytes: imageBytes);
        break;
    }

    // Kırpılan fotoğrafı dosyaya kaydet
    await File(photoPath).writeAsBytes(croppedImage);
  } catch (e) {
    Helper().getMessage(message: LocaleKeys.CameraPages_Camera_FaceDetection_PhotoNotCropped.tr(), status: StatusEnum.WARNING);
  }
}

// V1: Orijinal crop fonksiyonu - Göz merkezli, standart ölçeklendirme
Future<Uint8List> _processCropV1({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  // Gözlerin konumunu al
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Yüz tespit edilemedi.");
  }

  // Gözler arasındaki mesafeyi hesapla
  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));

  // Gözlerin orta noktasını bul
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // Gözlerin eğim açısını hesapla (derece cinsinden) - gözleri düz yapmak için
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  // Standart gözler arası mesafe (hedef değer)
  const double standardEyeDistance = 200.0;

  // Ölçeklendirme oranı - gözler arası mesafeyi standardize et
  final double eyeScaleRatio = standardEyeDistance / eyeDistance;

  // Standart kırpma alanı - gözler arası mesafenin katları
  const double cropMultiplier = 4.0; // Gözler arası mesafenin 4 katı alan
  final double standardCropSize = standardEyeDistance * cropMultiplier;

  // Hedef canvas boyutu (kare)
  final int targetCanvasSize = standardCropSize.toInt();

  // Kırpma alanını göz merkezine göre hesapla (yeterli margin ile)
  final double margin = standardCropSize * 0.6; // %60 margin
  int cropLeft = math.max(0, (eyeCenter.dx - margin / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - margin / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, margin.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, margin.toInt());

  // Kırpma alanının sınırları kontrol et
  if (cropLeft + cropWidth > decodedImage.width) {
    cropWidth = decodedImage.width - cropLeft;
  }
  if (cropTop + cropHeight > decodedImage.height) {
    cropHeight = decodedImage.height - cropTop;
  }

  // ImageEditorOption oluştur
  final option = ImageEditorOption();

  // 1. Önce kırpma işlemi - geniş alan al
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));

  // 2. Boyutlandırma - gözler arası mesafeyi standardize et
  final int scaledWidth = (cropWidth * eyeScaleRatio).toInt();
  final int scaledHeight = (cropHeight * eyeScaleRatio).toInt();

  option.addOption(ScaleOption(scaledWidth, scaledHeight));

  // 3. Döndürme işlemi - gözleri tam düz yap
  option.addOption(
    RotateOption(
      -angle.toInt(), // Gözleri düz hale getir
    ),
  );

  // İşlemleri uygula
  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;

  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetCanvasSize.toDouble(), targetCanvasSize.toDouble()));

  return editedImageBytes;
}

/// Kırpılan fotoğrafı siyah arka plan üzerinde ortalar
Future<Uint8List> mergeBackground({required Uint8List editedImageBytes, required Size canvasSize}) async {
  final ui.Image inputImage = await decodeImageFromList(editedImageBytes);

  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);

  // Siyah arka plan çiz
  canvas.drawRect(Offset.zero & canvasSize, Paint()..color = Colors.black);

  // Resmi yerleştir
  double width = inputImage.width.toDouble();
  double height = inputImage.height.toDouble();
  double x = (canvasSize.width - width) / 2;
  double y = (canvasSize.height - height) / 2;

  // Orijinal görüntüyü ortala ve oranını koruyarak çiz
  canvas.drawImageRect(inputImage, Rect.fromLTWH(0, 0, inputImage.width.toDouble(), inputImage.height.toDouble()), Rect.fromLTWH(x, y, width, height), Paint());

  final picture = recorder.endRecording();
  final finalImage = await picture.toImage(canvasSize.width.toInt(), canvasSize.height.toInt());

  // ui.Image'i img.Image'e dönüştür
  final byteData = await finalImage.toByteData(format: ui.ImageByteFormat.rawRgba);
  final pixels = byteData!.buffer.asUint8List();
  final image = img.Image.fromBytes(width: finalImage.width, height: finalImage.height, bytes: pixels.buffer, numChannels: 4);

  // JPEG olarak kodla
  final jpegBytes = img.encodeJpg(image, quality: 90);

  return Uint8List.fromList(jpegBytes);
}

// V2: Yüz bounding box merkezli crop - daha geniş alan
Future<Uint8List> _processCropV2({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final boundingBox = faceDetails.boundingBox;

  // Bounding box'ı %50 genişlet
  final double expandRatio = 1.5;
  final double expandedWidth = boundingBox.width * expandRatio;
  final double expandedHeight = boundingBox.height * expandRatio;

  // Merkezi koru, genişletilmiş alanı hesapla
  final double centerX = boundingBox.center.dx;
  final double centerY = boundingBox.center.dy;

  int cropLeft = math.max(0, (centerX - expandedWidth / 2).toInt());
  int cropTop = math.max(0, (centerY - expandedHeight / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, expandedWidth.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, expandedHeight.toInt());

  // Sınır kontrolü
  if (cropLeft + cropWidth > decodedImage.width) {
    cropWidth = decodedImage.width - cropLeft;
  }
  if (cropTop + cropHeight > decodedImage.height) {
    cropHeight = decodedImage.height - cropTop;
  }

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));

  // Kare boyuta getir
  final int targetSize = math.max(cropWidth, cropHeight);
  option.addOption(ScaleOption(targetSize, targetSize));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(800.0, 800.0));

  return editedImageBytes;
}

// V3: Sadece gözler arası mesafe tabanlı, minimal crop
Future<Uint8List> _processCropV3({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // Minimal crop - sadece gözler arası mesafenin 3 katı
  final double cropSize = eyeDistance * 3.0;

  int cropLeft = math.max(0, (eyeCenter.dx - cropSize / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - cropSize / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, cropSize.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, cropSize.toInt());

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  option.addOption(ScaleOption(600, 600)); // Sabit boyut

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: const Size(600.0, 600.0));

  return editedImageBytes;
}

// V4: Çoklu landmark tabanlı (gözler, burun, ağız)
Future<Uint8List> _processCropV4({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;
  final noseBase = faceDetails.landmarks[FaceLandmarkType.noseBase]?.position;
  final bottomMouth = faceDetails.landmarks[FaceLandmarkType.bottomMouth]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  // Tüm landmark'ların merkez noktasını hesapla
  List<Offset> landmarks = [Offset(leftEye.x.toDouble(), leftEye.y.toDouble()), Offset(rightEye.x.toDouble(), rightEye.y.toDouble())];
  if (noseBase != null) landmarks.add(Offset(noseBase.x.toDouble(), noseBase.y.toDouble()));
  if (bottomMouth != null) landmarks.add(Offset(bottomMouth.x.toDouble(), bottomMouth.y.toDouble()));

  double centerX = landmarks.map((e) => e.dx).reduce((a, b) => a + b) / landmarks.length;
  double centerY = landmarks.map((e) => e.dy).reduce((a, b) => a + b) / landmarks.length;

  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final double cropSize = eyeDistance * 3.5; // Biraz daha geniş

  int cropLeft = math.max(0, (centerX - cropSize / 2).toInt());
  int cropTop = math.max(0, (centerY - cropSize / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, cropSize.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, cropSize.toInt());

  // Döndürme açısını hesapla
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  option.addOption(RotateOption(-angle.toInt()));
  option.addOption(ScaleOption(700, 700));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: const Size(700.0, 700.0));

  return editedImageBytes;
}

// V5: Adaptif boyutlandırma - yüz boyutuna göre dinamik crop
Future<Uint8List> _processCropV5({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // Adaptif crop boyutu - gözler arası mesafeye göre
  double cropMultiplier;
  if (eyeDistance < 100) {
    cropMultiplier = 5.0; // Küçük yüzler için daha geniş
  } else if (eyeDistance < 150) {
    cropMultiplier = 4.0; // Orta boyut yüzler
  } else {
    cropMultiplier = 3.0; // Büyük yüzler için daha dar
  }

  final double cropSize = eyeDistance * cropMultiplier;

  int cropLeft = math.max(0, (eyeCenter.dx - cropSize / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - cropSize / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, cropSize.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, cropSize.toInt());

  // Adaptif hedef boyut
  final int targetSize = (eyeDistance * 4).toInt().clamp(500, 1000);

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  option.addOption(ScaleOption(targetSize, targetSize));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetSize.toDouble(), targetSize.toDouble()));

  return editedImageBytes;
}

// V6: Yüz konturları tabanlı crop (bounding box + padding)
Future<Uint8List> _processCropV6({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final boundingBox = faceDetails.boundingBox;

  // Yüz boyutuna göre padding hesapla
  final double faceWidth = boundingBox.width;
  final double faceHeight = boundingBox.height;
  final double avgFaceSize = (faceWidth + faceHeight) / 2;

  // Dinamik padding - yüz boyutunun %30'u
  final double padding = avgFaceSize * 0.3;

  int cropLeft = math.max(0, (boundingBox.left - padding).toInt());
  int cropTop = math.max(0, (boundingBox.top - padding).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, (faceWidth + 2 * padding).toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, (faceHeight + 2 * padding).toInt());

  // Kare yapmak için en büyük boyutu al
  final int maxDimension = math.max(cropWidth, cropHeight);

  // Merkezi koru, kare alan oluştur
  final double centerX = cropLeft + cropWidth / 2;
  final double centerY = cropTop + cropHeight / 2;

  cropLeft = math.max(0, (centerX - maxDimension / 2).toInt());
  cropTop = math.max(0, (centerY - maxDimension / 2).toInt());
  cropWidth = math.min(decodedImage.width - cropLeft, maxDimension);
  cropHeight = math.min(decodedImage.height - cropTop, maxDimension);

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));
  option.addOption(ScaleOption(750, 750));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: const Size(750.0, 750.0));

  return editedImageBytes;
}

// V7: Çok katmanlı işlem - önce geniş crop, sonra hassas crop
Future<Uint8List> _processCropV7({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // 1. Aşama: Geniş crop
  final double wideCropSize = eyeDistance * 5.0;
  int wideCropLeft = math.max(0, (eyeCenter.dx - wideCropSize / 2).toInt());
  int wideCropTop = math.max(0, (eyeCenter.dy - wideCropSize / 2).toInt());
  int wideCropWidth = math.min(decodedImage.width - wideCropLeft, wideCropSize.toInt());
  int wideCropHeight = math.min(decodedImage.height - wideCropTop, wideCropSize.toInt());

  final option1 = ImageEditorOption();
  option1.addOption(ClipOption(x: wideCropLeft, y: wideCropTop, width: wideCropWidth, height: wideCropHeight));

  Uint8List firstCrop = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option1))!;

  // 2. Aşama: Hassas crop (merkezi yeniden hesapla)
  final double preciseCropRatio = 0.7; // İlk crop'un %70'i
  final int preciseCropSize = (math.min(wideCropWidth, wideCropHeight) * preciseCropRatio).toInt();
  final int preciseLeft = (wideCropWidth - preciseCropSize) ~/ 2;
  final int preciseTop = (wideCropHeight - preciseCropSize) ~/ 2;

  final option2 = ImageEditorOption();
  option2.addOption(ClipOption(x: preciseLeft, y: preciseTop, width: preciseCropSize, height: preciseCropSize));
  option2.addOption(ScaleOption(800, 800));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: firstCrop, imageEditorOption: option2))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: const Size(800.0, 800.0));

  return editedImageBytes;
}

// V8: Yüz açısına göre adaptif crop
Future<Uint8List> _processCropV8({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // Yüz açılarını al
  final double rotationZ = faceDetails.headEulerAngleZ ?? 0.0;
  final double rotationY = faceDetails.headEulerAngleY ?? 0.0;

  // Açıya göre crop boyutunu ayarla
  final double angleAdjustment = (rotationZ.abs() + rotationY.abs()) / 45.0; // 0-1 arası
  final double cropMultiplier = 3.5 + (angleAdjustment * 1.5); // 3.5-5.0 arası

  final double cropSize = eyeDistance * cropMultiplier;

  int cropLeft = math.max(0, (eyeCenter.dx - cropSize / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - cropSize / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, cropSize.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, cropSize.toInt());

  // Gözlerin açısını hesapla
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));

  // Sadece küçük açılarda döndür
  if (angle.abs() < 15) {
    option.addOption(RotateOption(-angle.toInt()));
  }

  option.addOption(ScaleOption(850, 850));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: const Size(850.0, 850.0));

  return editedImageBytes;
}

// V9: Golden ratio tabanlı crop
Future<Uint8List> _processCropV9({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // Golden ratio (1.618) kullanarak crop boyutunu hesapla
  const double goldenRatio = 1.618;
  final double baseSize = eyeDistance * 2.5;
  final double cropWidth = baseSize * goldenRatio;
  final double cropHeight = baseSize;

  int cropLeft = math.max(0, (eyeCenter.dx - cropWidth / 2).toInt());
  int cropTop = math.max(0, (eyeCenter.dy - cropHeight / 2).toInt());
  int finalCropWidth = math.min(decodedImage.width - cropLeft, cropWidth.toInt());
  int finalCropHeight = math.min(decodedImage.height - cropTop, cropHeight.toInt());

  // Golden ratio'yu koruyarak kare yapmak için padding ekle
  final int targetSize = (eyeDistance * 4).toInt();

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: finalCropWidth, height: finalCropHeight));
  option.addOption(ScaleOption(targetSize, targetSize));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetSize.toDouble(), targetSize.toDouble()));

  return editedImageBytes;
}

// V10: Hibrit yaklaşım - en iyi özellikleri birleştir
Future<Uint8List> _processCropV10({required Face faceDetails, required String photoPath, required Uint8List imageBytes, required img.Image decodedImage}) async {
  final leftEye = faceDetails.landmarks[FaceLandmarkType.leftEye]?.position;
  final rightEye = faceDetails.landmarks[FaceLandmarkType.rightEye]?.position;
  final boundingBox = faceDetails.boundingBox;

  if (leftEye == null || rightEye == null) {
    throw Exception("Gözler tespit edilemedi.");
  }

  final eyeDistance = math.sqrt(math.pow(leftEye.x - rightEye.x, 2) + math.pow(leftEye.y - rightEye.y, 2));
  final eyeCenter = Offset((leftEye.x + rightEye.x) / 2, (leftEye.y + rightEye.y) / 2);

  // Hibrit merkez hesaplama - %70 göz merkezi, %30 bounding box merkezi
  final boundingCenter = boundingBox.center;
  final hybridCenterX = (eyeCenter.dx * 0.7) + (boundingCenter.dx * 0.3);
  final hybridCenterY = (eyeCenter.dy * 0.7) + (boundingCenter.dy * 0.3);
  final hybridCenter = Offset(hybridCenterX, hybridCenterY);

  // Adaptif crop boyutu - hem göz mesafesi hem bounding box boyutu dikkate al
  final double eyeBasedSize = eyeDistance * 3.8;
  final double boundingBasedSize = math.max(boundingBox.width, boundingBox.height) * 1.4;
  final double cropSize = (eyeBasedSize + boundingBasedSize) / 2;

  int cropLeft = math.max(0, (hybridCenter.dx - cropSize / 2).toInt());
  int cropTop = math.max(0, (hybridCenter.dy - cropSize / 2).toInt());
  int cropWidth = math.min(decodedImage.width - cropLeft, cropSize.toInt());
  int cropHeight = math.min(decodedImage.height - cropTop, cropSize.toInt());

  // Akıllı döndürme - sadece gerektiğinde
  final angle = math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180 / math.pi;
  final bool shouldRotate = angle.abs() > 2 && angle.abs() < 20;

  // Adaptif hedef boyut
  final int targetSize = (eyeDistance * 4.2).toInt().clamp(600, 900);

  final option = ImageEditorOption();
  option.addOption(ClipOption(x: cropLeft, y: cropTop, width: cropWidth, height: cropHeight));

  if (shouldRotate) {
    option.addOption(RotateOption(-angle.toInt()));
  }

  option.addOption(ScaleOption(targetSize, targetSize));

  Uint8List editedImageBytes = (await ImageEditor.editImage(image: imageBytes, imageEditorOption: option))!;
  editedImageBytes = await mergeBackground(editedImageBytes: editedImageBytes, canvasSize: Size(targetSize.toDouble(), targetSize.toDouble()));

  return editedImageBytes;
}

/// Yüz hizalama doğruluğunu hesapla
void calculateTotalPercentage() {
  double accuracyPercentage({required double value, required double acceptableDifference, required double fitValue}) {
    double error = (fitValue - value).abs() / acceptableDifference;
    double percentage = 100 - (error * 10);

    if (percentage > 100) {
      percentage = 100;
    } else if (percentage < 0) {
      percentage = 0;
    }

    return percentage;
  }

  // Yüz rotasyon ve pozisyon değerleri
  final double headRotationDifferanceSide = currentFace!.headEulerAngleZ!;
  final double headRotationDifferanceRightLeft = currentFace!.headEulerAngleY!;
  final double headRotationDifferanceTopBottom = currentFace!.headEulerAngleX!;
  final double boxSizeDifferent = Offset(currentFace!.boundingBox.size.height, currentFace!.boundingBox.size.width).distance;
  final double boxDistanceToCenter = Helper().calculateTwoPointDistance(currentFace!.boundingBox.center, photoCenter!);

  // Kabul edilebilir fark değerleri
  const double acceptableDifferenceSide = 3;
  const double acceptableDifferenceRightLeft = 3;
  const double acceptableDifferenceTopBottom = 2;
  const double acceptableDifferenceSize = 30;
  const double acceptableDifferenceDistance = 30;

  // İdeal değerler
  const double fitValueSide = 0;
  const double fitValueRightLeft = 0;
  const double fitValueTopBottom = -12;
  const double fitValueSize = 950;
  const double fitValueDistance = 0;

  // Doğruluk yüzdelerini hesapla
  final sideRatio = accuracyPercentage(value: headRotationDifferanceSide, acceptableDifference: acceptableDifferenceSide, fitValue: fitValueSide);
  final rightLeftRatio = accuracyPercentage(value: headRotationDifferanceRightLeft, acceptableDifference: acceptableDifferenceRightLeft, fitValue: fitValueRightLeft);
  final topBottomRatio = accuracyPercentage(value: headRotationDifferanceTopBottom, acceptableDifference: acceptableDifferenceTopBottom, fitValue: fitValueTopBottom);
  final sizeRatio = accuracyPercentage(value: boxSizeDifferent, acceptableDifference: acceptableDifferenceSize, fitValue: fitValueSize);
  final distanceRatio = accuracyPercentage(value: boxDistanceToCenter, acceptableDifference: acceptableDifferenceDistance, fitValue: fitValueDistance);

  // Toplam doğruluk yüzdesini hesapla
  totalAccuracy = (sideRatio + rightLeftRatio + topBottomRatio + sizeRatio + distanceRatio) / 5;
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';

class PlanChooser extends StatefulWidget {
  const PlanChooser({
    super.key,
    required this.isPopular,
    required this.isAdvantagePlan,
    required this.price,
    required this.currency,
    required this.timePeriod,
    required this.isActive,
    required this.onTap,
  });

  final bool isPopular;
  final double price;
  final String currency;
  final bool isAdvantagePlan;
  final String timePeriod;
  final bool isActive;
  final VoidCallback onTap;

  @override
  State<PlanChooser> createState() => _PlanChooserState();
}

class _PlanChooserState extends State<PlanChooser> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: AppColors.deepBlack.withValues(alpha: 0.2),
                blurRadius: 10,
                spreadRadius: 5,
                offset: const Offset(0, 0),
              ),
            ],
            border: widget.isActive
                ? Border.all(
                    color: AppColors.main,
                    width: 2,
                  )
                : Border.all(
                    color: AppColors.transparent,
                    width: 2,
                  ),
            borderRadius: AppColors.borderRadiusAll,
            color: AppColors.deepContrast,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.isPopular)
                ShaderMask(
                  shaderCallback: (Rect bounds) {
                    return LinearGradient(
                      colors: [AppColors.main, AppColors.white],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ).createShader(bounds);
                  },
                  child: const Text(
                    LocaleKeys.PaymentPage_Package_Popular,
                    style: TextStyle(
                      fontSize: 16.0,
                    ),
                  ).tr(),
                ),

              Text(
                '${widget.timePeriod} Pro Plan',
                textAlign: TextAlign.left,
                style: const TextStyle(
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              const Divider(height: 2),
              //Price
              const SizedBox(height: 10),
              Text(
                '${widget.currency} ${widget.price.toStringAsFixed(2)}/${widget.isAdvantagePlan ? LocaleKeys.PaymentPage_Package_Year.tr() : LocaleKeys.PaymentPage_Package_Month.tr()}',
                textAlign: TextAlign.left,
                style: const TextStyle(
                  fontSize: 28.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.isAdvantagePlan) const SizedBox(height: 10),
              if (widget.isAdvantagePlan)
                Row(
                  children: [
                    Text(
                      "${(widget.price / 12).toStringAsFixed(2)}/${LocaleKeys.PaymentPage_Package_Month.tr()}",
                      textAlign: TextAlign.left,
                      style: const TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                        decorationThickness: 2.0,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Container(
                      padding: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        color: AppColors.lightMain,
                        borderRadius: AppColors.borderRadiusAll,
                      ),
                      child: ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return LinearGradient(
                            colors: [AppColors.black, AppColors.main],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ).createShader(bounds);
                        },
                        child: Text(
                          '33% ${LocaleKeys.PaymentPage_Package_DiscountWord.tr()}',
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            fontSize: 12.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}

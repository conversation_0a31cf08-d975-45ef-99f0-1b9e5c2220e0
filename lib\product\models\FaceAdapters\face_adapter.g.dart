// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'face_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FaceAdapterAdapter extends TypeAdapter<FaceAdapter> {
  @override
  final int typeId = 5;

  @override
  FaceAdapter read(BinaryReader reader) {
    return FaceAdapter();
  }

  @override
  void write(BinaryWriter writer, FaceAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FaceAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

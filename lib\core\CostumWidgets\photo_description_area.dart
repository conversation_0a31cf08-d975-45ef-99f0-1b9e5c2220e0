import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PhotoDescriptionArea extends StatelessWidget {
  final OneDayPhotos oneDayPhoto;
  final Function()? setStateCalendar;

  const PhotoDescriptionArea({
    super.key,
    required this.oneDayPhoto,
    this.setStateCalendar,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () {
            Helper()
                .showEditDescriptionSheet(
                  context: context,
                  oneDayPhoto: oneDayPhoto,
                )
                .then(
                  (_) => {
                    {setStateCalendar!()},
                    LogService().editNote(oneDayPhoto.notes == null ? "null" : oneDayPhoto.notes!.length.toString()),
                  },
                );
          },
          child: Container(
            width: 0.9.sw,
            decoration: BoxDecoration(
              color: AppColors.panelBackground,
              borderRadius: AppColors.borderRadiusAll,
            ),
            child: Container(
              padding: const EdgeInsets.fromLTRB(10, 36, 10, 10),
              constraints: BoxConstraints(
                minHeight: 0.05.sh,
                maxHeight: 0.3.sh,
              ),
              child: SingleChildScrollView(
                child: Text(
                  oneDayPhoto.notes == null || oneDayPhoto.notes!.isEmpty ? LocaleKeys.PhotoPage_NoDescripiton.tr() : oneDayPhoto.notes!,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),
        ),
        Positioned(
          top: 10,
          left: 10,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                oneDayPhoto.date.toString().substring(0, 10),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(
                width: 0.58.sw,
              ),
              GestureDetector(
                onTap: () {
                  Helper()
                      .showEditDescriptionSheet(
                        context: context,
                        oneDayPhoto: oneDayPhoto,
                      )
                      .then(
                        (_) => {
                          {setStateCalendar!()},
                          LogService().editNote(oneDayPhoto.notes == null ? "null" : oneDayPhoto.notes!.length.toString()),
                        },
                      );
                },
                child: const Icon(
                  Icons.edit,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

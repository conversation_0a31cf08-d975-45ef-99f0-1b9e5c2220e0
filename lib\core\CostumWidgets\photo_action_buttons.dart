import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PhotoActionButtons extends StatelessWidget {
  final OneDayPhotos oneDayPhotos;
  final Photo photo;
  final Function()? updateSelectedPhotoForCalendar;
  final bool isFeedPage;
  final PageController? pageController;
  final Function()? setState;

  const PhotoActionButtons({
    super.key,
    required this.oneDayPhotos,
    required this.photo,
    this.updateSelectedPhotoForCalendar,
    this.isFeedPage = false,
    this.pageController,
    this.setState,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Column(
          children: [
            FloatingActionButton(
              heroTag: 'delete',
              onPressed: () async {
                LogService().deletePhoto();

                await Helper().getDialog(
                  message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_PermanentlyDeleteWarning.tr(),
                  onAccept: () async {
                    try {
                      int currentPhotoIndex = oneDayPhotos.photos.indexWhere((element) => element == photo);

                      await context.read<PhotoProvider>().deletePhoto(photo: photo);

                      Helper().getMessage(
                        message: LocaleKeys.PhotoEvents_ScaffoldMessage_photoDeleted.tr(),
                        icon: Icons.delete,
                      );

                      if (updateSelectedPhotoForCalendar == null && oneDayPhotos.photos.first == photo && !isFeedPage) {
                        Navigator.pop(context);
                      } else if (!isFeedPage) {
                        if (currentPhotoIndex == 0) {
                          setState!();
                        } else {
                          pageController!.jumpToPage(currentPhotoIndex == 0 ? 0 : currentPhotoIndex - 1);
                        }
                      }

                      if (updateSelectedPhotoForCalendar != null) {
                        updateSelectedPhotoForCalendar!();
                      }
                      if (isFeedPage) {
                        Navigator.pop(context);
                      }
                    } catch (e) {
                      Helper().getMessage(
                        message: LocaleKeys.PhotoEvents_ScaffoldMessage_photoDeleted.tr(),
                        status: StatusEnum.WARNING,
                      );
                    }
                  },
                );
                // TODO: Bu fonksiyonu da Helper içine alsak güzel olur bence.
              },
              child: const Icon(
                Icons.delete,
                color: AppColors.black,
              ),
            ),
            const Text(
              LocaleKeys.PhotoEvents_Delete,
            ).tr(),
          ],
        ),
        Column(
          children: [
            FloatingActionButton(
              heroTag: 'download',
              onPressed: () {
                LogService().downloadPhoto();

                Helper().downloadPhoto(
                  context,
                  photo,
                );
                if (isFeedPage) {
                  Navigator.pop(context);
                }
              },
              child: const Icon(
                Icons.file_download,
                color: AppColors.black,
              ),
            ),
            const Text(
              LocaleKeys.PhotoEvents_Download,
            ).tr(),
          ],
        ),
        Column(
          children: [
            FloatingActionButton(
                heroTag: 'share',
                onPressed: () async {
                  LogService().sharePhoto();

                  await Helper().shareItem(photo.path);
                },
                child: const Icon(
                  Icons.share,
                  color: AppColors.black,
                )),
            const Text(
              LocaleKeys.PhotoEvents_Share,
            ).tr(),
          ],
        ),
      ],
    );
  }
}

import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/export_loading_widget.dart';
import 'package:facelog/Pages/Produce/Video/core/video_variables.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/components/slider_component.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/sound_model.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'components/sound_list_tile.dart';

class VideoPreferencesView extends StatefulWidget {
  const VideoPreferencesView({
    super.key,
  });

  @override
  State<VideoPreferencesView> createState() => _VideoPreferencesViewState();
}

class _VideoPreferencesViewState extends State<VideoPreferencesView> with VideoPreferencesMixin, WidgetsBindingObserver {
  // Texts
  // ! disabled
  // final String _isInclude = LocaleKeys.VideoPreferencesPage_IsIncludeNonFaces.tr();

  @override
  void initState() {
    super.initState();

    audioListenersStarter();

    LogService().logScreen("VideoExportPage");
  }

  @override
  void dispose() async {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    for (int i = 0; i < soundModelList.length; i++) {
      soundModelList[i].audioPlayer.dispose();
    }

    isVideoExportProgressLoading = false;

    // listeyi sıfırla
    soundModelList = [
      SoundModel(
        name: 'Silence of the Amps',
        path: AssetsPath.silenceOfTheAmps,
        duration: const Duration(seconds: 231),
        position: Duration.zero,
        isSelected: false,
        isPlaying: false,
        isObjectPermanent: true,
        audioPlayer: AudioPlayer(),
      ),
    ];
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      for (int i = 0; i < soundModelList.length; i++) {
        soundModelList[i].audioPlayer.pause();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          Helper().cancelApproveDialog(context, isVideoExportProgressLoading);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            padding: const EdgeInsets.only(left: 10),
            icon: const Icon(
              Icons.arrow_back_ios,
            ),
            onPressed: () {
              Helper().cancelApproveDialog(context, isVideoExportProgressLoading);
            },
          ),
          title: const Text(
            LocaleKeys.VideoPreferencesPage_AppBar_Title,
            style: TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ).tr(),
          actions: <Widget>[
            GestureDetector(
              onTap: () async {
                if (Platform.isAndroid) {
                  if (!await Helper().storageAccessRequest()) return;
                }

                if (isVideoExportProgressLoading || !mounted) return;
                await renderFunction(
                  qualityIndex: qualityList[activeQualityIndex],
                  videoLength: videoLengthInputController.text.isNotEmpty ? videoLengthInputController.text : videoSpeedList[activeSpeedIndex].toString(),
                  audioFilePath: soundModelList.any((soundModel) => soundModel.isSelected) ? soundModelList.firstWhere((soundModel) => soundModel.isSelected).path : null,
                  audioIsAssets: soundModelList.any((soundModel) => soundModel.isSelected) ? soundModelList.firstWhere((soundModel) => soundModel.isSelected).isObjectPermanent : false,
                );
              },
              child: Container(
                color: AppColors.transparent,
                padding: const EdgeInsets.fromLTRB(12, 12, 20, 12),
                child: Text(
                  LocaleKeys.VideoPreferencesPage_AppBar_ExportButton,
                  style: TextStyle(
                    fontSize: 40.sp,
                    color: AppColors.main,
                  ),
                ).tr(),
              ),
            ),
          ],
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: Column(
                  children: <Widget>[
                    const SizedBox(height: 20),
                    Row(
                      children: <Widget>[
                        Text(
                          LocaleKeys.VideoPreferencesPage_Body_VideoFeatures_Header,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.values[5],
                          ),
                        ).tr(),
                        const Spacer(),
                      ],
                    ),
                    const SizedBox(height: 20),
                    SliderLabelWidget(
                      activeIndex: activeQualityIndex,
                      labels: qualityList,
                      header: LocaleKeys.VideoPreferencesPage_Body_VideoFeatures_SliderQuality_Header.tr(),
                      onChanged: (index) {
                        setState(() {
                          activeQualityIndex = index;
                        });
                      },
                    ),
                    const SizedBox(height: 30),
                    Row(
                      children: [
                        SizedBox(
                          width: 0.78.sw,
                          child: SliderLabelWidget(
                            activeIndex: activeSpeedIndex,
                            labels: videoSpeedList,
                            header: LocaleKeys.VideoPreferencesPage_Body_VideoFeatures_SliderLength_Header.tr(),
                            onChanged: (index) {
                              setState(() {
                                videoLengthInputController.clear();
                                activeSpeedIndex = index;
                              });
                            },
                          ),
                        ),
                        Container(
                          height: 35,
                          width: 50,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: AppColors.borderRadiusAll,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.black.withValues(alpha: 0.2),
                                blurRadius: 3,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: TextField(
                            maxLength: 3,
                            controller: videoLengthInputController,
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: AppColors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 45.h,
                            ),
                            decoration: InputDecoration(
                              counterText: "",
                              hintText: videoSpeedList[activeSpeedIndex].toString(),
                              border: InputBorder.none,
                            ),
                            onChanged: (value) {
                              setState(
                                () {
                                  videoLengthInputController.text = value.replaceFirst("..", ".").replaceFirst(",", "");

                                  if (value.isNotEmpty && value.substring(0, 1) == ".") {
                                    videoLengthInputController.text = value.substring(1);
                                  }
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Padding(
                            padding: const EdgeInsets.only(left: 30.0),
                            child: photoProvider.allPhotosList.isNotEmpty
                                ? Text(
                                    '${(double.parse((videoLengthInputController.text.isNotEmpty) ? videoLengthInputController.text : videoSpeedList[activeSpeedIndex].toString()) / photoProvider.allPhotosList.map((e) => e.photos).expand((element) => element).toList().length).toStringAsFixed(2)} ${LocaleKeys.VideoPreferencesPage_Body_VideoFeatures_LengthInfo.tr()}',
                                  )
                                : const Text(LocaleKeys.PhotoEvents_ScaffoldMessage_YouDontHaveAnyPhoto).tr()),
                        const Spacer(),
                      ],
                    ),
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Divider(),
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Text(
                          LocaleKeys.VideoPreferencesPage_Body_AudioSource_Header.tr(),
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.values[5],
                          ),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () async {
                            if (isPremium!) {
                              await pickMultiFile().then((value) {
                                audioListenersStarter();
                              });
                            } else {
                              Helper().mustPremiumDialog(context);
                            }
                          },
                          child: ClipRRect(
                            borderRadius: AppColors.borderRadiusAll,
                            child: Container(
                              width: 0.35.sw,
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppColors.main,
                                    AppColors.deepMain,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.music_note),
                                  const SizedBox(width: 10),
                                  Text(
                                    LocaleKeys.VideoPreferencesPage_Body_AddMusic,
                                    style: TextStyle(
                                      fontSize: 45.sp,
                                    ),
                                  ).tr(),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    SizedBox(
                      height: 90.0 * soundModelList.length,
                      child: ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: soundModelList.length,
                        itemBuilder: (context, index) {
                          return SoundListTile(
                            soundModel: soundModelList[index],
                            onTap: () {
                              setState(() {
                                if (soundModelList[index].isSelected) {
                                  soundModelList[index].isSelected = false;
                                } else {
                                  for (int i = 0; i < soundModelList.length; i++) {
                                    soundModelList[i].isSelected = false;
                                  }
                                  soundModelList[index].isSelected = true;
                                }
                              });
                            },
                          );
                        },
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        Checkbox(
                          value: !isBrandLogoActive,
                          onChanged: (_) {
                            _isBrandLogoActiveSwitch();
                          },
                        ),
                        GestureDetector(
                          onTap: () {
                            _isBrandLogoActiveSwitch();
                          },
                          child: Text(
                            LocaleKeys.VideoPreferencesPage_WannaRemoveWatermark,
                            textAlign: TextAlign.left,
                            style: TextStyle(
                              color: AppColors.onBackground,
                              fontWeight: FontWeight.values[2],
                              fontSize: 60.sp,
                            ),
                          ).tr(),
                        ),
                      ],
                    ),
                    // ! disabled
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.start,
                    //   children: <Widget>[
                    //     Checkbox(
                    //       value: isIncludeNonFaces,
                    //       onChanged: (_) {
                    //         _isIncludeNonFaces();
                    //       },
                    //     ),
                    //     GestureDetector(
                    //       onTap: () {
                    //         _isIncludeNonFaces();
                    //       },
                    //       child: Text(
                    //         _isInclude,
                    //         textAlign: TextAlign.left,
                    //         style: TextStyle(
                    //           color: AppColors.onBackground,
                    //           fontWeight: FontWeight.values[2],
                    //           fontSize: 60.sp,
                    //         ),
                    //       ).tr(),
                    //     ),
                    //   ],
                    // ),
                  ],
                ),
              ),
            ),
            if (isVideoExportProgressLoading) ...[
              const ExportLoadingWidget(isWithProgress: false),
            ]
          ],
        ),
      ),
    );
  }

  // create function for isBrandLogoActive switching
  void _isBrandLogoActiveSwitch() async {
    if (isPremium!) {
      setState(() {
        isBrandLogoActive = !isBrandLogoActive;
      });
    } else {
      Helper().mustPremiumDialog(context);
    }
  }

  // ! disabled
  // void _isIncludeNonFaces() async {
  //   setState(() {
  //     isIncludeNonFaces = !isIncludeNonFaces;
  //   });
  // }

  void audioListenersStarter() {
    for (int i = 0; i < soundModelList.length; i++) {
      soundModelList[i].audioPlayer.onPlayerStateChanged.listen(
        (state) {
          if (mounted) {
            setState(() {
              soundModelList[i].isPlaying = state == PlayerState.playing;
            });
          }
        },
      );

      soundModelList[i].audioPlayer.onPositionChanged.listen((newPosition) {
        setState(() {
          soundModelList[i].position = newPosition;
        });
      });
    }
  }
}

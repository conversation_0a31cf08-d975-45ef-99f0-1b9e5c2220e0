import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:vibration/vibration.dart';

class StreakHelper {
  Color getDotColor(StreakStatus status) {
    Color dotColor;

    switch (status) {
      case StreakStatus.empty:
        dotColor = AppColors.deepWhite;
        break;
      case StreakStatus.streak:
        dotColor = AppColors.main;
        break;
      case StreakStatus.broken:
        dotColor = Helper().getAppColorName() == "red" ? AppColors.purple : AppColors.red;
        break;
      case StreakStatus.freeze:
        dotColor = Helper().getAppColorName() == "blue" ? AppColors.deepBlue : AppColors.blue;
        break;
    }

    return dotColor;
  }

  int calculateStreak() {
    final reversedList = streakList.reversed.toList();

    int streak = 0;
    bool hasStarted = false;

    for (var day in reversedList) {
      if (day.status == StreakStatus.streak || day.status == StreakStatus.freeze) {
        streak++;
        hasStarted = true;
      } else if (hasStarted && day.status == StreakStatus.broken || day.status == StreakStatus.empty) {
        break;
      }
    }

    return streak;
  }

  Future<void> vibration() async {
    if ((await Vibration.hasCustomVibrationsSupport()) == true && (await Vibration.hasVibrator()) == true && (await Vibration.hasAmplitudeControl()) == true) {
      Vibration.vibrate(pattern: [150, 40, 400], intensities: [50, 255, 50]);
    }
  }
}

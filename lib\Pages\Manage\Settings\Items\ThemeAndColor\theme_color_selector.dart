import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Items/ThemeAndColor/color_preview.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeColorSelector extends StatefulWidget {
  const ThemeColorSelector({super.key});

  @override
  ThemeColorSelectorState createState() => ThemeColorSelectorState();
}

class ThemeColorSelectorState extends State<ThemeColorSelector> {
  // Texts
  final String _title = LocaleKeys.SettingsPage_SettingsTiles_Theme_SelectColor_Title.tr();
  final String _selectedColors = LocaleKeys.SettingsPage_SettingsTiles_Theme_SelectColor_SelectedColors.tr();
  final String _main = LocaleKeys.SettingsPage_SettingsTiles_Theme_SelectColor_Colors_Main.tr();
  final String _light = LocaleKeys.SettingsPage_SettingsTiles_Theme_SelectColor_Colors_Light.tr();
  final String _dark = LocaleKeys.SettingsPage_SettingsTiles_Theme_SelectColor_Colors_Dark.tr();

  @override
  void initState() {
    super.initState();

    LogService().logScreen("ThemeColorSelector");
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(
        vertical: 0.27.sh,
        horizontal: 20,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AutoSizeText(
            _title,
            maxLines: 1,
            style: TextStyle(
              fontSize: 70.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: AppColors.selectableColors.map(
              (color) {
                return Padding(
                  padding: const EdgeInsets.all(5),
                  child: GestureDetector(
                    onTap: () => updateColors(color),
                    child: Container(
                      width: AppColors.main == color ? 55 : 45,
                      height: AppColors.main == color ? 55 : 45,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                );
              },
            ).toList(),
          ),
          const SizedBox(height: 40),
          Text(
            _selectedColors,
            style: const TextStyle(
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ColorPreview(color: AppColors.main, label: _main),
                ColorPreview(color: AppColors.lightMain, label: _light),
                ColorPreview(color: AppColors.deepMain, label: _dark),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void saveColors() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    // ignore: deprecated_member_use
    prefs.setInt('mainColor', AppColors.main.value);
    // ignore: deprecated_member_use
    prefs.setInt('lightMainColor', AppColors.lightMain.value);
    // ignore: deprecated_member_use
    prefs.setInt('deepMainColor', AppColors.deepMain.value);
  }

  void updateColors(Color newMainColor) {
    setState(() {
      AppColors.main = newMainColor;
      AppColors.lightMain = _getLighterColor(AppColors.main);
      AppColors.deepMain = _getDarkerColor(AppColors.main);
    });

    saveColors();

    LogService().changeAppColor(Helper().getAppColorName());

    context.read<ThemeProvider>().updateColor();
  }

  Color _getLighterColor(Color color) {
    final hslColor = HSLColor.fromColor(color);
    return hslColor.withLightness((hslColor.lightness + 0.3).clamp(0.0, 1.0)).toColor();
  }

  Color _getDarkerColor(Color color) {
    final hslColor = HSLColor.fromColor(color);
    return hslColor.withLightness((hslColor.lightness - 0.2).clamp(0.0, 1.0)).toColor();
  }
}

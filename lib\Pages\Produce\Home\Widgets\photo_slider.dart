import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/Photo/photo_page.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class PhotoSlider extends StatefulWidget {
  const PhotoSlider({
    super.key,
  });

  @override
  State<PhotoSlider> createState() => _PhotoSliderState();
}

class _PhotoSliderState extends State<PhotoSlider> {
  late List<Photo> allPhotos;

  @override
  Widget build(BuildContext context) {
    allPhotos = context.read<PhotoProvider>().allPhotosList.map((e) => e.photos).expand((element) => element).toList();
    return SizedBox(
      height: 0.50.sw,
      child: context.watch<PhotoProvider>().allPhotosList.isNotEmpty
          ? ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: allPhotos.length,
              itemBuilder: (BuildContext context, int index) {
                final int currentPhotoDayIndex = context.read<PhotoProvider>().allPhotosList.indexWhere((element) => element.photos.contains(allPhotos[allPhotos.length - (index + 1)]));
                final OneDayPhotos currentDay = context.read<PhotoProvider>().allPhotosList[currentPhotoDayIndex];
                final Photo currentPhoto = allPhotos[allPhotos.length - (index + 1)];

                return GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PhotoPage(
                          oneDayPhotos: currentDay,
                          selectedPhoto: currentPhoto,
                        ),
                      ),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: ClipRRect(
                      borderRadius: AppColors.borderRadiusAll,
                      child: SizedBox(
                        width: 160,
                        child: Image.file(
                          cacheHeight: 500,
                          File(currentPhoto.path),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                );
              },
            )
          : Center(
              child: const Text(
                LocaleKeys.Home_NoPhotos,
              ).tr(),
            ),
    );
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'facecontourtype_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FaceContourTypeAdapterAdapter
    extends TypeAdapter<FaceContourTypeAdapter> {
  @override
  final int typeId = 10;

  @override
  FaceContourTypeAdapter read(BinaryReader reader) {
    return FaceContourTypeAdapter();
  }

  @override
  void write(BinaryWriter writer, FaceContourTypeAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FaceContourTypeAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

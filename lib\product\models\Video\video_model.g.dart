// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class VideoModelAdapter extends TypeAdapter<VideoModel> {
  @override
  final int typeId = 12;

  @override
  VideoModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VideoModel(
      path: fields[0] as String,
      createdAt: fields[1] as DateTime,
      photoCount: fields[2] as int,
      firstPhotoDate: fields[3] as DateTime,
      lastPhotoDate: fields[4] as DateTime,
      resolution: fields[6] as String,
      thumbnailByte: fields[7] as Uint8List,
      duration: fields[8] as String?,
      durationPerPhoto: fields[9] as String?,
      dayCount: fields[10] as int?,
      musicName: fields[11] as String?,
      isBrandLogoActive: fields[14] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, VideoModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.path)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.photoCount)
      ..writeByte(3)
      ..write(obj.firstPhotoDate)
      ..writeByte(4)
      ..write(obj.lastPhotoDate)
      ..writeByte(6)
      ..write(obj.resolution)
      ..writeByte(7)
      ..write(obj.thumbnailByte)
      ..writeByte(8)
      ..write(obj.duration)
      ..writeByte(9)
      ..write(obj.durationPerPhoto)
      ..writeByte(10)
      ..write(obj.dayCount)
      ..writeByte(11)
      ..write(obj.musicName)
      ..writeByte(14)
      ..write(obj.isBrandLogoActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VideoModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Payment/Market/market_view_mixin.dart';
import 'package:facelog/Pages/Manage/Payment/components/advantage_feature.dart';
import 'package:facelog/core/CostumWidgets/costum_button.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class MarketView extends StatefulWidget {
  const MarketView({super.key});

  @override
  State<MarketView> createState() => _MarketViewState();
}

class _MarketViewState extends State<MarketView> with MarketViewMixin {
  // https://drive.google.com/file/d/1ZD50aKLJlJN7h18fFEd5GpK-iZodGDE6/view?usp=drive_link
  // kopyalanan linkteki "/d/" den sonraki id alınır. Örnek: 1ZD50aKLJlJN7h18fFEd5GpK-iZodGDE6
  // daha sonra aşağıda "id=" den sonra yazılır
  String imageUrl = 'https://drive.google.com/uc?export=view&id=10WDFgaDmX0SEm73OkeDHJH1FKT3dc1lP';
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(
            height: 0.22.sh,
            width: double.infinity,
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.fitWidth,
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: AppColors.background,
                highlightColor: AppColors.lightblack,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: AspectRatio(
                    aspectRatio: 2.39,
                    child: Container(
                      color: AppColors.black,
                    ),
                  ),
                ),
              ),
              errorWidget: (context, url, error) => const SizedBox.shrink(),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: const Text(
                    LocaleKeys.PaymentPage_BecomeProMemeber,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 28,
                    ),
                  ).tr(),
                ),
                buildProductList(),
                const SizedBox(height: 20),
                AdvantagePlanFeature(),
                const SizedBox(height: 40),
                CustomButton(
                  buttonText: LocaleKeys.PaymentPage_tryFreeTrials.tr(),
                  onPressed: () {
                    buySelectedSubscription();
                  },
                ),
                const SizedBox(height: 30),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: const Text(
                    LocaleKeys.PaymentPage_legalText,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      color: AppColors.dirtyWhite,
                      fontWeight: FontWeight.w700,
                      fontSize: 12,
                    ),
                  ).tr(),
                  // TODO: Text içindeki linklerin tıklanabilir olması sağlanacak.
                ),
                const SizedBox(height: 15),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }
    settings.ext.flutterSdkPath = flutterSdkPath()

    includeBuild("${settings.ext.flutterSdkPath}/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version '8.6.0' apply false
    id "org.jetbrains.kotlin.android" version "2.0.0" apply false
    id "com.google.gms.google-services" version '4.4.2' apply false
    id "com.google.firebase.firebase-perf" version "1.4.2" apply false
}

include ":app"

// This is to fix the ffmpeg_kit_flutter plugin issue with Android Gradle Plugin 8.6.0
gradle.beforeProject { project ->
    if (project.name == "ffmpeg_kit_flutter") {
        project.buildscript {
            repositories {
                google()
                mavenCentral()
            }
            dependencies {
                classpath "com.android.tools.build:gradle:8.6.0"
            }
        }
    }
}

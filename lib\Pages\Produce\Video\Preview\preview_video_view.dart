import 'dart:async';
import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/Service/video_caching_api.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/core/CostumWidgets/video_viewer_component.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/Pages/Produce/Video/core/path_constants.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/models/Video/video_model.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';
import 'package:toggle_list/toggle_list.dart';

part 'components/video_properties_widget.dart';

final class VideoPreviewPage extends StatefulWidget {
  final VideoModel? savedVideoModel;

  const VideoPreviewPage({
    super.key,
    this.savedVideoModel,
  });

  @override
  State<VideoPreviewPage> createState() => _VideoPreviewPageState();
}

class _VideoPreviewPageState extends State<VideoPreviewPage> {
  // Texts
  final String _deleteSuccessful = LocaleKeys.VideoListPage_DeleteSuccessfully.tr();
  final String _deleteUnsuccessful = LocaleKeys.VideoListPage_DeleteUnsuccessfully.tr();

  late VideoPlayerController _controller;
  late Future<void> _initializeVideoPlayerFuture;

  late String videoPath;

  VideoModel? videoModel;

  @override
  void initState() {
    super.initState();

    videoPath = widget.savedVideoModel?.path ?? faceLogExportVideo;

    _controller = VideoPlayerController.file(File(videoPath));
    _initializeVideoPlayerFuture = _initializeVideoPlayerFutures();
    _controller.setLooping(true);
    _controller.setVolume(1.0);

    LogService().logScreen("VideoPreviewPage");
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: const Text(LocaleKeys.VideoPreview_AppBarHeader).tr(),
        actions: [
          GestureDetector(
            onTap: () {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder: (context) => const NavBarAndPages(isFirst: false),
                ),
                (route) => false,
              );
            },
            child: Container(
              color: AppColors.transparent,
              padding: const EdgeInsets.all(18),
              child: Text(
                LocaleKeys.VideoPreview_Done,
                style: TextStyle(
                  fontSize: 40.sp,
                  color: AppColors.main,
                ),
              ).tr(),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: FutureBuilder(
          future: _initializeVideoPlayerFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done && videoModel != null) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    children: [
                      VideoViewerComponent(
                        controller: _controller,
                        togglePlayPause: () {
                          setState(() {
                            if (_controller.value.isPlaying) {
                              _controller.pause();
                            } else {
                              _controller.play();
                            }
                          });
                        },
                      ),
                      const SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _VideoProporties(
                              proportiesList: [
                                if (videoModel!.duration != null) "${LocaleKeys.VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_PerPhotoDuration.tr()} ${videoModel!.durationPerPhoto}",
                                "${LocaleKeys.VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_PhotoCount.tr()} ${videoModel!.photoCount}",
                                if (videoModel!.dayCount != null) "${LocaleKeys.VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_DayCount.tr()} ${videoModel!.dayCount}",
                                "${LocaleKeys.VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_Resolution.tr()} ${videoModel!.resolution}",
                                "${LocaleKeys.VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_FirstPhoto.tr()} ${DateFormat("dd MMM yyyy").format(videoModel!.firstPhotoDate)}",
                                "${LocaleKeys.VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_LastPhoto.tr()} ${DateFormat("dd MMM yyyy").format(videoModel!.lastPhotoDate)}",
                              ],
                            ),
                            Divider(color: AppColors.text),
                            const SizedBox(height: 10),
                            Center(
                              child: GestureDetector(
                                onTap: () async {
                                  await Helper().openFileManager();
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: AppColors.main,
                                    borderRadius: AppColors.borderRadiusAll,
                                  ),
                                  child: Column(
                                    children: [
                                      const Text(
                                        LocaleKeys.VideoPreferencesPage_VideoPropertiesList_VideoPropertiesItems_GoToFolder,
                                        style: TextStyle(
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.white,
                                        ),
                                      ).tr(),
                                      Text(
                                        videoPath,
                                        style: const TextStyle(
                                          fontSize: 10,
                                          color: AppColors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 30),
                            Center(
                              child: widget.savedVideoModel == null
                                  ? Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      children: [
                                        SizedBox(
                                          height: 64,
                                          width: 64,
                                          child: FloatingActionButton(
                                            heroTag: "delete",
                                            onPressed: () async {
                                              try {
                                                File(videoPath).deleteSync();
                                                await widget.savedVideoModel!.delete();
                                                Helper().getMessage(
                                                  message: _deleteSuccessful,
                                                  icon: Icons.delete,
                                                );
                                                Navigator.pop(context, widget.savedVideoModel);
                                              } catch (e) {
                                                Helper().getMessage(
                                                  message: _deleteUnsuccessful,
                                                  status: StatusEnum.WARNING,
                                                  icon: Icons.delete,
                                                );
                                                Navigator.pop(context);
                                              }
                                            },
                                            child: const Icon(
                                              Icons.delete,
                                            ),
                                          ),
                                        ),
                                        _shareButton(),
                                      ],
                                    )
                                  : _shareButton(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            } else {
              return SizedBox(
                height: 0.75.sh,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  SizedBox _shareButton() {
    return SizedBox(
      height: 64,
      width: 64,
      child: FloatingActionButton(
        heroTag: "share",
        onPressed: () {
          Helper().shareItem(videoPath);
          debugPrint(videoPath);
        },
        child: const Icon(
          Icons.share,
        ),
      ),
    );
  }

  Future<void> _initializeVideoPlayerFutures() async {
    await VideoCachingAPI().getAndCacheVideoInfo(context, widget.savedVideoModel, _controller).then((getVideoModel) {
      videoModel = getVideoModel;
    });
  }
}

final class _ControlsOverlay extends StatefulWidget {
  const _ControlsOverlay({
    required this.controller,
  });

  final VideoPlayerController controller;

  @override
  State<_ControlsOverlay> createState() => _ControlsOverlayState();
}

class _ControlsOverlayState extends State<_ControlsOverlay> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: widget.controller.value.isPlaying
              ? const SizedBox.shrink()
              : const ColoredBox(
                  color: Colors.black26,
                  child: Center(
                    child: Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 100.0,
                      semanticLabel: 'Play',
                    ),
                  ),
                ),
        ),
      ],
    );
  }
}

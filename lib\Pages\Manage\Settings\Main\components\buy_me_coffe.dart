import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:url_launcher/url_launcher.dart';

class BuyMeACoffee extends StatelessWidget {
  const BuyMeACoffee({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        _launchURL(Uri.parse('https://www.buymeacoffee.com/facelog'));
      },
      child: Container(
        height: 50,
        color: AppColors.white,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SvgPicture.asset(
            AssetsPath.coffe,
          ),
        ),
      ),
    );
  }
}

void _launchURL(Uri url) async {
  await launchUrl(url);
}

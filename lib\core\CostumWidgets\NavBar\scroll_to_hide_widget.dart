import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ScrollToHideWidget extends StatefulWidget {
  const ScrollToHideWidget({
    super.key,
    required this.child,
    this.feedPageScrollController,
    required this.height,
    this.duration = const Duration(milliseconds: 400),
    this.hideNavBarController,
  });

  final Widget child;

  final ScrollController? feedPageScrollController;
  final Duration duration;
  final double height;

  final ScrollController? hideNavBarController;

  @override
  State<ScrollToHideWidget> createState() => _ScrollToHideWidgetState();
}

class _ScrollToHideWidgetState extends State<ScrollToHideWidget> {
  @override
  void initState() {
    super.initState();

    if (widget.feedPageScrollController != null) {
      widget.feedPageScrollController!.addListener(listen);
      widget.hideNavBarController!.addListener(listenHideFunc);
    }
  }

  @override
  void dispose() {
    if (widget.feedPageScrollController != null) {
      widget.feedPageScrollController!.removeListener(listen);
      widget.hideNavBarController!.removeListener(listenHideFunc);
    }

    super.dispose();
  }

  void listen() {
    if (widget.feedPageScrollController != null) {
      final direction = widget.feedPageScrollController!.position.userScrollDirection;
      if (direction == ScrollDirection.forward || widget.feedPageScrollController!.position.pixels <= 300) {
        show();
      } else if (direction == ScrollDirection.reverse) {
        hide();
      }
    }
  }

  void listenHideFunc() {
    if (widget.hideNavBarController != null) {
      hide();
    }
  }

  void show() {
    if (!navbarIsVisible) {
      setState(() {
        navbarIsVisible = true;
      });
    }
  }

  void hide() {
    if (navbarIsVisible) {
      setState(() {
        navbarIsVisible = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: widget.duration,
      height: navbarIsVisible ? widget.height : 0,
      padding: EdgeInsets.symmetric(
        horizontal: 100.w,
      ),
      child: Center(
        child: widget.child,
      ),
    );
  }
}

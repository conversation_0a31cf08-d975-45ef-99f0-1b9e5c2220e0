/// Flutter icons FacelogIcon
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  FacelogIcon
///      fonts:
///       - asset: fonts/FacelogIcon.ttf
///
///
///
library;

import 'package:flutter/widgets.dart';

class FacelogIcon {
  FacelogIcon._();

  static const _kFontFam = 'FacelogIcon';
  static const String? _kFontPkg = null;

  static const IconData mouth3 = IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData eye1 = IconData(0xe80d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/Streak/StreakHistoryPage/streak_history_page.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/state/Provider/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

class StreakHistoryItem extends StatelessWidget {
  const StreakHistoryItem({super.key});

  @override
  Widget build(BuildContext context) {
    context.watch<ThemeProvider>().themeMode;

    return Ink(
      decoration: BoxDecoration(
        color: AppColors.panelBackground,
        borderRadius: AppColors.borderRadiusTop,
      ),
      child: InkWell(
        borderRadius: AppColors.borderRadiusTop,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StreakHistoryPage(),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 10,
          ),
          child: Row(
            children: [
              const Icon(
                Icons.history,
              ),
              const SizedBox(width: 10),
              Text(
                LocaleKeys.Home_Streak_StreakHistory.tr(),
                style: const TextStyle(
                  fontSize: 18.0,
                ),
              ),
              const Spacer(),
              SvgPicture.asset(
                AssetsPath.iceDot,
                width: 70.w,
              ),
              SizedBox(width: 10.w),
              Text(
                freezeCount.toString(),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 40.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

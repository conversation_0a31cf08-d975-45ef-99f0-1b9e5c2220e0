import 'dart:io';
import 'dart:typed_data';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/Preview/preview_video_view.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/Video/video_model.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/video_cache_service.dart';
import 'package:flutter/material.dart';

final class VideoListView extends StatefulWidget {
  const VideoListView({super.key});

  @override
  State<VideoListView> createState() => _VideoListViewState();
}

class _VideoListViewState extends State<VideoListView> {
  List<VideoModel> _videos = [];
  final List<VideoModel> _validVideos = [];
  bool _isLoading = true;
  final List<Uint8List> _thumbnails = [];
  final List<String> _resolutions = [];
  final List<bool> _isSelected = [];

  bool isSelectModeEnabled = false;

  final String _title = LocaleKeys.VideoListPage_title;
  final String _dialogMessage = LocaleKeys.VideoListPage_dialogMessage;
  final String _approve = LocaleKeys.VideoListPage_approve;
  final String _selectButton = LocaleKeys.VideoListPage_selectVideo;
  final String _noVideos = LocaleKeys.VideoListPage_noVideo;

  @override
  void initState() {
    super.initState();
    _getVideos();

    LogService().logScreen("VideoListView");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(_title).tr(),
          leading: IconButton(
            onPressed: () {
              if (isSelectModeEnabled) {
                setState(() {
                  isSelectModeEnabled = false;
                });
                // all selected videos will be false
                for (int i = 0; i < _isSelected.length; i++) {
                  _isSelected[i] = false;
                }
              } else {
                Navigator.pop(context);
              }
            },
            icon: Icon(
              isSelectModeEnabled ? Icons.close : Icons.arrow_back_ios,
            ),
          ),
          actions: [
            // TODO: Hepsini silmek istediğine emin misin dialogu yap. getX ile yap
            if (isSelectModeEnabled) ...[
              IconButton(
                icon: const Icon(
                  Icons.delete,
                  color: Colors.red,
                ),
                onPressed: () {
                  Helper().getDialog(
                      message: _dialogMessage.tr(),
                      acceptButtonText: _approve.tr(),
                      onAccept: () async {
                        await _deleteMultiVideo();
                      });
                },
              ),
            ],
            !isSelectModeEnabled
                ? TextButton(
                    child: Text(
                      _selectButton,
                      style: TextStyle(fontSize: 16, color: AppColors.main),
                    ).tr(),
                    onPressed: () {
                      setState(() {
                        isSelectModeEnabled = true;
                      });
                    },
                  )
                : IconButton(
                    icon: const Icon(
                      Icons.list,
                    ),
                    onPressed: () {
                      if (isSelectModeEnabled) {
                        selectAllValidVideos();
                      } else {
                        isSelectModeEnabled = true;
                      }
                    },
                  ),
          ],
        ),
        body: _isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : _validVideos.isEmpty
                ? Center(
                    child: Text(
                      _noVideos,
                      style: const TextStyle(
                        color: AppColors.grey,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ).tr(),
                  )
                : GridView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 6),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 0,
                      childAspectRatio: 9 / 8,
                    ),
                    itemCount: _validVideos.length,
                    itemBuilder: (context, index) {
                      final video = _validVideos[index];
                      final thumbnail = _thumbnails[index];
                      return Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              if (isSelectModeEnabled) {
                                setState(() {
                                  _isSelected[index] = !_isSelected[index];
                                });
                              } else {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => VideoPreviewPage(
                                      savedVideoModel: video,
                                    ),
                                  ),
                                ).then(
                                  (video) {
                                    if (video != null) {
                                      setState(() {
                                        _validVideos.remove(video);
                                      });
                                    }
                                  },
                                );
                              }
                            },
                            onLongPress: () {
                              if (!isSelectModeEnabled) {
                                setState(() {
                                  isSelectModeEnabled = true;
                                  _isSelected[index] = true;
                                });
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: AppColors.borderRadiusAll,
                                color: AppColors.panelBackground,
                                border: Border.all(
                                  color: _isSelected[index] ? AppColors.main : Colors.transparent,
                                  width: _isSelected[index] ? 2 : 0,
                                ),
                              ),
                              margin: const EdgeInsets.all(2),
                              child: Column(
                                children: [
                                  Stack(
                                    children: [
                                      ClipRRect(
                                        borderRadius: AppColors.borderRadiusTop,
                                        child: AspectRatio(
                                          aspectRatio: 16 / 9,
                                          child: Image.memory(
                                            thumbnail,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 6.0),
                                          child: Text(video.createdAt.toString().substring(0, 16)),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ));
  }

  Future<void> _deleteMultiVideo() async {
    setState(() {
      _isLoading = true;
    });

    List<int> indexesToDelete = [];
    for (int i = 0; i < _isSelected.length; i++) {
      if (_isSelected[i]) {
        indexesToDelete.add(i);
      }
    }

    for (int i = indexesToDelete.length - 1; i >= 0; i--) {
      int index = indexesToDelete[i];
      VideoModelCacheService().deleteVideo(index);
      File file = File(_validVideos[index].path);
      if (await file.exists()) {
        await file.delete();
      }
      _validVideos.removeAt(index);
      _thumbnails.removeAt(index);
      _resolutions.removeAt(index);
      _isSelected.removeAt(index);
    }

    setState(() {
      _isLoading = false;
      isSelectModeEnabled = false;
    });
  }

  void selectAllValidVideos() {
    for (int i = 0; i < _validVideos.length; i++) {
      _isSelected[i] = true;
    }
    setState(() {});
  }

  Future<void> _getVideos() async {
    setState(() {
      _isLoading = true;
    });

    _videos = await VideoModelCacheService().getVideoList();
    for (var video in _videos) {
      if (await File(video.path).exists()) {
        _resolutions.add(video.resolution);
        _thumbnails.add(video.thumbnailByte);
        _validVideos.add(video);
        _isSelected.add(false);
      } else {
        // This video is invalid then we can delete on cache
        video.delete();
      }
    }

    setState(() {
      _isLoading = false;
    });
  }
}

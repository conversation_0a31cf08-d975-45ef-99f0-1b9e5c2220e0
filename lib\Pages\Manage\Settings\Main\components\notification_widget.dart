import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/notification_services.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/state/Provider/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationWidget extends StatefulWidget {
  final bool isTutorial;
  const NotificationWidget({
    super.key,
    this.isTutorial = false,
  });

  @override
  State<NotificationWidget> createState() => _NotificationWidgetState();
}

class _NotificationWidgetState extends State<NotificationWidget> {
  // Texts
  final String _invalidDateTime = LocaleKeys.SettingsPage_SettingsTiles_DailyReminder_InvalidDateTime.tr();
  final String _header = LocaleKeys.SettingsPage_SettingsTiles_DailyReminder_Header.tr();

  @override
  Widget build(BuildContext context) {
    context.watch<ThemeProvider>().themeMode;

    return Ink(
      decoration: BoxDecoration(
        color: AppColors.panelBackground,
        borderRadius: widget.isTutorial ? AppColors.borderRadiusAll : AppColors.borderRadiusTop,
      ),
      child: InkWell(
        borderRadius: widget.isTutorial ? AppColors.borderRadiusAll : AppColors.borderRadiusTop,
        onTap: () async {
          TimeOfDay? selectedTime = await showTimePicker(
            context: context,
            initialTime: notificationTime,
            errorInvalidText: _invalidDateTime,
            barrierColor: AppColors.black.withValues(alpha: 0.65),
          );

          if (selectedTime != null) {
            await NotificationServices().setDailyNotification(
              context,
              selectedTime: selectedTime,
            );
            setState(() {});
          }
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 5,
            horizontal: 10,
          ),
          child: Row(
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.notifications,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    _header,
                    style: TextStyle(
                      fontSize: 45.sp,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: ClipRRect(
                  child: SizedBox(
                    width: 60,
                    height: 30,
                    child: Center(
                      child: Text(
                        '${notificationTime.hour.toString().padLeft(2, '0')}:${notificationTime.minute.toString().padLeft(2, '0')}',
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Switch.adaptive(
                value: isNotificationsEnabled,
                trackOutlineColor: isNotificationsEnabled ? WidgetStateProperty.all(AppColors.transparent) : WidgetStateProperty.all(AppColors.dirtyRed),
                inactiveThumbColor: AppColors.dirtyRed,
                inactiveTrackColor: AppColors.white,
                onChanged: (isNotificationEnabled) async {
                  isNotificationsEnabled = isNotificationEnabled;
                  SharedPreferences prefs = await SharedPreferences.getInstance();
                  prefs.setBool('notificationsEnabled', isNotificationsEnabled);

                  if (!isNotificationsEnabled) {
                    LogService().notificationOnOff(notificationTime.format(context).toString());
                    await NotificationServices().cancelAllNotificaitons();
                  } else {
                    await NotificationServices().setDailyNotification(context);
                  }

                  setState(() {});
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/Music/mixin/add_music_mixin.dart';
import 'package:facelog/Pages/Produce/Video/Music/view/components/done_button.dart';
import 'package:facelog/Pages/Produce/Video/Music/view/components/progress_bar_view.dart';
import 'package:facelog/core/CostumWidgets/video_viewer_component.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddMusicView extends StatefulWidget {
  final String audioFilePath;
  final Duration durationOfMusic;
  final double videoDuration;
  final bool isBrandLogoActive;
  final bool isAsset;

  const AddMusicView({
    super.key,
    required this.audioFilePath,
    required this.durationOfMusic,
    required this.videoDuration,
    required this.isBrandLogoActive,
    required this.isAsset,
  });

  @override
  State<AddMusicView> createState() => _AddMusicViewState();
}

class _AddMusicViewState extends State<AddMusicView> with AddMusicMixin {
  // Texts
  final String _pageTitle = LocaleKeys.AddMusicPage_Title.tr();

  @override
  void initState() {
    super.initState();

    LogService().logScreen("AddMusicView");
  }

  @override
  Widget build(BuildContext context) {
    containerCenter = (MediaQuery.of(context).size.width - rangerWidth) / 2;

    // TODO: Kullanıcı videonun ileri bir kısmına atladıysa value buna göre değişmeli.
    double value = (((currentPosition.inSeconds - startSecond) / (endSecond - startSecond)) * rangerWidth);

    double sliderFillerValue = isScrolling ? 0.0 : value;

    int itemNumber = (widget.durationOfMusic.inSeconds / widget.videoDuration * 15).toInt() + 2;
    return Scaffold(
      appBar: AppBar(
        title: Text(_pageTitle),
        actions: [
          DoneButton(
            isLoading: isLoading,
            widget: widget,
            startSecond: startSecond,
          ),
        ],
      ),
      body: FutureBuilder(
          future: initializeVideoPlayerFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              return SafeArea(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(height: isLandscape ? 0.25.sh : 0.03.sh),
                    VideoViewerComponent(
                      togglePlayPause: togglePlayPause,
                      controller: controller,
                      isTimerActive: true,
                      allowScrubbing: false,
                    ),
                    SizedBox(height: isLandscape ? 0.12.sh : 0.02.sh),
                    RangerAndProgressBarView(
                      scrollController: scrollController,
                      itemNumber: itemNumber,
                      containerCenter: containerCenter,
                      spaceBetweenContainers: spaceBetweenContainers,
                      singleLineWidth: singleLineWidth,
                      rangerWidth: rangerWidth,
                      sliderFillerValue: sliderFillerValue,
                      timerScrollListener: timerScrollListener,
                      videoLength: widget.videoDuration,
                      currentPosition: currentPosition,
                      durationOfMusic: widget.durationOfMusic,
                    ),
                  ],
                ),
              );
            } else {
              return SizedBox(
                height: 0.75.sh,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }
          }),
    );
  }
}

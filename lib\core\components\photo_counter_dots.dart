import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

final class PhotoCounterDots extends StatelessWidget {
  final OneDayPhotos oneDayPhotos;
  final int currentPhotoIndex;

  const PhotoCounterDots({
    super.key,
    required this.oneDayPhotos,
    required this.currentPhotoIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: AnimatedSmoothIndicator(
          activeIndex: currentPhotoIndex,
          count: oneDayPhotos.photos.length,
          effect: ScrollingDotsEffect(
            activeDotColor: AppColors.main,
            dotColor: AppColors.grey,
            dotHeight: 8,
            dotWidth: 8,
            activeDotScale: 1.5,
            maxVisibleDots: 7,
          ),
        ),
      ),
    );
  }
}

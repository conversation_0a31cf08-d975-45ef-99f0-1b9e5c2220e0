import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';

class RangeMusicComponent extends StatelessWidget {
  final double containerCenter;
  final double rangerWidth;
  final double sliderFillerValue;

  const RangeMusicComponent({
    super.key,
    required this.containerCenter,
    required this.rangerWidth,
    required this.sliderFillerValue,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: containerCenter,
      top: 0,
      child: ShaderMask(
        shaderCallback: (bounds) => LinearGradient(
          colors: [
            AppColors.main,
            Colors.yellow,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ).createShader(bounds),
        child: IgnorePointer(
          child: Stack(
            children: [
              // TODO: Geri gitme animasyonunu kapatarak AnimatedContainer ile akıcılık sağla.
              // TODO: sliderfillervalue negatif olabiliyor. bazen de 135 den büyük oluyor. Mantıklı bir çözüm bulunup güzel olsun.
              Container(
                padding: const EdgeInsets.all(6),
                width: sliderFillerValue >= 0
                    ? sliderFillerValue <= rangerWidth
                        ? sliderFillerValue
                        : 135
                    : 0,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  color: AppColors.main.withValues(alpha: 0.3),
                ),
              ),
              Container(
                width: rangerWidth,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  border: Border.all(
                    width: 3,
                    color: AppColors.main,
                  ),
                  color: Colors.transparent,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

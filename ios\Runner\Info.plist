<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb771592641817537</string>
				</array>
			</dict>
		</array>
		<key>FacebookAppID</key>
		<string>771592641817537</string>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>timelapse app</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to location when open.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Uygulamanızın kullan<PERSON><PERSON><PERSON> fotoğraflarına erişim izni gerekmektedir.</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Facelog</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleLocalizations</key>
		<array>
			<string>en</string>
			<string>tr</string>
		</array>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>facelog</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<!-- Download Feature TODO: hiveler vs görünmesin sadece kullanıcı için olan ögeler görünsün. -->
		<key>UISupports Document Browser</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>UIFileSharingEnabled</key>
		<true/>
		
		<!-- Bildirim izni için -->
		<key>UIBackgroundModes</key>
		<array>
    		<string>fetch</string>
    		<string>remote-notification</string>
		</array>
		<!-- Kamera izni için. -->
		<key>NSCameraUsageDescription</key>
		<string>App requires access to your camera to let you add pictures to server.</string>
		<!-- Kullanımda konum izini için. -->
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>We need to access your location to add location to your photo.</string>
		<!-- Fotoğraf izinleri. -->
		<key>NSPhotoLibraryUsageDescription</key>
		<string>We need to access your photos to you import the photos.</string>
	</dict>
</plist>

import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';

final class ExportLoadingWidget extends StatefulWidget {
  const ExportLoadingWidget({
    super.key,
    required this.isWithProgress,
    this.onCancel,
  });

  final bool isWithProgress;
  final VoidCallback? onCancel;

  @override
  State<ExportLoadingWidget> createState() => _ExportLoadingWidgetState();
}

class _ExportLoadingWidgetState extends State<ExportLoadingWidget> {
  @override
  void initState() {
    super.initState();

    croppedPhotoIndexForLoading.addListener(_croppedPhotoIndexListener);
  }

  @override
  void dispose() {
    croppedPhotoIndexForLoading.removeListener(_croppedPhotoIndexListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.isWithProgress
        ? Stack(
            children: [
              ModalBarrier(
                color: AppColors.black.withValues(alpha: 0.5),
                dismissible: false,
              ),
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      value: croppedPhotoIndexForLoading.value / allPhotosCountForLoading,
                      backgroundColor: AppColors.white,
                      color: AppColors.main,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      '${croppedPhotoIndexForLoading.value} / $allPhotosCountForLoading işleniyor',
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Fotoğraf analizi devam ediyor...',
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 14,
                      ),
                    ),
                    if (widget.onCancel != null) ...[
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: widget.onCancel,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.red,
                          foregroundColor: AppColors.white,
                        ),
                        child: const Text('İptal Et'),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          )
        : Stack(
            children: [
              ModalBarrier(
                color: AppColors.black.withValues(alpha: 0.5),
                dismissible: false,
              ),
              const Center(child: CircularProgressIndicator())
            ],
          );
  }

  void _croppedPhotoIndexListener() {
    if (mounted) {
      setState(() {});
    }
  }
}

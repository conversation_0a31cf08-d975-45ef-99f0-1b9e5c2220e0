// ignore_for_file: file_names

import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:hive_flutter/adapters.dart';

part 'one_day_photos.g.dart';

@HiveType(typeId: 4)
class OneDayPhotos extends HiveObject {
  @HiveField(0)
  DateTime date;
  @HiveField(1)
  List<Photo> photos;
  @HiveField(2)
  String? notes;

  OneDayPhotos({
    required this.date,
    required this.photos,
    this.notes,
  });
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pushable_button/pushable_button.dart';

class ContinueButton extends StatelessWidget {
  ContinueButton({
    super.key,
  });

  // Texts
  final String continueButton = LocaleKeys.Home_Streak_Continue.tr();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 0.5.sw,
      child: PushableButton(
        height: 52,
        elevation: 2,
        hslColor: HSLColor.fromColor(AppColors.main),
        shadow: const BoxShadow(
          color: AppColors.transparantBlack,
          blurRadius: 3,
          offset: Offset(0, 2),
        ),
        onPressed: () {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const NavBarAndPages(isFirst: false)),
            (route) => false,
          );
        },
        child: Text(
          continueButton,
          style: TextStyle(
            color: AppColors.white,
            fontSize: 70.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

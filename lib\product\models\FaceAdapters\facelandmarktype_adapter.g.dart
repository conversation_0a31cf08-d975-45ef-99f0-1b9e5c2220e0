// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'facelandmarktype_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FaceLandmarkTypeAdapterAdapter
    extends TypeAdapter<FaceLandmarkTypeAdapter> {
  @override
  final int typeId = 7;

  @override
  FaceLandmarkTypeAdapter read(BinaryReader reader) {
    return FaceLandmarkTypeAdapter();
  }

  @override
  void write(BinaryWriter writer, FaceLandmarkTypeAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FaceLandmarkTypeAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

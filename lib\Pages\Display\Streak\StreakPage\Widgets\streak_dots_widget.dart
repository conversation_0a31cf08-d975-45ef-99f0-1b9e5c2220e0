import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/streak_dot.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/models/streak_model.dart';
import 'package:flutter/material.dart';

class StreaksDotsWidget extends StatelessWidget {
  const StreaksDotsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        7,
        (index) => StreakDot(
          streakModel: index > 4
              ? StreakModel(
                  date: DateTime.now().add(Duration(days: index - 4)),
                  status: StreakStatus.empty,
                )
              : streakList[(streakList.length - 5) + index],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Items/TutorialPage/import_export_tutorial.dart';
import 'package:facelog/Pages/Manage/Settings/Items/TutorialPage/notification_tutorial.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/core/CostumWidgets/select_orientation_view.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:introduction_screen/introduction_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

final class TutorialPage extends StatefulWidget {
  const TutorialPage({super.key});

  @override
  State<TutorialPage> createState() => _TutorialPageState();
}

class _TutorialPageState extends State<TutorialPage> {
  bool isLoading = false;

  @override
  void initState() {
    super.initState();

    LogService().logScreen("TutorialPage");

    if (isFirstLogin) {
      LogService().userNativeLanguage();
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> rawPages = [
      if (isFirstLogin) ...[
        const SelectOrientationPage(
          isTutorialPage: true,
        ),
      ],
      NotificaitonTutorialPage(),
      ImportExportTutorialPage(),
    ];

    return IgnorePointer(
      ignoring: isLoading,
      child: PopScope(
        canPop: false,
        child: Scaffold(
          body: IntroductionScreen(
              scrollPhysics: const ClampingScrollPhysics(),
              controlsPadding: const EdgeInsets.all(10),
              rawPages: rawPages,
              onDone: () async {
                setState(
                  () => isLoading = true,
                );

                await savePreferences();

                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NavBarAndPages(isFirst: true),
                  ),
                  (route) => false,
                );
              },
              showDoneButton: true,
              showNextButton: true,
              showSkipButton: true,
              skip: const Text(
                LocaleKeys.TutorialPage_GeneralButtonText_Skip,
              ).tr(),
              next: const Text(
                LocaleKeys.TutorialPage_GeneralButtonText_Next,
              ).tr(),
              done: const Text(
                LocaleKeys.TutorialPage_GeneralButtonText_Done,
              ).tr(),
              dotsDecorator: getDotsDecorator(context)),
        ),
      ),
    );
  }

  // Function to save preferences
  Future<void> savePreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isFirstLogin', false);
    await prefs.setBool('isLandscape', isLandscape);

    LogService().changeOrientation();

    isFirstLogin = false;
  }

  DotsDecorator getDotsDecorator(BuildContext context) {
    return DotsDecorator(
      spacing: const EdgeInsets.symmetric(horizontal: 1),
      activeColor: AppColors.main,
      color: AppColors.grey,
      activeSize: const Size(12, 8),
      activeShape: RoundedRectangleBorder(
        borderRadius: AppColors.borderRadiusCircular,
      ),
    );
  }
}

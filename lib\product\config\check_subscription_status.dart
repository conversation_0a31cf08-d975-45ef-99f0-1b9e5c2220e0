import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> checkSubscriptionStatus() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();

  final String? purchaseDateStr = prefs.getString('purchaseDate');

  isPremium = prefs.getBool('isPremium');

  if (isPremium == null) {
    // InAppPurchase isPremium Check
    isPremium = false;
    prefs.setBool('isPremium', isPremium!);
    prefs.remove('purchaseDate');

    final InAppPurchase inAppPurchase = InAppPurchase.instance;
    late StreamSubscription<List<PurchaseDetails>> subscription;
    final Stream<List<PurchaseDetails>> purchaseUpdated = inAppPurchase.purchaseStream;
    subscription = purchaseUpdated.listen((List<PurchaseDetails> purchaseDetailsList) async {
      for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
        if (purchaseDetails.status == PurchaseStatus.restored) {
          debugPrint(isPremium.toString());
          isPremium = true;
          prefs.setBool('isPremium', true);
          final String purchaseDate = (DateTime.now().add(const Duration(days: 31))).toIso8601String();
          await prefs.setString('purchaseDate', purchaseDate);

          LogService().checkSubscription("restoredFirstOpen");
        } else {
          isPremium = false;
          prefs.setBool('isPremium', isPremium!);
          prefs.remove('purchaseDate');

          LogService().checkSubscription("notSubscribedFirstOpen");
        }
        subscription.cancel();
      }
    }, onDone: () {
      debugPrint('dinleme işlemi tamamlandı.');
      subscription.cancel();
    }, onError: (Object error) {
      debugPrint('Abonelik durumunuzu kontrol edemiyoruz. Lütfen tekrar kontrol edin.');
      prefs.setBool('premiumAlert', true);

      isPremium = false;
      prefs.setBool('isPremium', isPremium!);
      prefs.remove('purchaseDate');

      subscription.cancel();

      LogService().checkSubscription("alertFirstOpen");
    });

    await inAppPurchase.restorePurchases();
  } else if (!isPremium!) {
    debugPrint('devam et');
    return;
  } else if (purchaseDateStr != null) {
    final DateTime purchaseDate = DateTime.parse(purchaseDateStr);
    final DateTime currentDate = DateTime.now();
    final bool isExpired = purchaseDate.isBefore(currentDate);

    if (isExpired) {
      List<ConnectivityResult> connectivityResults = await (Connectivity().checkConnectivity());

      if (connectivityResults.contains(ConnectivityResult.none)) {
        debugPrint('İnternet erişimimiz olmadığı için abonelik durumunuzu kontrol edemiyoruz. Lütfen internet bağlantınızı kontrol edin.');
        prefs.setBool('premiumAlert', true);

        isPremium = false; //abonelik o dönem için dolmuş.
        prefs.setBool('isPremium', isPremium!);
        prefs.remove('purchaseDate');

        LogService().checkSubscription("alertForConnection");
      } else {
        debugPrint('İnternetiniz açık ve 1 aylık aboneliğinizin devam edip etmediğini kontrol ediyoruz.');

        // InAppPurchase isPremium Check
        final InAppPurchase inAppPurchase = InAppPurchase.instance;
        late StreamSubscription<List<PurchaseDetails>> subscription;
        final Stream<List<PurchaseDetails>> purchaseUpdated = inAppPurchase.purchaseStream;

        subscription = purchaseUpdated.listen((List<PurchaseDetails> purchaseDetailsList) async {
          if (purchaseDetailsList.isEmpty) {
            debugPrint('Satın alma işlemi gerçekleşmedi.');
            isPremium = false;
            prefs.setBool('isPremium', isPremium!);
            prefs.remove('purchaseDate');
            subscription.cancel();

            LogService().checkSubscription("purchaseDetailsEmpty");
          }
          for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
            if (purchaseDetails.status == PurchaseStatus.restored) {
              debugPrint(isPremium.toString());
              isPremium = true;
              prefs.setBool('isPremium', true);
              final String purchaseDate = (DateTime.now().add(const Duration(days: 31))).toIso8601String();
              await prefs.setString('purchaseDate', purchaseDate);

              LogService().checkSubscription("restored");
            } else {
              isPremium = false;
              prefs.setBool('isPremium', isPremium!);
              prefs.remove('purchaseDate');

              LogService().checkSubscription("expired");
            }
            subscription.cancel();
          }
        }, onDone: () {
          debugPrint('dinleme işlemi tamamlandı.');
          subscription.cancel();
        }, onError: (Object error) {
          debugPrint('Abonelik durumunuzu kontrol edemiyoruz. Lütfen tekrar kontrol edin.');
          prefs.setBool('premiumAlert', true);

          isPremium = false;
          prefs.setBool('isPremium', isPremium!);
          prefs.remove('purchaseDate');

          subscription.cancel();

          LogService().checkSubscription("alert");
        });

        await inAppPurchase.restorePurchases();
      }
    } else {
      debugPrint('Henüz Abonelik kontrolü yapmamız gereken bir süre geçmedi. Kullanıcı abone.');
      return;
    }
  } else {
    debugPrint('Bu kullanıcı mevcut dönemde abone olmamış.');
    return;
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'facecontour_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FaceContourAdapterAdapter extends TypeAdapter<FaceContourAdapter> {
  @override
  final int typeId = 11;

  @override
  FaceContourAdapter read(BinaryReader reader) {
    return FaceContourAdapter();
  }

  @override
  void write(BinaryWriter writer, FaceContourAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FaceContourAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

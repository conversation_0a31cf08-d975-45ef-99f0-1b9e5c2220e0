import 'package:facelog/product/models/ReferanceParts/refrance_parts.dart';
import 'package:facelog/product/models/streak_model.dart';
import 'package:flutter/material.dart';

// first
late bool isFirstLogin;

// Streak
late int freezeCount;
List<StreakModel> streakList = [];

// App
late bool isFreeTrailUsed;
bool? isPremium;
late bool isLandscape;
bool navbarIsVisible = true;

// Camera Settings
late ReferanceParts refParts;
late bool isAutoTake;
late bool isRefPartsActive;
late bool isGridActive;
late bool isGhostImageActive;
late bool isStatisticsActive;
late bool isFrontCamera;

late int lastPhotoNumber;

// Advaced Settings
late bool isLocationFeatureActive;
late bool isDescriptionFeatureActive;
late bool isFavoriFeatureActive;
late bool isFeedPageFeatureActive;

// Internet
late bool isConnected;

// Notificaiton
late TimeOfDay notificationTime;
late bool isNotificationsEnabled;

// Rating Dialog
late bool isRated;

// Photo
late double photoSize;

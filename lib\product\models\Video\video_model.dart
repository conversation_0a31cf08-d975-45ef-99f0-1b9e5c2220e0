import 'dart:typed_data';

import 'package:hive_flutter/hive_flutter.dart';

part 'video_model.g.dart';

@HiveType(typeId: 12)
class VideoModel extends HiveObject {
  @HiveField(0)
  final String path;
  @HiveField(1)
  final DateTime createdAt;
  @HiveField(2)
  final int photoCount;
  @HiveField(3)
  final DateTime firstPhotoDate;
  @HiveField(4)
  final DateTime lastPhotoDate;
  // ! 4 den sonra 6 gelecek. düzeltme. hive hata veriyor önceden öyle kaydedildiği için
  @HiveField(6)
  final String resolution;
  @HiveField(7)
  final Uint8List thumbnailByte;
  // ? buil 21 den önce kullanıcı kalmadığında  nullable olmak zorunda kalmayacak. sadece music name nullable olacak
  @HiveField(8)
  final String? duration;
  @HiveField(9)
  final String? durationPerPhoto;
  @HiveField(10)
  final int? dayCount;
  @HiveField(11)
  final String? musicName;
  @HiveField(14)
  final bool? isBrandLogoActive;

  VideoModel({
    required this.path,
    required this.createdAt,
    required this.photoCount,
    required this.firstPhotoDate,
    required this.lastPhotoDate,
    required this.resolution,
    required this.thumbnailByte,
    required this.duration,
    required this.durationPerPhoto,
    required this.dayCount,
    required this.musicName,
    required this.isBrandLogoActive,
  });
}

import 'package:audioplayers/audioplayers.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/models/sound_model.dart';
import 'package:flutter/material.dart';

TextEditingController videoLengthInputController = TextEditingController();

List<String> qualityList = ['240p', '360p', '480p', '720p', '1080p'];
List<String> videoSpeedList = ['1', '3', '5', '10', '20', '30'];
int activeQualityIndex = 3; // default value of quality
int activeSpeedIndex = 2; // default value of video length

bool isBrandLogoActive = true;

late List<AnimationController> soundRotationAnimController;

List<SoundModel> soundModelList = [
  SoundModel(
    name: 'Silence of the Amps',
    path: AssetsPath.silenceOfTheAmps,
    duration: const Duration(seconds: 231),
    position: Duration.zero,
    isSelected: false,
    isPlaying: false,
    isObjectPermanent: true,
    audioPlayer: AudioPlayer(),
  ),
];

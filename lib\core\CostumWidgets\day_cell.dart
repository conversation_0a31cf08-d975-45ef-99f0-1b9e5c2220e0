import 'dart:io';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:flutter/material.dart';

class DayCell extends StatefulWidget {
  final OneDayPhotos oneDayPhoto;

  const DayCell({
    super.key,
    required this.oneDayPhoto,
  });

  @override
  State<DayCell> createState() => DayCellState();
}

class DayCellState extends State<DayCell> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox.expand(
          child: ClipRRect(
            borderRadius: AppColors.borderRadiusAll,
            child: Image.file(
              cacheHeight: 150,
              File(widget.oneDayPhoto.photos.first.path),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Align(
          alignment: Alignment.center,
          child: Text(
            '${widget.oneDayPhoto.date.day}', //* TODO: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bölgesinde tarih nasıl gösteriliyorsa ona göre tarih almalıyız. Mesela Amerikada gün aydan sonra.
            style: TextStyle(
              color: AppColors.white.withValues(alpha: 0.7),
              fontSize: 16,
              fontWeight: FontWeight.bold,
              shadows: AppColors.basicShadow,
            ),
          ),
        ),
        widget.oneDayPhoto.photos.any((photo) => photo.isFavorite && isFavoriFeatureActive)
            ? const Align(
                alignment: Alignment.topLeft,
                child: Padding(
                  padding: EdgeInsets.all(2),
                  child: Icon(
                    Icons.favorite,
                    color: AppColors.red,
                    size: 12,
                  ),
                ),
              )
            : const SizedBox(),
      ],
    );
  }
}

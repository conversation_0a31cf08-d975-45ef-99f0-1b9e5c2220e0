import 'dart:io';
import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/camera_mixin.dart';
import 'package:facelog/Pages/Produce/CameraProgress/components/face_parts.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/ReferanceParts/refrance_parts.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/photo/referance_parts_service.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:showcaseview/showcaseview.dart';

class SetFaceParts extends StatefulWidget {
  final String photoPath;
  final String? address;
  final String? dayNoteText;

  const SetFaceParts({
    super.key,
    required this.photoPath,
    required this.address,
    required this.dayNoteText,
  });

  @override
  State<SetFaceParts> createState() => _SetFacePartsState();
}

class _SetFacePartsState extends State<SetFaceParts> with PhotoSaveMixin {
  // Texts
  final String _title = LocaleKeys.CameraPages_Camera_SetReferanceFace.tr();
  final String refPartsTutorial = LocaleKeys.CameraPages_Camera_SetReferanceFace.tr();
  final String refPartsTutorialDesc = LocaleKeys.CameraPages_Camera_RefPartsDescTutorial.tr();

  bool showReferanceParts = true;

  bool isLoading = false;

  // Showcase Keys
  late BuildContext contextForShowcase;
  final GlobalKey refPartKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    LogService().logScreen("SetFaceParts");
  }

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      disableMovingAnimation: true,
      builder: (BuildContext context) {
        contextForShowcase = context;
        return Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            leading: IconButton(
              padding: const EdgeInsets.only(left: 10),
              icon: const Icon(
                Icons.arrow_back_ios,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            actions: [
              IconButton(
                icon: const Icon(
                  Icons.question_mark,
                ),
                onPressed: () {
                  ShowCaseWidget.of(contextForShowcase).startShowCase(
                    [
                      refPartKey,
                    ],
                  );
                },
              ),
              const SizedBox(width: 5)
            ],
            title: Text(_title),
            backgroundColor: AppColors.background,
          ),
          body: Column(
            children: [
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: AppColors.highBorderRadiusAll,
                    child: SizedOverflowBox(
                      size: Size(1.sw, 0.76.sh),
                      child: Image.file(
                        File(widget.photoPath),
                      ),
                    ),
                  ),
                  if (showReferanceParts)
                    Positioned.fill(
                      child: Image.asset(
                        AssetsPath.grid,
                        scale: 1,
                      ),
                    ),
                  if (showReferanceParts) ...[
                    eyes1(
                      setStateFunc: () {
                        setState(() {});
                      },
                      showcaseKey: refPartKey,
                      showcaseTitle: LocaleKeys.CameraPages_Camera_SetReferanceFace.tr(),
                      showcaseDesc: LocaleKeys.CameraPages_Camera_RefPartsDescTutorial.tr(),
                    ),
                    eyes2(setStateFunc: () {
                      setState(() {});
                    }),
                    mouth(
                      setStateFunc: () {
                        setState(() {});
                      },
                    ),
                  ],
                  Positioned(
                    bottom: 10,
                    right: 20,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.background.withValues(alpha: 0.7),
                      ),
                      child: IconButton(
                        onPressed: () {
                          setState(() {
                            showReferanceParts = !showReferanceParts;
                          });
                        },
                        icon: Transform.rotate(
                          angle: isLandscape ? pi / 2 : 0,
                          child: Icon(
                            showReferanceParts ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                            size: 30,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Transform.rotate(
                        angle: isLandscape ? pi / 2 : 0,
                        child: const Icon(
                          Icons.close,
                          size: 60,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        if (isLoading) return;
                        isLoading = true;

                        //hive kaydet
                        ReferancePartsService().addReferanceParts(
                          ReferanceParts(
                            eye1: refParts.eye1,
                            eye2: refParts.eye2,
                            mouth: refParts.mouth,
                          ),
                        );
                        debugPrint('eye1: ${refParts.eye1}');
                        debugPrint('eye2: ${refParts.eye2}');
                        debugPrint('mouth: ${refParts.mouth}');

                        await savePhoto(
                          address: widget.address,
                          context: context,
                          photoPath: widget.photoPath,
                          dayNoteText: widget.dayNoteText,
                        );

                        // TODO: kafa hizalama göze göre olduğunda bunu kontrol et
                        // referanceFace = currentFace!;
                        // await FaceDetailService().saveReferanceFace(currentFace!);

                        isLoading = false;
                      },
                      child: Container(
                        color: AppColors.transparent,
                        child: Row(
                          children: [
                            Text(
                              LocaleKeys.CameraPages_Camera_SaveFaceParts,
                              style: TextStyle(
                                color: AppColors.text,
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                              ),
                            ).tr(),
                            const SizedBox(width: 10),
                            const Icon(
                              Icons.send,
                              size: 40,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
            ],
          ),
        );
      },
    );
  }
}

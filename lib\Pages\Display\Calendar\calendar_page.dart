import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/CostumWidgets/day_cell.dart';
import 'package:facelog/core/CostumWidgets/photo_information_widget.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/navbar_provider.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/models/CalendarModel/sample_view_calendar.dart';
import 'package:facelog/core/CostumWidgets/no_photo.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class CalendarPage extends SampleView {
  const CalendarPage({
    super.key,
  });

  @override
  CalendarPageState createState() => CalendarPageState();
}

class CalendarPageState extends SampleViewState {
  CalendarPageState();

  late DateTime minDate;
  late DateTime maxDate;

  late List<OneDayPhotos> allPhotoList = context.read<PhotoProvider>().allPhotosList;

  late OneDayPhotos? selectedDate;

  @override
  void initState() {
    super.initState();

    if (allPhotoList.isNotEmpty) {
      selectedDate = allPhotoList.last;

      if (allPhotoList.length > 1) {
        minDate = allPhotoList.first.date;
        maxDate = allPhotoList.last.date;
      } else {
        minDate = allPhotoList.first.date;
        // max date min ile eşit olamaz dedği için 1 microsecond ekliyoruz.
        maxDate = minDate.add(const Duration(microseconds: 1));
      }
    }

    LogService().logScreen("CalendarPage");
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          context.read<NavbarProvider>().updateIndex(2);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text(LocaleKeys.Calendar_AppBar).tr(),
        ),
        body: allPhotoList.isEmpty ? const AnyPhotosWidget() : getCalendar(),
      ),
    );
  }

  Widget getCalendar() {
    return SingleChildScrollView(
      child: Column(
        children: [
          AspectRatio(
            aspectRatio: isLandscape ? 1.4 : 0.8,
            child: SfCalendar(
              monthCellBuilder: photoCell,
              minDate: minDate,
              maxDate: maxDate,
              view: CalendarView.month,
              dataSource: DataSource(allPhotoList),
              firstDayOfWeek: 1,
              showTodayButton: true,
              showDatePickerButton: true,
              showNavigationArrow: true,
              onTap: calendarTapped,
              onLongPress: calendarLongPressed,
              todayHighlightColor: AppColors.main,
              selectionDecoration: BoxDecoration(
                borderRadius: AppColors.borderRadiusAll,
                border: Border.all(color: AppColors.main),
              ),
              initialSelectedDate: allPhotoList.last.date,
              viewHeaderHeight: 40,
              monthViewSettings: const MonthViewSettings(
                showTrailingAndLeadingDates: false,
                navigationDirection: MonthNavigationDirection.horizontal,
                dayFormat: 'EEE',
                appointmentDisplayMode: MonthAppointmentDisplayMode.none,
              ),
              headerStyle: CalendarHeaderStyle(
                backgroundColor: AppColors.background,
                textStyle: TextStyle(
                  color: AppColors.text,
                  fontSize: 17,
                ),
              ),
              viewHeaderStyle: ViewHeaderStyle(
                backgroundColor: AppColors.background,
                dayTextStyle: TextStyle(
                  color: AppColors.text,
                  fontSize: 10,
                ),
              ),
            ),
          ),
          if (selectedDate != null) ...[
            PhotoInformationWidget(
              oneDayPhotos: selectedDate!,
              setStateParent: () => setStateCalendar(),
              updateCalendarForDelete: () => updateCalendarForDelete(),
            )
          ]
        ],
      ),
    );
  }

  Widget photoCell(
    BuildContext buildContext,
    MonthCellDetails details,
  ) {
    late OneDayPhotos oneDayPhoto;

    if (details.appointments.isNotEmpty) {
      oneDayPhoto = details.appointments.first as OneDayPhotos;
    }
    if (details.appointments.isEmpty) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            details.date.day.toString(),
            style: const TextStyle(
              color: AppColors.dirtyWhite,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    }
    return Stack(
      children: [
        DayCell(oneDayPhoto: details.appointments.first as OneDayPhotos),
        if (details.appointments.isNotEmpty && oneDayPhoto.photos.length > 1)
          Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  oneDayPhoto.photos.length >= 5 ? 5 : oneDayPhoto.photos.length,
                  (index) => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    height: 5,
                    width: 5,
                    decoration: BoxDecoration(
                      color: AppColors.main,
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  void calendarTapped(CalendarTapDetails calendarTapDetails) {
    if (calendarTapDetails.targetElement == CalendarElement.calendarCell) {
      if (calendarTapDetails.appointments!.isNotEmpty) {
        selectedDate = calendarTapDetails.appointments!.first as OneDayPhotos;
      } else {
        selectedDate = null;
      }
      setState(() {});
    }
  }

  void calendarLongPressed(CalendarLongPressDetails calendarLongPressDetails) {
    if (calendarLongPressDetails.targetElement == CalendarElement.calendarCell && calendarLongPressDetails.appointments!.isNotEmpty) {
      selectedDate = calendarLongPressDetails.appointments!.first as OneDayPhotos;
    } else {
      selectedDate = null;
    }
    setState(() {});
  }

  void setStateCalendar() {
    setState(
      () {},
    );
  }

  void updateCalendarForDelete() {
    if (allPhotoList.isNotEmpty) {
      selectedDate = allPhotoList.last;
    }
    setState(
      () {},
    );
  }
}

class DataSource extends CalendarDataSource {
  DataSource(
    List<OneDayPhotos> source,
  ) {
    appointments = source;
  }

  @override
  DateTime getStartTime(int index) => appointments![index].date;

  @override
  DateTime getEndTime(int index) => appointments![index].date;
}

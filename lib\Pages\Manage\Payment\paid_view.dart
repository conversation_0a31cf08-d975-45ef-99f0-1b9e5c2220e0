import 'package:facelog/Pages/Manage/Payment/components/advantage_feature.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class PaidView extends StatefulWidget {
  const PaidView({super.key});

  @override
  State<PaidView> createState() => _PaidView();
}

class _PaidView extends State<PaidView> {
  @override
  void initState() {
    super.initState();

    LogService().logScreen("PaidView");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              const _CircleSuccessImage(),
              AdvantagePlanFeature(),
              const _SubscriptionText(),
            ],
          ),
        ),
      ),
    );
  }
}

final class _SubscriptionText extends StatelessWidget {
  const _SubscriptionText();

  final TextStyle linkStyle = const TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.blue);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.8,
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: <TextSpan>[
            TextSpan(
              text: 'You can manage your subscription at any time from the ',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: AppColors.text,
              ),
            ),
            TextSpan(
              text: 'Google Play Manage Subscription ',
              style: linkStyle,
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  LogService().manageSubscription();

                  launchUrl(
                    Uri.parse('https://play.google.com/store/account/subscriptions'),
                  );
                },
            ),
            TextSpan(
              text: 'tab.',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: AppColors.text,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

final class _CircleSuccessImage extends StatelessWidget {
  const _CircleSuccessImage();

  @override
  Widget build(BuildContext context) {
    return const CircleAvatar(
      radius: 50,
      backgroundColor: AppColors.green,
      child: Icon(
        Icons.check,
        size: 70,
        color: Colors.white,
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Home/PhotoFix/photo_fix_service.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';

class PhotoFixController {
  final PhotoProvider photoProvider;
  late final PhotoFixService _photoFixService;

  PhotoFixController({required this.photoProvider}) {
    _photoFixService = PhotoFixService(photoProvider: photoProvider);
  }

  /// Kırpılmamış fotoğraf kontrolü
  bool hasUncroppedPhotos() {
    return _photoFixService.hasUncroppedPhotos();
  }

  /// Fotoğraf düzeltme işlemini başlatır
  Future<bool> fixUncroppedPhotos({
    Function(int current, int total)? onProgress,
  }) async {
    try {
      final success = await _photoFixService.fixUncroppedPhotosManually(
        onProgress: onProgress,
      );

      if (success) {
        Helper().getMessage(
          message: LocaleKeys.Home_PhotoFix_SuccessMessage.tr(),
          icon: Icons.check_circle,
        );
        return true;
      } else {
        Helper().getMessage(
          message: LocaleKeys.Home_PhotoFix_NoPhotosMessage.tr(),
          icon: Icons.info,
        );
        return false;
      }
    } catch (e) {
      LogService().logError("Fotoğraf düzeltme hatası: $e");
      Helper().getMessage(
        message: LocaleKeys.Home_PhotoFix_ErrorMessage.tr(),
        icon: Icons.error,
      );
      return false;
    }
  }
}

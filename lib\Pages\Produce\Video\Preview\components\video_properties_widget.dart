part of '../preview_video_view.dart';

final class _VideoProporties extends StatelessWidget {
  final List proportiesList;

  const _VideoProporties({
    required this.proportiesList,
  });

  @override
  Widget build(BuildContext context) {
    return ToggleList(
      shrinkWrap: true,
      children: [
        ToggleListItem(
          title: const Text(
            LocaleKeys.VideoPreferencesPage_VideoPropertiesList_Header,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ).tr(),
          content: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (var i = 0; i < proportiesList.length; i++)
                  Text(
                    "${proportiesList[i]}",
                    style: const TextStyle(
                      fontSize: 20,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

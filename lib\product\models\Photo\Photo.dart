// ignore_for_file: file_names

import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:hive_flutter/adapters.dart';

part 'Photo.g.dart';

@HiveType(typeId: 1)
class Photo extends HiveObject {
  @HiveField(0)
  DateTime? date;
  @HiveField(1)
  String path;
  @HiveField(2)
  bool isFavorite;
  @HiveField(3)
  String? location;
  @HiveField(4)
  // ? normalde null olamyacak güncellemeden öncekiler için şimdilik nullable.
  bool? isImported;
  @HiveField(5)
  Face? face;
  @HiveField(6)
  // ? eski kullanıcıların fotoğraflarının kırpılıp kırpılmadığını kontrol etmek için
  bool? isCropped;

  // ? import edilen fotoğraflar için. belki normalde de kullanılabilir ileride de gösterilecek ise
  double? totalAccuracy;
  bool isAnalyzed = false;

  Photo({
    required this.date,
    required this.path,
    this.isFavorite = false,
    this.location,
    this.isImported = false,
    required this.face,
    this.isCropped = true, // Yeni fotoğraflar varsayılan olarak kırpılmış
  });
}

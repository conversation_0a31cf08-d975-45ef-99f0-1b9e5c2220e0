// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offset_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OffsetAdapterAdapter extends TypeAdapter<OffsetAdapter> {
  @override
  final int typeId = 3;

  @override
  OffsetAdapter read(BinaryReader reader) {
    return OffsetAdapter();
  }

  @override
  void write(BinaryWriter writer, OffsetAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OffsetAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

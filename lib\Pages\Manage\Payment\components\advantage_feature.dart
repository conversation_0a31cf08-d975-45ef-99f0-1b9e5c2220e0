import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';

final class AdvantagePlanFeature extends StatelessWidget {
  AdvantagePlanFeature({
    super.key,
  });

  final List<String> _features = [
    LocaleKeys.PaymentPage_Features_Feature1.tr(),
    LocaleKeys.PaymentPage_Features_Feature2.tr(),
    LocaleKeys.PaymentPage_Features_Feature3.tr(),
    LocaleKeys.PaymentPage_Features_Feature4.tr(),
    LocaleKeys.PaymentPage_Features_Feature5.tr(),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _features
          .asMap()
          .entries
          .map((entry) => Column(
                children: [
                  _buildFeatureItem(entry.key),
                  if (entry.key != _features.length - 1) const SizedBox(height: 4),
                ],
              ))
          .toList(),
    );
  }

  Widget _buildFeatureItem(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Row(
        children: [
          const Icon(
            Icons.check,
            size: 16.0,
          ),
          const SizedBox(width: 10),
          Text(
            _features[index],
            textAlign: TextAlign.left,
            style: const TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

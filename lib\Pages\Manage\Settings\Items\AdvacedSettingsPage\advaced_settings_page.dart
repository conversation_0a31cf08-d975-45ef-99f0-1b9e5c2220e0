import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/state/Provider/navbar_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AdvacedSettingsPage extends StatefulWidget {
  const AdvacedSettingsPage({super.key});

  @override
  State<AdvacedSettingsPage> createState() => _AdvacedSettingsPageState();
}

class _AdvacedSettingsPageState extends State<AdvacedSettingsPage> {
  // // Texts
  // final String _importPhotosTitle = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description_Title.tr();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          // _appbarTitle,
          "Advaced Settings",
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 9),
        child: Column(
          children: [
            SizedBox(height: 4),
            // ! disable location
            // switchWidget(
            //   title: "Photo Location",
            //   icon: Icons.location_on,
            //   value: isLocationFeatureActive,
            //   onTap: () async {
            //     isLocationFeatureActive = !isLocationFeatureActive;

            //     final pref = await SharedPreferences.getInstance();
            //     pref.setBool("isLocationFeatureActive", isLocationFeatureActive);
            //   },
            // ),
            switchWidget(
              title: "Photo Description",
              icon: Icons.description,
              value: isDescriptionFeatureActive,
              onTap: () async {
                isDescriptionFeatureActive = !isDescriptionFeatureActive;

                final pref = await SharedPreferences.getInstance();
                pref.setBool("isDescriptionFeatureActive", isDescriptionFeatureActive);
              },
            ),
            switchWidget(
              title: "Favorites",
              icon: Icons.favorite,
              value: isFavoriFeatureActive,
              onTap: () async {
                isFavoriFeatureActive = !isFavoriFeatureActive;

                final pref = await SharedPreferences.getInstance();
                pref.setBool("isFavoriFeatureActive", isFavoriFeatureActive);
              },
            ),
            switchWidget(
              title: "Feed Page",
              icon: Icons.person_2,
              value: isFeedPageFeatureActive,
              onTap: () async {
                isFeedPageFeatureActive = !isFeedPageFeatureActive;

                final pref = await SharedPreferences.getInstance();
                pref.setBool("isFeedPageFeatureActive", isFeedPageFeatureActive);

                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => const NavBarAndPages(isFirst: false)),
                  (route) => false,
                );
                context.read<NavbarProvider>().currentIndex = isFeedPageFeatureActive ? 2 : 1;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget switchWidget({
    required String title,
    required IconData icon,
    required bool value,
    required Function onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Ink(
        decoration: BoxDecoration(
          color: AppColors.panelBackground,
          borderRadius: AppColors.borderRadiusAll,
        ),
        child: InkWell(
          borderRadius: AppColors.borderRadiusAll,
          onTap: () {
            onTap();
            setState(() {});
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 6,
              horizontal: 10,
            ),
            child: Row(
              children: [
                Row(
                  children: [
                    Icon(
                      icon,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      // TODO:
                      // LocaleKeys.SettingsPage_SettingsTiles_Theme_Header.tr(),
                      title,
                      style: TextStyle(
                        fontSize: 45.sp,
                      ),
                    ).tr(),
                  ],
                ),
                const Spacer(),
                Switch.adaptive(
                  value: value,
                  inactiveThumbColor: AppColors.dirtyRed,
                  inactiveTrackColor: AppColors.white,
                  onChanged: (_) {
                    onTap();
                    setState(() {});
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

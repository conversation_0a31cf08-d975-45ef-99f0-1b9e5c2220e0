import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class FullScreenPhotoView extends StatefulWidget {
  const FullScreenPhotoView({
    super.key,
    required this.oneDayPhotos,
    required this.photo,
  });

  final OneDayPhotos oneDayPhotos;
  final Photo photo;

  @override
  State<FullScreenPhotoView> createState() => _FullScreenPhotoViewState();
}

class _FullScreenPhotoViewState extends State<FullScreenPhotoView> {
  @override
  void initState() {
    super.initState();

    LogService().logScreen("FullScreenPhotoView");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: FullScreenPhotoButton(
          icon: Icons.close,
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          FullScreenPhotoButton(
            icon: Icons.delete,
            onPressed: () async {
              await Helper().getDialog(
                message: LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_PermanentlyDeleteWarning.tr(),
                onAccept: () async {
                  try {
                    await context.read<PhotoProvider>().deletePhoto(photo: widget.photo);
                    Helper().getMessage(
                      message: LocaleKeys.PhotoEvents_ScaffoldMessage_photoDeleted.tr(),
                      icon: Icons.delete,
                    );

                    Navigator.pop(context);
                  } catch (e) {
                    Helper().getMessage(
                      message: LocaleKeys.PhotoEvents_ScaffoldMessage_photoCouldntDeleted.tr(),
                      status: StatusEnum.WARNING,
                    );
                  }
                },
              );
            },
          ),
          FullScreenPhotoButton(
            icon: Icons.file_download,
            onPressed: () {
              Helper().downloadPhoto(
                context,
                widget.photo,
              );
            },
          ),
          FullScreenPhotoButton(
            icon: Icons.share,
            onPressed: () {
              Helper().shareItem(
                widget.photo.path,
              );
            },
          ),
          const SizedBox(width: 15),
        ],
      ),
      body: PhotoView(
        heroAttributes: PhotoViewHeroAttributes(tag: widget.photo.path),
        maxScale: 2.0,
        minScale: 0.17,
        backgroundDecoration: BoxDecoration(
          color: AppColors.background,
        ),
        loadingBuilder: (context, _) {
          return Center(
            child: Shimmer(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.grey,
                  AppColors.black,
                ],
              ),
              child: AspectRatio(
                aspectRatio: isLandscape ? 16 / 9 : 9 / 16,
                child: Container(
                  color: AppColors.black,
                ),
              ),
            ),
          );
        },
        imageProvider: FileImage(File(widget.photo.path)),
      ),
    );
  }
}

class FullScreenPhotoButton extends StatelessWidget {
  final IconData icon;
  final Function() onPressed;

  const FullScreenPhotoButton({
    super.key,
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
        child: Icon(
          icon,
          color: AppColors.text,
        ),
      ),
    );
  }
}

import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/services/streak_helper.dart';
import 'package:flutter/material.dart';

class AnnualStreakCalendarDot extends StatelessWidget {
  final StreakStatus status;

  const AnnualStreakCalendarDot({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: StreakHelper().getDotColor(status),
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.text,
          width: 0.3,
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/CostumWidgets/photo_show_widget.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/core/components/photo_counter_dots.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';

class FeedPhotoWidget extends StatefulWidget {
  final OneDayPhotos oneDayPhotos;

  const FeedPhotoWidget({
    super.key,
    required this.oneDayPhotos,
  });

  @override
  State<FeedPhotoWidget> createState() => _FeedPhotoWidgetState();
}

class _FeedPhotoWidgetState extends State<FeedPhotoWidget> with SingleTickerProviderStateMixin {
  late final AnimationController likeAnimationController;
  late final Animation<double> likeAnimation;

  ValueNotifier<bool> isCollapsed = ValueNotifier<bool>(true);

  int currentPhotoIndex = 0;

  @override
  void initState() {
    super.initState();

    likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    likeAnimation = CurvedAnimation(
      parent: likeAnimationController,
      curve: Curves.elasticOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Stack(
          children: [
            SizedBox(
              height: photoSize,
              child: PageView.builder(
                onPageChanged: (currentPhotoIndexPage) {
                  setState(() {
                    currentPhotoIndex = currentPhotoIndexPage;
                  });
                },
                scrollDirection: Axis.horizontal,
                itemCount: widget.oneDayPhotos.photos.length,
                itemBuilder: (BuildContext context, int currentPhotoIndexPage) {
                  return Center(
                    child: GestureDetector(
                      onDoubleTap: () {
                        if (isFavoriFeatureActive) {
                          context.read<PhotoProvider>().photoChanceFavoriteStates(
                                context: context,
                                oneDayPhotos: widget.oneDayPhotos,
                                selectedPhotoIndex: currentPhotoIndexPage,
                                likeAnimationController: likeAnimationController,
                              );
                        }
                      },
                      child: PhotoShowWidget(
                        oneDayPhotos: widget.oneDayPhotos,
                        currentPhotoIndexPage: currentPhotoIndexPage,
                        likeAnimation: likeAnimation,
                        likeAnimationController: likeAnimationController,
                        isFeedPage: true,
                        setStateParent: () {
                          setState(() {});
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        PhotoCounterDots(
          oneDayPhotos: widget.oneDayPhotos,
          currentPhotoIndex: currentPhotoIndex,
        ),
        if (isDescriptionFeatureActive)
          GestureDetector(
            onTap: () {
              isCollapsed.value = !isCollapsed.value;
            },
            onDoubleTap: () {
              Helper()
                  .showEditDescriptionSheet(
                context: context,
                oneDayPhoto: widget.oneDayPhotos,
              )
                  .then(
                (_) {
                  LogService().editNote(widget.oneDayPhotos.notes == null ? "null" : widget.oneDayPhotos.notes!.length.toString());
                },
              );
            },
            child: Container(
              color: AppColors.transparent,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 20),
              child: ReadMoreText(
                widget.oneDayPhotos.notes == null || widget.oneDayPhotos.notes!.isEmpty ? LocaleKeys.FeedPage_Body_NoDescription.tr() : widget.oneDayPhotos.notes!,
                trimMode: TrimMode.Line,
                colorClickableText: AppColors.text,
                lessStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                trimCollapsedText: LocaleKeys.FeedPage_Body_SeeMore.tr(),
                trimExpandedText: " ${LocaleKeys.FeedPage_Body_SeeLess.tr()}",
                preDataText: DateFormat('dd/MM/yyyy').format(widget.oneDayPhotos.date),
                preDataTextStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w800,
                ),
                moreStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                isCollapsed: isCollapsed,
              ),
            ),
          ),
      ],
    );
  }
}

import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';

class VideoViewerComponent extends StatefulWidget {
  final VideoPlayerController controller;
  final VoidCallback togglePlayPause;
  final bool isTimerActive;
  final bool allowScrubbing;

  const VideoViewerComponent({
    super.key,
    required this.controller,
    required this.togglePlayPause,
    this.isTimerActive = true,
    this.allowScrubbing = true,
  });

  @override
  State<VideoViewerComponent> createState() => _VideoViewerComponentState();
}

class _VideoViewerComponentState extends State<VideoViewerComponent> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isLandscape ? 0.96.sw : 0.8.sw,
      child: ClipRRect(
        borderRadius: AppColors.borderRadiusAll,
        child: GestureDetector(
          onTap: widget.togglePlayPause,
          child: AspectRatio(
            aspectRatio: widget.controller.value.aspectRatio,
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: <Widget>[
                VideoPlayer(widget.controller),
                VideoProgressIndicator(
                  widget.controller,
                  allowScrubbing: widget.allowScrubbing,
                ),
                ControlsOverlay(controller: widget.controller),
                if (widget.isTimerActive) ...[
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: VideoTimer(
                      controller: widget.controller,
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ControlsOverlay extends StatefulWidget {
  const ControlsOverlay({
    super.key,
    required this.controller,
  });

  final VideoPlayerController controller;

  @override
  State<ControlsOverlay> createState() => _ControlsOverlayState();
}

class _ControlsOverlayState extends State<ControlsOverlay> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: widget.controller.value.isPlaying
              ? const SizedBox.shrink()
              : const ColoredBox(
                  color: Colors.black26,
                  child: Center(
                    child: Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 100.0,
                      semanticLabel: 'Play',
                    ),
                  ),
                ),
        ),
      ],
    );
  }
}

class VideoTimer extends StatefulWidget {
  const VideoTimer({
    super.key,
    required this.controller,
  });

  final VideoPlayerController controller;

  @override
  State<VideoTimer> createState() => _VideoTimerState();
}

class _VideoTimerState extends State<VideoTimer> {
  late Duration _position;

  @override
  void initState() {
    _position = widget.controller.value.position;
    widget.controller.addListener(() {
      _updatePosition();
    });

    super.initState();
  }

  @override
  void dispose() {
    widget.controller.removeListener(() {
      _updatePosition();
    });
    super.dispose();
  }

  void _updatePosition() {
    if (mounted) {
      setState(() {
        _position = widget.controller.value.position;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      '${_position.inMinutes.toString().padLeft(2, '0')}:${(_position.inSeconds % 60).toString().padLeft(2, '0')}',
      style: const TextStyle(
        fontSize: 14,
        color: AppColors.white,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

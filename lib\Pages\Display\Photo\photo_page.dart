import 'package:facelog/core/CostumWidgets/photo_information_widget.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';

// ! <PERSON>u say<PERSON> ile "PhotoInformationWidget" aslında aynı ancak birinde appar olduğu için böyle kullanılıyor.
class PhotoPage extends StatefulWidget {
  final OneDayPhotos oneDayPhotos;
  final Photo? selectedPhoto;

  const PhotoPage({
    super.key,
    required this.oneDayPhotos,
    this.selectedPhoto,
  });

  @override
  State<PhotoPage> createState() => _PhotoPageState();
}

class _PhotoPageState extends State<PhotoPage> with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();

    LogService().logScreen("PhotoPage");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: GestureDetector(
        onVerticalDragUpdate: (details) {
          if (details.delta.dy > 10) {
            Navigator.pop(context);
          }
        },
        child: PhotoInformationWidget(
          oneDayPhotos: widget.oneDayPhotos,
          selectedPhoto: widget.selectedPhoto,
          setStateParent: () {
            setState(() {});
          },
        ),
      ),
    );
  }
}

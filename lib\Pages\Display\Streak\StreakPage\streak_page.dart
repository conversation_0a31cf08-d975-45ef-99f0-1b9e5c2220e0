import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/continue_button_widget.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/get_freeze_right.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/photo_count_widget.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/streak_day_widget.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/streak_dots_widget.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/streak_progress_bar.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/streak_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

class StreakPage extends StatefulWidget {
  const StreakPage({super.key});

  @override
  State<StreakPage> createState() => _StreakPageState();
}

class _StreakPageState extends State<StreakPage> {
  double fireScale = 0;

  @override
  void initState() {
    super.initState();

    LogService().logScreen("StreakPage");

    StreakHelper().vibration();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const NavBarAndPages(isFirst: false)),
            (route) => false,
          );
        }
      },
      child: Scaffold(
        body: Center(
          child: Column(
            children: [
              SizedBox(height: 0.1.sh),
              const StreakProgressBar(),
              const GetFreezeRight(),
              SizedBox(height: 0.1.sh),
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
                width: fireScale,
                child: LottieBuilder.asset(
                  AssetsPath.streakAnimation,
                  onLoaded: (composition) {
                    setState(() {
                      fireScale = 550.sp;
                    });
                  },
                ),
              ),
              SizedBox(height: 0.06.sh),
              StreakDayNumber(),
              PhotoCountWidget(),
              SizedBox(height: 0.07.sh),
              const StreaksDotsWidget(),
              SizedBox(height: 0.06.sh),
              ContinueButton(),
            ],
          ),
        ),
      ),
    );
  }
}

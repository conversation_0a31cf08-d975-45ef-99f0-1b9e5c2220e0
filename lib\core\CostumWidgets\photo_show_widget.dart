import 'dart:io';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/FullSizePhoto/full_screen_photo_view.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinch_zoom_release_unzoom/pinch_zoom_release_unzoom.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class PhotoShowWidget extends StatefulWidget {
  const PhotoShowWidget({
    super.key,
    required this.oneDayPhotos,
    required this.currentPhotoIndexPage,
    required this.likeAnimation,
    required this.likeAnimationController,
    this.isFeedPage = false,
    this.setStateParent,
  });

  final OneDayPhotos oneDayPhotos;
  final int currentPhotoIndexPage;
  final Animation<double> likeAnimation;
  final AnimationController likeAnimationController;
  final bool isFeedPage;
  final Function()? setStateParent;

  @override
  State<PhotoShowWidget> createState() => _PhotoShowWidgetState();
}

class _PhotoShowWidgetState extends State<PhotoShowWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2),
          child: Center(
            child: PinchZoomReleaseUnzoomWidget(
              overlayColor: Colors.black,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => FullScreenPhotoView(
                        photo: widget.oneDayPhotos.photos[widget.currentPhotoIndexPage],
                        oneDayPhotos: widget.oneDayPhotos,
                      ),
                    ),
                  );
                },
                child: Hero(
                  tag: widget.oneDayPhotos.photos[widget.currentPhotoIndexPage].path,
                  child: SizedBox(
                    width: 1.sw,
                    child: ClipRRect(
                      borderRadius: AppColors.borderRadiusAll,
                      child: Image.file(
                        File(widget.oneDayPhotos.photos[widget.currentPhotoIndexPage].path),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        Center(
          child: ScaleTransition(
            scale: widget.likeAnimation,
            child: const Icon(
              Icons.favorite,
              color: AppColors.red,
              size: 120,
            ),
          ),
        ),
        Row(
          children: [
            if (isFavoriFeatureActive)
              GestureDetector(
                onTap: () {
                  context.read<PhotoProvider>().photoChanceFavoriteStates(
                        context: context,
                        oneDayPhotos: widget.oneDayPhotos,
                        selectedPhotoIndex: widget.currentPhotoIndexPage,
                        likeAnimationController: widget.likeAnimationController,
                      );
                  if (widget.setStateParent != null) {
                    widget.setStateParent!();
                  }
                },
                child: Container(
                  color: AppColors.transparent,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  child: Icon(
                    Icons.favorite,
                    size: 28,
                    color: widget.oneDayPhotos.photos[widget.currentPhotoIndexPage].isFavorite ? AppColors.red : AppColors.white,
                    shadows: [
                      BoxShadow(
                        color: AppColors.deepBlack.withValues(alpha: 0.2),
                        blurRadius: 2,
                        spreadRadius: 5,
                        offset: const Offset(2, 2),
                      ),
                    ],
                  ),
                ),
              ),
            if (widget.isFeedPage) ...[
              const Spacer(),
              GestureDetector(
                onTap: () {
                  Helper().showPhotoOptionsBottomSheet(
                    context: context,
                    oneDayPhotos: widget.oneDayPhotos,
                    currentPhotoIndex: widget.currentPhotoIndexPage,
                  );
                },
                child: Container(
                  color: AppColors.transparent,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  child: Icon(
                    Icons.more_vert,
                    size: 30,
                    color: AppColors.white,
                    shadows: [
                      BoxShadow(
                        color: AppColors.deepBlack.withValues(alpha: 0.2),
                        blurRadius: 2,
                        spreadRadius: 5,
                        offset: const Offset(2, 2),
                      ),
                    ],
                  ),
                ),
              ),
            ]
          ],
        ),
        if (widget.oneDayPhotos.photos[widget.currentPhotoIndexPage].location != null && isLocationFeatureActive)
          Positioned(
            bottom: 10,
            right: 5,
            child: GestureDetector(
              onTap: () async {
                await _forwardMap(widget.oneDayPhotos.photos[widget.currentPhotoIndexPage].location!);
              },
              child: Container(
                padding: const EdgeInsets.all(7),
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  borderRadius: AppColors.highBorderRadiusAll,
                  color: AppColors.background.withValues(alpha: 0.7),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 20,
                    ),
                    const SizedBox(width: 5),
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 0.87.sw),
                      child: AutoSizeText(
                        widget.oneDayPhotos.photos[widget.currentPhotoIndexPage].location!,
                        minFontSize: 10,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        if (!isDescriptionFeatureActive)
          Positioned(
            bottom: isLocationFeatureActive ? 50 : 10,
            right: 20,
            child: Text(
              DateFormat('dd/MM/yyyy').format(widget.oneDayPhotos.date),
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _forwardMap(String location) async {
    await Helper().getDialog(
      message: LocaleKeys.PhotoEvents_LocationService_ForwardMap.tr(),
      onAccept: () async {
        final Uri googleMapsUrl = Uri.parse('https://www.google.com/maps/search/?api=1&query=$location');

        if (await canLaunchUrl(Uri.parse(googleMapsUrl.toString()))) {
          await launchUrl(Uri.parse(googleMapsUrl.toString()));
        } else {
          Helper().getMessage(
            message: LocaleKeys.PhotoEvents_LocationService_FailForwarding.tr(),
            status: StatusEnum.WARNING,
          );
        }
      },
      acceptButtonText: LocaleKeys.PhotoEvents_LocationService_ButtonText.tr(),
    );
  }
}

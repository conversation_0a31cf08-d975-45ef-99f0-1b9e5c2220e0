// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'refrance_parts.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ReferancePartsAdapter extends TypeAdapter<ReferanceParts> {
  @override
  final int typeId = 2;

  @override
  ReferanceParts read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ReferanceParts(
      eye1: fields[0] as Offset,
      eye2: fields[1] as Offset,
      mouth: fields[2] as Offset,
    );
  }

  @override
  void write(BinaryWriter writer, ReferanceParts obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.eye1)
      ..writeByte(1)
      ..write(obj.eye2)
      ..writeByte(2)
      ..write(obj.mouth);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReferancePartsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

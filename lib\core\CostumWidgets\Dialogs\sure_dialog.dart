import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';

class CustomDialogWidget extends StatefulWidget {
  final String? title;
  final String contentText;
  final bool withTimer;
  final Function? onAccept;
  final String? acceptButtonText;

  const CustomDialogWidget({
    super.key,
    this.title,
    required this.contentText,
    this.withTimer = false,
    required this.onAccept,
    this.acceptButtonText,
  });

  @override
  State<CustomDialogWidget> createState() => _CustomDialogWidgetState();
}

class _CustomDialogWidgetState extends State<CustomDialogWidget> {
  // Texts
  final String _acceptButton = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_ActionButtons_Yes.tr();
  final String _title = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxExportPhotos_SurePopUp_Title.tr();
  final String _declineButton = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxDeleteAllPhotos_SurePopUp_ActionButtons_No.tr();

  int count = 3;
  late bool withTimer = widget.withTimer;
  // default accept button text
  late String acceptButtonText = widget.acceptButtonText ?? _acceptButton;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() async {
    while (count > 0) {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        setState(() {
          count--;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title ?? _title),
      content: Text(widget.contentText),
      shape: RoundedRectangleBorder(
        side: const BorderSide(
          color: AppColors.grey,
        ),
        borderRadius: AppColors.borderRadiusAll,
      ),
      actions: [
        // Okey
        if (widget.onAccept == null) ...[
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: AppColors.transparent,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text(LocaleKeys.Dialog_Okay).tr(),
          ),
        ] else ...[
          // Cancel
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: AppColors.transparent,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(_declineButton),
          ),

          withTimer && count > 0
              ?
              // Timer
              Text(
                  '$_acceptButton $count',
                  style: const TextStyle(
                    color: AppColors.dirtyWhite,
                  ),
                )
              :
              // Accept
              ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);

                    if (widget.onAccept != null) {
                      widget.onAccept!();
                    }
                  },
                  child: Text(
                    acceptButtonText,
                    style: const TextStyle(
                      color: AppColors.white,
                    ),
                  ).tr(),
                )
        ]
      ],
    );
  }
}

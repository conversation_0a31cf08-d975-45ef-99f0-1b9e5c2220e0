// ignore_for_file: constant_identifier_names

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:facelog/product/models/supporter_model.dart';

const String SUPPORTERS_COLLECTION_REF = 'supporters';

class DatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  late final CollectionReference _supportersRef;

  DatabaseService() {
    _supportersRef = _firestore.collection(SUPPORTERS_COLLECTION_REF).withConverter<Supporter>(
          fromFirestore: (snapshot, _) => Supporter.fromJson(snapshot.data()!),
          toFirestore: (supporter, _) => supporter.toJson(),
        );
  }

  Future<QuerySnapshot> getSupporters() {
    return _supportersRef.get();
  }

  void getSupporter(Supporter supporter) {
    _supportersRef.add(supporter);
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';

final class TheMarketNotAvailable extends StatelessWidget {
  TheMarketNotAvailable({super.key});

  // Texts
  final String _marketNotAvailable = LocaleKeys.PaymentPage_TheMarketNotAvailable.tr();

  @override
  Widget build(BuildContext context) {
    LogService().logScreen("TheMarketNotAvailable");

    return Center(
      child: Text(
        _marketNotAvailable,
        textAlign: TextAlign.center,
      ),
    );
  }
}

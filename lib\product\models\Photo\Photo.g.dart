// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: file_names

part of 'Photo.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PhotoAdapter extends TypeAdapter<Photo> {
  @override
  final int typeId = 1;

  @override
  Photo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Photo(
      date: fields[0] as DateTime?,
      path: fields[1] as String,
      isFavorite: fields[2] as bool,
      location: fields[3] as String?,
      isImported: fields[4] as bool?,
      face: fields[5] as Face?,
      isCropped: fields[6] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, Photo obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.path)
      ..writeByte(2)
      ..write(obj.isFavorite)
      ..writeByte(3)
      ..write(obj.location)
      ..writeByte(4)
      ..write(obj.isImported)
      ..writeByte(5)
      ..write(obj.face)
      ..writeByte(6)
      ..write(obj.isCropped);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) => identical(this, other) || other is PhotoAdapter && runtimeType == other.runtimeType && typeId == other.typeId;
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Payment/Market/market_view.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:io';
import 'package:facelog/Pages/Manage/Payment/components/plan_chooser.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:shared_preferences/shared_preferences.dart';

mixin MarketViewMixin on State<MarketView> {
  int activeSubscription = 0;
  final String _kAnnualSubscriptionId = 'facelog_annual_subscription';
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  final List<String> _kProductIds = <String>[
    'facelog_annual_subscription',
    'facelog_monthly_subscription',
  ];
  List<ProductDetails> _products = <ProductDetails>[];
  bool _loading = true;
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  @override
  void initState() {
    super.initState();

    final Stream<List<PurchaseDetails>> purchaseUpdated = _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen((List<PurchaseDetails> purchaseDetailsList) {
      _listenToPurchaseUpdated(purchaseDetailsList);
    });

    initStoreInfo();

    LogService().logScreen("MarketView");
  }

  @override
  void dispose() {
    if (Platform.isIOS) {
      final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition = _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      iosPlatformAddition.setDelegate(null);
    }
    _subscription.cancel();
    super.dispose();
  }

  Future<void> initStoreInfo() async {
    List<ProductDetails> products = <ProductDetails>[];
    bool loading = false;

    final ProductDetailsResponse productDetailResponse = await _inAppPurchase.queryProductDetails(_kProductIds.toSet());
    if (productDetailResponse.error == null && productDetailResponse.productDetails.isNotEmpty) {
      products = productDetailResponse.productDetails;
    }

    setState(() {
      _products = products;
      _loading = loading;
    });
  }

  Card buildProductList() {
    if (_loading) {
      return Card(
        child: ListTile(
          leading: const CircularProgressIndicator(),
          title: const Text(LocaleKeys.PaymentPage_FetchingProducts).tr(),
        ),
      );
    }
    final List<Widget> productWidgets = <Widget>[];

    for (ProductDetails productDetails in _products) {
      productWidgets.add(
        PlanChooser(
          isPopular: productDetails.id == _kAnnualSubscriptionId,
          price:   productDetails.rawPrice,
          currency: productDetails.currencyCode,
          isAdvantagePlan: productDetails.id == _kAnnualSubscriptionId,
          timePeriod: productDetails.id == _kAnnualSubscriptionId ? LocaleKeys.PaymentPage_Package_Annual.tr() : LocaleKeys.PaymentPage_Package_Monthly.tr(),
          isActive: activeSubscription == _products.indexOf(productDetails),
          onTap: () {
            setState(() {
              activeSubscription = _products.indexOf(productDetails);
            });
          },
        ),
      );
      productWidgets.add(const SizedBox(height: 10));
    }

    return Card(
      child: Column(
        children: productWidgets,
      ),
    );
  }

  Future<void> buySelectedSubscription() async {
    final ProductDetails selectedProduct = _products[activeSubscription];
    try {
      final PurchaseParam purchaseParam = GooglePlayPurchaseParam(productDetails: selectedProduct);
      _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
    } catch (error) {
      // Satın alma sırasında bir hata oluştuğunda yapılacaklar
      debugPrint('Satın alma hatası: $error');
      LogService().buySubscription(_products[activeSubscription], "catchError");
    }
  }

  void _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        debugPrint('Satın alma bekleniyor');
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          debugPrint('Satın alma hatası');
          LogService().buySubscription(_products[activeSubscription], "statusError");
        } else if (purchaseDetails.status == PurchaseStatus.purchased || purchaseDetails.status == PurchaseStatus.restored) {
          setState(() {
            isPremium = true;
          });
          _nextPurchaseDate();
          debugPrint('Abonelik devam ediyor');
          LogService().buySubscription(_products[activeSubscription], "success");
        } else if (purchaseDetails.status == PurchaseStatus.canceled) {
          debugPrint('Satın alma iptal edldi');
          LogService().buySubscription(_products[activeSubscription], "cancel");
        }

        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

  Future<void> _nextPurchaseDate() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String purchaseDate = (DateTime.now().add(const Duration(days: 31))).toIso8601String();
    await prefs.setString('purchaseDate', purchaseDate);
    await prefs.setBool('isPremium', true); // Satın alım yaptığı için premium true olarak kaydedilir yapılır.
    debugPrint('Satın alma tarihi kaydedildi: $purchaseDate');
  }
}

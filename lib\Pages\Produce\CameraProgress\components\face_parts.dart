import 'dart:math';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:showcaseview/showcaseview.dart';

int refPartSize = 55;

// Ekrandaki sınırlar
double rightLimit = 1.sw - (0.1.sw + refPartSize);
double leftLimit = 0.1.sw;
double bottomLimit = 1.sh - (0.05.sh + refPartSize + 0.24.sh);
double topLimit = 0.05.sh;

Offset updatePosition(
  Offset currentPosition,
  DragUpdateDetails details,
  bool isLandscape,
  Offset landscapeOffset,
  Offset portraitOffset,
) {
  Offset newPosition = currentPosition + details.delta;
  newPosition = details.globalPosition + (isLandscape ? landscapeOffset : portraitOffset);

  if (newPosition.dx > rightLimit) {
    newPosition = Offset(rightLimit, newPosition.dy);
  }
  if (newPosition.dx < leftLimit) {
    newPosition = Offset(leftLimit, newPosition.dy);
  }
  if (newPosition.dy > bottomLimit) {
    newPosition = Offset(newPosition.dx, bottomLimit);
  }
  if (newPosition.dy < topLimit) {
    newPosition = Offset(newPosition.dx, topLimit);
  }

  return newPosition;
}

Positioned draggableWidget({
  required Offset position,
  required Function(Offset) updatePositionFunc,
  required String assetPath,
  Function()? setStateFunc,
  bool isLandscape = true, // Default landscape value is true in initapp.dart
  required Offset landscapeOffset,
  required Offset portraitOffset,
  GlobalKey? showcaseKey,
  String? showcaseTitle,
  String? showcaseDesc,
}) {
  Widget buildWidget = GestureDetector(
    dragStartBehavior: DragStartBehavior.start,
    onPanUpdate: (details) {
      if (setStateFunc != null) {
        Offset newPosition = updatePosition(
          position,
          details,
          isLandscape,
          landscapeOffset,
          portraitOffset,
        );
        updatePositionFunc(newPosition);

        setStateFunc();
      }
    },
    child: Transform.rotate(
      angle: isLandscape ? pi / 2 : 0,
      child: SvgPicture.asset(
        height: 40,
        assetPath,
        // ignore: deprecated_member_use
        color: Colors.black,
      ),
    ),
  );

  return Positioned(
    top: position.dy,
    left: position.dx,
    child: showcaseKey != null
        ? Showcase(
            key: showcaseKey,
            title: showcaseTitle,
            description: showcaseDesc,
            overlayOpacity: 0.7,
            targetPadding: AppColors.showcasePadding,
            child: buildWidget,
          )
        : buildWidget,
  );
}

Positioned eyes1({
  Function()? setStateFunc,
  GlobalKey? showcaseKey,
  String? showcaseTitle,
  String? showcaseDesc,
}) {
  return draggableWidget(
    position: refParts.eye1,
    updatePositionFunc: (newPosition) {
      refParts.eye1 = newPosition;
    },
    assetPath: AssetsPath.eye,
    setStateFunc: setStateFunc,
    isLandscape: isLandscape,
    landscapeOffset: const Offset(50, -150),
    portraitOffset: const Offset(-75, -190),
    showcaseKey: showcaseKey,
    showcaseTitle: showcaseTitle,
    showcaseDesc: showcaseDesc,
  );
}

Positioned eyes2({
  Function()? setStateFunc,
}) {
  return draggableWidget(
    position: refParts.eye2,
    updatePositionFunc: (newPosition) {
      refParts.eye2 = newPosition;
    },
    assetPath: AssetsPath.eye,
    setStateFunc: setStateFunc,
    isLandscape: isLandscape,
    landscapeOffset: const Offset(50, -150),
    portraitOffset: const Offset(-75, -190),
  );
}

Positioned mouth({
  Function()? setStateFunc,
}) {
  return draggableWidget(
    position: refParts.mouth,
    updatePositionFunc: (newPosition) {
      refParts.mouth = newPosition;
    },
    assetPath: AssetsPath.mouth,
    setStateFunc: setStateFunc,
    isLandscape: isLandscape,
    landscapeOffset: const Offset(25, -150),
    portraitOffset: const Offset(-75, -190),
  );
}

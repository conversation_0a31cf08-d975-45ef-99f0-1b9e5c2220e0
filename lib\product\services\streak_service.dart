// ignore_for_file: file_names

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/models/streak_model.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StreakService {
  static const String _boxName = 'StreakBox';

  Future<Box<StreakModel>> get _box async {
    return await Hive.openBox<StreakModel>(_boxName);
  }

  // Functions ------------------------------
  // Get All
  Future<List<StreakModel>> getAllStreaksInHive() async {
    final box = await _box;
    return box.values.toList();
  }

  // Add
  Future<void> addStreak() async {
    try {
      // Normalize date to remove time component
      final DateTime now = DateTime.now();
      final DateTime normalizedDate = DateTime(now.year, now.month, now.day);

      // Initialize streak list if empty
      if (streakList.isEmpty) {
        await getLastDays();
      }

      // Check if today's streak already exists
      bool todayExists = streakList.any((element) => element.date.isAtSameMomentAs(normalizedDate));

      if (todayExists) {
        // Update existing streak for today
        final todayIndex = streakList.indexWhere((element) => element.date.isAtSameMomentAs(normalizedDate));

        if (todayIndex != -1) {
          streakList[todayIndex] = StreakModel(
            date: normalizedDate,
            status: StreakStatus.streak,
          );

          final box = await _box;
          await box.putAt(todayIndex, streakList[todayIndex]);
        }
      } else {
        // Add new streak for today
        final StreakModel model = StreakModel(
          date: normalizedDate,
          status: StreakStatus.streak,
        );

        streakList.add(model);

        final box = await _box;
        await box.add(model);
      }

      LogService().addStreak();
    } catch (e) {
      LogService().logError("Error in addStreak: $e");
    }
  }

  // Sort by date and update Hive
  Future<void> updateList() async {
    try {
      if (streakList.isEmpty) {
        return; // Nothing to update if the list is empty
      }

      // Sort the list by date
      streakList.sort((a, b) => a.date.compareTo(b.date));

      final box = await _box;
      await box.clear();

      // Add all streaks back to Hive in sorted order
      for (var streak in streakList) {
        await box.add(streak);
      }
    } catch (e) {
      LogService().logError("Error in updateList: $e");
    }
  }

  Future<void> getLastDays() async {
    streakList = await getAllStreaksInHive();

    final now = DateTime.now();
    final normalizedNow = DateTime(now.year, now.month, now.day);

    // İlk 5 günü boş ekle (eğer hiç streak yoksa)
    if (streakList.isEmpty) {
      final firstDate = normalizedNow.subtract(const Duration(days: 4));

      for (DateTime date = firstDate; date.isBefore(normalizedNow.add(const Duration(days: 1))); date = date.add(const Duration(days: 1))) {
        streakList.add(StreakModel(
          date: DateTime(date.year, date.month, date.day),
          status: StreakStatus.empty,
        ));
      }
    }
    // Eğer birkaç gün aradan sonra girmişse aradaki boşlukları doldur
    else {
      // Boş olmayan son streak tarihini bul
      StreakModel? lastNonEmptyStreak;
      try {
        lastNonEmptyStreak = streakList.lastWhere((element) => element.status != StreakStatus.empty);
      } catch (e) {
        // Hiç streak yoksa en son tarihi al
        if (streakList.isNotEmpty) {
          lastNonEmptyStreak = streakList.last;
        }
      }

      if (lastNonEmptyStreak != null) {
        final normalizedLastDate = DateTime(
          lastNonEmptyStreak.date.year,
          lastNonEmptyStreak.date.month,
          lastNonEmptyStreak.date.day,
        );

        // Sadece gelecek günleri ekle
        if (normalizedLastDate.isBefore(normalizedNow)) {
          // SharedPreferences'den freeze count'u al
          SharedPreferences prefs = await SharedPreferences.getInstance();
          int currentFreezeCount = prefs.getInt('freezeCount') ?? 0;
          freezeCount = currentFreezeCount;

          int usedFreezeCount = 0;

          // Bir sonraki günden başla
          DateTime currentDate = normalizedLastDate.add(const Duration(days: 1));

          while (currentDate.isBefore(normalizedNow.add(const Duration(days: 1)))) {
            // Bu tarihin zaten var olup olmadığını kontrol et
            bool dateExists = streakList.any((element) => element.date.isAtSameMomentAs(currentDate));

            if (!dateExists) {
              final useFreeze = freezeCount > 0 && currentDate.isBefore(normalizedNow);

              streakList.add(StreakModel(
                date: currentDate,
                status: useFreeze
                    ? StreakStatus.freeze
                    : currentDate.isAtSameMomentAs(normalizedNow)
                        ? StreakStatus.empty
                        : StreakStatus.broken,
              ));

              if (useFreeze) {
                freezeCount--;
                usedFreezeCount++;
              }
            }

            currentDate = currentDate.add(const Duration(days: 1));
          }

          if (usedFreezeCount > 0) {
            final String usedFreeze = LocaleKeys.Home_Streak_UsedFreeze.tr();

            Helper().getMessage(
              status: StatusEnum.INFO,
              message: "$usedFreezeCount $usedFreeze",
            );

            LogService().useFreezeRight(usedFreezeCount);

            // Güncellenmiş freeze count'u kaydet
            await prefs.setInt('freezeCount', freezeCount);
          }
        }
      }
    }

    await updateList();
  }

  List<int> getYears() {
    final List<int> years = streakList.map((e) => e.date.year).toSet().toList();
    years.sort((a, b) => b.compareTo(a));

    return years;
  }

  // Delete all
  Future<void> deleteAll() async {
    try {
      streakList.clear();

      final box = await _box;
      await box.clear();
    } catch (e) {
      LogService().logError("Error in deleteAll: $e");
    }
  }
}

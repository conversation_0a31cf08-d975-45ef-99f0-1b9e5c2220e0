import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/core/video_variables.dart';
import 'package:facelog/product/config/check_subscription_status.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/FaceAdapters/facecontour_adapter.dart';
import 'package:facelog/product/models/FaceAdapters/facecontourtype_adapter.dart';
import 'package:facelog/product/models/FaceAdapters/facelandmark_adapter.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/models/ReferanceParts/refrance_parts.dart';
import 'package:facelog/product/models/FaceAdapters/face_adapter.dart';
import 'package:facelog/product/models/FaceAdapters/facelandmarktype_adapter.dart';
import 'package:facelog/product/models/Video/video_model.dart';
import 'package:facelog/product/models/offset_adapter.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/models/point_adapter.dart';
import 'package:facelog/product/models/rect_adapter.dart';
import 'package:facelog/product/models/streak_model.dart';
import 'package:facelog/product/services/firebase/core/firebase_options.dart';
import 'package:facelog/product/services/firebase/messaging/firebase_messagging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> initApp() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  // Firebase messaging and Firebase Analytics
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Lock Orientation to Portrait
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Hive
  await Hive.initFlutter();
  Hive.registerAdapter(PhotoAdapter());
  Hive.registerAdapter(ReferancePartsAdapter());
  Hive.registerAdapter(OffsetAdapter());
  Hive.registerAdapter(FaceAdapter());
  Hive.registerAdapter(OneDayPhotosAdapter());
  Hive.registerAdapter(RectAdapter());
  Hive.registerAdapter(FaceLandmarkTypeAdapter());
  Hive.registerAdapter(FaceLandmarkAdapter());
  Hive.registerAdapter(PointAdapter());
  Hive.registerAdapter(FaceContourTypeAdapter());
  Hive.registerAdapter(FaceContourAdapter());
  Hive.registerAdapter(VideoModelAdapter());
  Hive.registerAdapter(StreakModelAdapter());
  Hive.registerAdapter(StreakStatusAdapter());

  // SharedPreferences
  SharedPreferences prefs = await SharedPreferences.getInstance();

  // General Settings
  // is First
  isFirstLogin = prefs.getBool('isFirstLogin') ?? true;

  isFreeTrailUsed = prefs.getBool('isFreeTrailUsed') ?? false;
  isLandscape = prefs.getBool('isLandscape') ?? true;

  // Streak
  freezeCount = prefs.getInt('freezeCount') ?? 0;

  // Advaced Settings
  isLocationFeatureActive = prefs.getBool("isLocationFeatureActive") ?? false;
  isDescriptionFeatureActive = prefs.getBool("isDescriptionFeatureActive") ?? false;
  isFavoriFeatureActive = prefs.getBool("isFavoriFeatureActive") ?? false;
  isFeedPageFeatureActive = prefs.getBool("isFeedPageFeatureActive") ?? false;

  // Notification
  notificationTime = TimeOfDay(
    hour: prefs.getInt('selectedHour') ?? 12,
    minute: prefs.getInt('selectedMinute') ?? 0,
  );
  isNotificationsEnabled = prefs.getBool('notificationsEnabled') ?? false;

  // Rating Dialog
  isRated = prefs.getBool('isRated') ?? false;
  debugPrint('isRated value is $isRated');

  //Connectivity Check
  List<ConnectivityResult> connectivityResults = await (Connectivity().checkConnectivity());

  isConnected = connectivityResults.contains(ConnectivityResult.none) ? true : false;
  Connectivity().onConnectivityChanged.listen(
    (List<ConnectivityResult> result) async {
      isConnected = !result.contains(ConnectivityResult.none);
    },
  );

  // Camera Settings
  isAutoTake = prefs.getBool('isAutoTake') ?? true;
  isRefPartsActive = prefs.getBool('isRefPartsActive') ?? false;
  isGridActive = prefs.getBool('isGridActive') ?? true;
  isGhostImageActive = prefs.getBool('isGhostImageActive') ?? false;
  isFrontCamera = prefs.getBool('isFrontCamera') ?? true;
  lastPhotoNumber = prefs.getInt('lastPhotoNumber') ?? 0;
  isStatisticsActive = prefs.getBool('isStatisticsActive') ?? true;

  // Theme color
  // ignore: deprecated_member_use
  AppColors.main = Color(prefs.getInt('mainColor') ?? AppColors.main.value);
  // ignore: deprecated_member_use
  AppColors.lightMain = Color(prefs.getInt('lightMainColor') ?? AppColors.lightMain.value);
  // ignore: deprecated_member_use
  AppColors.deepMain = Color(prefs.getInt('deepMainColor') ?? AppColors.deepMain.value);

  // TODO: kafa hizalama göze göre olduğunda bunu kontrol et.
  // referanceFace = await FaceDetailService().getReferanceFace().then((value) {
  //   if (value.isNotEmpty) {
  //     return value.first;
  //   } else {
  //     return null;
  //   }
  // });

  // ----------------------

  // Easy Localization paketinini sürekli print atmasını engellemek için
  EasyLocalization.logger.enableBuildModes = [];

  // Firebase Messaging Initialization.
  if (isNotificationsEnabled) {
    firebaseMessageListen();
  }

  if (Platform.isAndroid) {
    // InAppPurchase
    await checkSubscriptionStatus();
  } else {
    isPremium = true;
  }

  if (kDebugMode) {
    qualityList.add("debug");
  }

  // Custom Error
  ErrorWidget.builder = (FlutterErrorDetails details) {
    return Material(
      child: Container(
        color: AppColors.background,
        padding: const EdgeInsets.all(15),
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: AppColors.main,
              borderRadius: AppColors.borderRadiusAll,
            ),
            child: Wrap(
              children: [
                Column(
                  children: [
                    const Text(
                      LocaleKeys.General_AppError,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      details.exception.toString(),
                      style: const TextStyle(
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  };
}

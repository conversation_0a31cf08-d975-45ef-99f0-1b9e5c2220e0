import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:mailto/mailto.dart';
import 'services_tab.dart';

class ContactUsPage extends StatefulWidget {
  const ContactUsPage({super.key});

  @override
  State<ContactUsPage> createState() => _ContactUsPageState();
}

class _ContactUsPageState extends State<ContactUsPage> {
  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  late String? model;
  late String? device;
  late String? _version;

  @override
  void initState() {
    super.initState();
    _loadVersionInfo();

    LogService().logScreen("ContactUsPage");
  }

  Future<void> _loadVersionInfo() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;
      setState(() {
        _version = packageInfo.version;
        model = androidDeviceInfo.model;
        device = androidDeviceInfo.device;
      });
    } else if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfoPlugin.iosInfo;
      setState(() {
        _version = packageInfo.version;
        model = iosDeviceInfo.model;
        device = iosDeviceInfo.utsname.machine;
      });
    }
  }

  void _launchMailto({required String subject, String? body}) async {
    String mailBody = '';
    if (_version != null) {
      mailBody += '$_version \n';
    }
    if (model != null) {
      mailBody += '$model \n';
    }
    if (device != null) {
      mailBody += '$device \n';
    }
    final mailtoLink = Mailto(
      to: ['<EMAIL>'],
      subject: subject,
      body: mailBody,
    );
    debugPrint(mailBody);
    // ignore: deprecated_member_use
    await launch('$mailtoLink');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: const Text(LocaleKeys.SettingsPage_SettingsTiles_Contact_ContactUsHeader).tr(),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        child: Column(
          children: [
            NewTabForServices(
              title: LocaleKeys.SettingsPage_SettingsTiles_Contact_AskAQuestion.tr(),
              iconData: Icons.chat_bubble,
              onTap: () {
                LogService().logScreen("AskQuestion");
                _launchMailto(subject: LocaleKeys.SettingsPage_SettingsTiles_Contact_AskAQuestion.tr());
              },
            ),
            const SizedBox(height: 15),
            NewTabForServices(
              title: LocaleKeys.SettingsPage_SettingsTiles_Contact_ReportAProblem.tr(),
              iconData: Icons.bug_report,
              onTap: () {
                LogService().logScreen("ReportProblem");
                _launchMailto(
                  subject: LocaleKeys.SettingsPage_SettingsTiles_Contact_ReportAProblem.tr(),
                );
              },
            ),
            const SizedBox(height: 15),
            NewTabForServices(
              title: LocaleKeys.SettingsPage_SettingsTiles_Contact_IdeasAndFeedback.tr(),
              iconData: Icons.lightbulb,
              onTap: () {
                LogService().logScreen("IdeasAndFeedback");
                _launchMailto(
                  subject: LocaleKeys.SettingsPage_SettingsTiles_Contact_IdeasAndFeedback.tr(),
                  body: '-',
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

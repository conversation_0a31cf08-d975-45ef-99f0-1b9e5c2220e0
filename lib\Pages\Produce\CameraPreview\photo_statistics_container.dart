import 'dart:math';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

double? totalAccuracy;

class PhotoStatisticsContainer extends StatelessWidget {
  final Face detectedFace;

  PhotoStatisticsContainer({
    super.key,
    required this.detectedFace,
  });

  // Texts
  final String _statisticsTitle = LocaleKeys.CameraPages_Camera_FaceDetection_Statistics.tr();
  final String _rotation = LocaleKeys.CameraPages_Camera_FaceDetection_Rotation.tr();
  final String _side = LocaleKeys.CameraPages_Camera_FaceDetection_Side.tr();
  final String _rightLeft = LocaleKeys.CameraPages_Camera_FaceDetection_RightLeft.tr();
  final String _upDown = LocaleKeys.CameraPages_Camera_FaceDetection_UpDown.tr();
  final String _size = LocaleKeys.CameraPages_Camera_FaceDetection_Size.tr();
  final String _distane = LocaleKeys.CameraPages_Camera_FaceDetection_Distance.tr();

  late final double headRotationDifferanceSide = detectedFace.headEulerAngleZ!;
  late final double headRotationDifferanceRightLeft = detectedFace.headEulerAngleY!;
  late final double headRotationDifferanceTopBottom = detectedFace.headEulerAngleX!;
  late final double boxSizeDifferent = Offset(detectedFace.boundingBox.size.height, detectedFace.boundingBox.size.width).distance;
  late final double boxDistanceToCenter = Helper().calculateTwoPointDistance(detectedFace.boundingBox.center, photoCenter!);
  // TODO: face align gözler ile yapıldığında burada hesaplanması zorunlu olmayacak. (referance face için)

  final double acceptableDifferenceSide = 3;
  final double acceptableDifferenceRightLeft = 3;
  final double acceptableDifferenceTopBottom = 2;
  final double acceptableDifferenceSize = 30;
  final double acceptableDifferenceDistance = 30;

  final double fitValueSide = 0;
  final double fitValueRightLeft = 0;
  final double fitValueTopBottom = -12;
  final double fitValueSize = 950;
  final double fitValueDistance = 0;

  @override
  Widget build(BuildContext context) {
    final sideRatio = accuracyPercentage(
      value: headRotationDifferanceSide,
      acceptableDifference: acceptableDifferenceSide,
      fitValue: fitValueSide,
    );
    final rightLeftRatio = accuracyPercentage(
      value: headRotationDifferanceRightLeft,
      acceptableDifference: acceptableDifferenceRightLeft,
      fitValue: fitValueRightLeft,
    );
    final topBottomRatio = accuracyPercentage(
      value: headRotationDifferanceTopBottom,
      acceptableDifference: acceptableDifferenceTopBottom,
      fitValue: fitValueTopBottom,
    );
    final sizeRatio = accuracyPercentage(
      value: boxSizeDifferent,
      acceptableDifference: acceptableDifferenceSize,
      fitValue: fitValueSize,
    );
    final distanceRatio = accuracyPercentage(
      value: boxDistanceToCenter,
      acceptableDifference: acceptableDifferenceDistance,
      fitValue: fitValueDistance,
    );

    totalAccuracy = (sideRatio + rightLeftRatio + topBottomRatio + sizeRatio + distanceRatio) / 5;

    return Positioned(
      top: isLandscape ? 30 : 5,
      left: isLandscape ? -20 : 5,
      // TODO: normalde animasyonlu geliyor ama state güncellemeyle alakalı bir sorun sonra yapalım abi
      child: Transform.rotate(
        angle: isLandscape ? pi / 2 : 0,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 225,
          transform: Matrix4.translationValues(isStatisticsActive ? 0 : -250, 0, 0),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: AppColors.highBorderRadiusAll,
            color: AppColors.background.withValues(alpha: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              AutoSizeText(
                _statisticsTitle,
                maxLines: 1,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 5),
              buildStatisticRow(
                label: "$_rotation ($_side)",
                value: headRotationDifferanceSide,
                ratio: sideRatio,
                fitValue: fitValueSide,
                isInvert: false,
              ),
              buildStatisticRow(
                label: "$_rotation ($_rightLeft)",
                value: headRotationDifferanceRightLeft,
                ratio: rightLeftRatio,
                fitValue: fitValueRightLeft,
                isInvert: true,
              ),
              buildStatisticRow(
                label: "$_rotation ($_upDown)",
                value: headRotationDifferanceTopBottom,
                ratio: topBottomRatio,
                fitValue: fitValueTopBottom,
                isInvert: true,
              ),
              buildStatisticRow(
                label: _size,
                value: boxSizeDifferent,
                ratio: sizeRatio,
                fitValue: fitValueSize,
                isInvert: true,
              ),
              buildStatisticRow(
                label: _distane,
                value: boxDistanceToCenter,
                ratio: distanceRatio,
                fitValue: fitValueDistance,
                isInvert: false,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // verilen değerlere göre yüzdelik değer döndürüyor.
  double accuracyPercentage({
    required double value,
    required double acceptableDifference,
    required double fitValue,
  }) {
    double error = (fitValue - value).abs() / acceptableDifference;
    double percentage = 100 - (error * 10);

    if (percentage > 100) {
      percentage = 100;
    } else if (percentage < 0) {
      percentage = 0;
    }

    return percentage;
  }

  Widget buildStatisticRow({
    required String label,
    required double value,
    required double ratio,
    required double fitValue,
    required bool isInvert,
  }) {
    Color textColor = Helper().getColorForPercentage(ratio);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: AutoSizeText(
              label,
              maxLines: 1,
              minFontSize: 10,
            ),
          ),
          Text(
            '${((isInvert ? -1 : 1) * (fitValue - value)).toStringAsFixed(1)} (%${ratio.toStringAsFixed(0)})',
            style: TextStyle(color: textColor),
          ),
        ],
      ),
    );
  }
}

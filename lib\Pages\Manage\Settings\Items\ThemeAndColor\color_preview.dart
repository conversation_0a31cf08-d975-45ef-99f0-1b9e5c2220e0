import 'package:flutter/material.dart';

class ColorPreview extends StatelessWidget {
  final Color color;
  final String label;

  const ColorPreview({
    super.key,
    required this.color,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color,
            border: Border.all(color: Colors.black),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        const SizedBox(height: 8),
        Text(label),
      ],
    );
  }
}

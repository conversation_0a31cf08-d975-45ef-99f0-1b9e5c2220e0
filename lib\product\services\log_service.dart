import 'dart:io';

import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/models/Video/video_model.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:provider/provider.dart';

class LogService {
  // Singleton
  static final LogService _instance = LogService._internal();
  // Factory constructor
  factory LogService() {
    return _instance;
  }
  // Private constructor
  LogService._internal();

  final FirebaseAnalytics? _logger = kDebugMode ? FirebaseAnalytics.instance : null;

  void logOpenApp() async {
    await _logger?.logAppOpen();
  }

  void logScreen(String screenName) {
    _logger?.logScreenView(screenName: screenName);
  }

  void setUserId() async {
    await _logger?.setUserId();
  }

  void savePhoto({
    required BuildContext context,
  }) async {
    await _logger?.setUserProperty(name: "lastPhotoNumber", value: lastPhotoNumber.toString());

    final PhotoProvider photoProvider = context.read<PhotoProvider>();

    // kaçıncı gün
    final int photoDay = photoProvider.allPhotosList.length;
    // kaçıncı fotoğraf
    final int photoThByAll = photoProvider.allPhotosList.map((e) => e.photos).expand((element) => element).toList().length;
    // günün kaçıncı fotoğrafı
    final int photoThByDay = photoProvider.allPhotosList.last.photos.length;

    await _logger?.logEvent(
      name: 'save_photo',
      parameters: {
        // TODO: 1920x1080 veya 1080x1920 den farklı fotoğraf çekebiliyorlar mı görsek güzel olur.
        "photoDay": photoDay,
        "photoThByAll": photoThByAll,
        "photoThByDay": photoThByDay,
        "isIncludeFace": currentFace != null ? "true" : "false",
        "isAutoTake": isAutoTake.toString(),
        "isRefPartsActive": isRefPartsActive.toString(),
        "isGridActive": isGridActive.toString(),
        "isGhostImageActive": isGhostImageActive.toString(),
        "isStatisticsActive": isStatisticsActive.toString(),
        "isFrontCamera": isFrontCamera.toString(),
        "isLandscape": isLandscape.toString(),
        "isHaveLocation": photoProvider.allPhotosList.last.photos.last.location != null ? "true" : "false",
        "noteLength": photoProvider.allPhotosList.last.notes != null ? photoProvider.allPhotosList.last.notes!.length : "null",
      },
    );
  }

  void userNativeLanguage() async {
    await _logger?.setUserProperty(name: "nativeLanguage", value: Platform.localeName);
  }

  void userIsAutoTake() async {
    await _logger?.setUserProperty(name: "isAutoTake", value: isAutoTake.toString());
  }

  void userIsRefPartsActive() async {
    await _logger?.setUserProperty(name: "isRefPartsActive", value: isRefPartsActive.toString());
  }

  void userIsGridActive() async {
    await _logger?.setUserProperty(name: "isGridActive", value: isGridActive.toString());
  }

  void userIsGhostImageActive() async {
    await _logger?.setUserProperty(name: "isGhostImageActive", value: isGhostImageActive.toString());
  }

  void userIsStatisticsActive() async {
    await _logger?.setUserProperty(name: "isStatisticsActive", value: isStatisticsActive.toString());
  }

  void exportVideo(VideoModel videoModel) async {
    await _logger?.logEvent(
      name: "export_video",
      parameters: {
        "duration": videoModel.duration.toString(),
        "durationPerPhoto": videoModel.durationPerPhoto.toString(),
        "photoCount": videoModel.photoCount.toString(),
        "dayCount": videoModel.dayCount.toString(),
        "resolution": videoModel.resolution,
        "firstPhotoDate": videoModel.firstPhotoDate.toString(),
        "lastPhotoDate": videoModel.lastPhotoDate.toString(),
        "musicName": videoModel.musicName ?? "null",
        "isBrandLogoActive": videoModel.isBrandLogoActive.toString(),
      },
    );
  }

  void mustPremiumDialog(bool isAccept) async {
    await _logger?.logEvent(
      name: "must_premium_dialog",
      parameters: {
        "isAccept": isAccept.toString(),
      },
    );
  }

  void tryFreePremiumDialog() async {
    await _logger?.setUserProperty(name: "isFreeTrailUsed", value: isFreeTrailUsed.toString());

    await _logger?.logEvent(
      name: "try_free_premium_dialog",
      parameters: {
        "isAccept": isFreeTrailUsed.toString(),
      },
    );
  }

  void notificationOnOff(String time) async {
    await _logger?.setUserProperty(name: "notification", value: isNotificationsEnabled ? time : "null");

    await _logger?.logEvent(
      name: "notification_on_off",
      parameters: {
        "isNotificationEnabled": isNotificationsEnabled.toString(),
      },
    );
  }

  void changeLanguage(String language) async {
    await _logger?.setUserProperty(name: "appLanguage", value: language);

    await _logger?.logEvent(
      name: "change_language",
      parameters: {
        "language": language,
      },
    );
  }

  void changeTheme(String theme) async {
    await _logger?.setUserProperty(name: "theme", value: theme);

    await _logger?.logEvent(
      name: "change_theme",
      parameters: {
        "theme": theme,
      },
    );
  }

  void changeAppColor(String color) async {
    await _logger?.setUserProperty(name: "color", value: color);

    await _logger?.logEvent(
      name: "change_app_color",
      parameters: {
        "color": color,
      },
    );
  }

  void againTutorialButton() async {
    await _logger?.logEvent(
      name: "again_tutorial_button",
    );
  }

  void changeOrientation() async {
    await _logger?.setUserProperty(name: "isLandspace", value: isLandscape.toString());

    await _logger?.logEvent(
      name: "change_orientation",
      parameters: {
        "isLandscape": isLandscape.toString(),
      },
    );
  }

  void rateUsButton() async {
    await _logger?.logEvent(
      name: "rate_us_button",
    );
  }

  void rateUsBottomSheet(double point) async {
    await _logger?.setUserProperty(name: "isRated", value: isRated.toString());

    await _logger?.logEvent(
      name: "rate_us_bottom_sheet",
      parameters: {
        "point": point.toInt(),
      },
    );
  }

  void shareFacelog() async {
    await _logger?.logEvent(
      name: "share_facelog",
    );
  }

  void deleteAllPhotos({required bool isImported}) async {
    await _logger?.logEvent(
      name: "delete_all_photos",
      parameters: {
        "isImported": isImported.toString(),
      },
    );
  }

  void exportPhotos(int photoCount) async {
    await _logger?.logEvent(
      name: "export_photos",
      parameters: {
        "photoCount": photoCount,
      },
    );
  }

  void importPhotos(int photoCount) async {
    await _logger?.logEvent(
      name: "import_photos",
      parameters: {
        "photoCount": photoCount,
      },
    );
  }

  void takePhoto(bool isAutoTake) async {
    await _logger?.logEvent(
      name: "take_photo",
      parameters: {
        "isAutoTake": isAutoTake.toString(),
      },
    );
  }

  void changeCamera() async {
    await _logger?.setUserProperty(name: "isFrontCamera", value: isFrontCamera.toString());

    await _logger?.logEvent(
      name: "change_camera",
    );
  }

  void addLocation() async {
    await _logger?.logEvent(
      name: "add_location",
    );
  }

  void addNote(String noteLenght) async {
    await _logger?.logEvent(
      name: "add_note",
      parameters: {
        "noteLenght": noteLenght,
      },
    );
  }

  void editNote(String noteLenght) async {
    await _logger?.logEvent(
      name: "edit_note",
      parameters: {
        "noteLenght": noteLenght,
      },
    );
  }

  void likePhoto() async {
    await _logger?.logEvent(
      name: "like_photo",
    );
  }

  void unlikePhoto() async {
    await _logger?.logEvent(
      name: "unlike_photo",
    );
  }

  void deletePhoto() async {
    await _logger?.logEvent(
      name: "delete_photo",
    );
  }

  void downloadPhoto() async {
    await _logger?.logEvent(
      name: "download_photo",
    );
  }

  void sharePhoto() async {
    await _logger?.logEvent(
      name: "share_photo",
    );
  }

  void manageSubscription() async {
    await _logger?.logEvent(
      name: "manage_subscription",
    );
  }

  void buySubscription(ProductDetails product, String status) async {
    await _logger?.logEvent(
      name: 'purschase',
      parameters: {
        'status': status,
        "plan": product.id,
        "currency": product.currencyCode,
        "price": product.price,
        // "coupon": couponCode,
      },
    );
  }

  void checkSubscription(String status) async {
    await _logger?.setUserProperty(name: "premiumStatus", value: status);
    await _logger?.setUserProperty(name: "isPremium", value: isPremium.toString());

    await _logger?.logEvent(
      name: "check_subscription",
      parameters: {
        "status": status,
      },
    );
  }

  void logError(String error) async {
    await _logger?.logEvent(
      name: "app_error",
      parameters: {
        "error": error,
      },
    );
  }

  void socialButton(String name) async {
    await _logger?.logEvent(
      name: "social_button",
      parameters: {
        "name": name,
      },
    );
  }

  void getFreezeRight() async {
    await _logger?.logEvent(
      name: "get_freeze_right",
    );
  }

  void useFreezeRight(int usedCount) async {
    await _logger?.logEvent(
      name: "use_freeze_right",
      parameters: {
        "freezeCount": usedCount,
      },
    );
  }

  void addStreak() async {
    await _logger?.logEvent(
      name: "add_streak",
    );
  }
}

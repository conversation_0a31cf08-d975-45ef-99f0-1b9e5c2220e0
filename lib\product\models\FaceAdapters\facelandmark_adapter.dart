import 'dart:math';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'facelandmark_adapter.g.dart';

@HiveType(typeId: 8)
class FaceLandmarkAdapter extends TypeAdapter<FaceLandmark> {
  @override
  final typeId = 8;

  @override
  FaceLandmark read(BinaryReader reader) {
    final type = reader.read() as FaceLandmarkType;
    final position = reader.read() as Point<int>;
    return FaceLandmark(type: type, position: position);
  }

  @override
  void write(BinaryWriter writer, FaceLandmark obj) {
    writer.write(obj.type);
    writer.write(obj.position);
  }
}

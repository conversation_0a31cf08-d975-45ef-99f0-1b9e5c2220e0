import 'dart:async';
import 'dart:io';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/Pages/Produce/Video/core/path_constants.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';

class VideoAPI {
  // * This function is for merging the images to the video.
  Future<void> mergeImagesToVideo({
    required BuildContext context,
    required String qualityIndex,
    required String videoLength,
    required bool isAudioNull,
    required bool isBrandLogoActive,
  }) async {
    if (kDebugMode) {
      print(isLandscape);
    }
    String newCommandToExecute = "";

    // create txt file
    final Directory cacheDir = await getApplicationCacheDirectory();
    var txtFile = File("${cacheDir.path}/photolist.txt");
    txtFile.createSync(recursive: true);

    late List<String> photoPathList;

    final photoProvider = context.read<PhotoProvider>();
    late final List<Photo> allphotos;

    // sadece face null olmayan fotoğrafları al
    allphotos = photoProvider.allPhotosList.map((e) => e.photos).expand((element) => element).where((element) => element.face != null).toList();

    if (allphotos.isEmpty) {
      if (kDebugMode) {
        print('❌ Face null olmayan fotoğraf bulunamadı');
      }
      return;
    }

    // Fotoğrafların pathlarini al
    photoPathList = allphotos.map((e) => e.path).toList();

    // Fotoğrafların varlığını kontrol et
    List<String> validPhotoPaths = [];
    for (String path in photoPathList) {
      final photoFile = File(path);
      if (await photoFile.exists()) {
        validPhotoPaths.add(path);
        if (kDebugMode) {
          final size = await photoFile.length();
          print('✅ Fotoğraf geçerli: $path ($size bytes)');
        }
      } else {
        if (kDebugMode) {
          print('❌ Fotoğraf bulunamadı: $path');
        }
      }
    }

    if (validPhotoPaths.isEmpty) {
      if (kDebugMode) {
        print('❌ Hiç geçerli fotoğraf bulunamadı');
      }
      return;
    }

    photoPathList = validPhotoPaths;

    if (kDebugMode) {
      print('📷 Toplam ${photoPathList.length} geçerli fotoğraf bulundu');
    }

    //txt file content write
    double onePhotoDuration = double.parse(videoLength) / photoPathList.length;
    String txtFileString = "";
    for (String path in photoPathList) {
      // Path'i escape et (güvenlik için)
      String escapedPath = path.replaceAll("'", "'\\''");
      txtFileString += "file '$escapedPath'\n";
      txtFileString += "duration $onePhotoDuration\n";
    }

    // Son fotoğraf için extra bir entry (FFmpeg concat sorunu için)
    if (photoPathList.isNotEmpty) {
      String escapedLastPath = photoPathList.last.replaceAll("'", "'\\''");
      txtFileString += "file '$escapedLastPath'\n";
    }

    txtFile.writeAsStringSync(txtFileString);

    if (kDebugMode) {
      print('📝 TXT dosyası içeriği:\n$txtFileString');
    }

    newCommandToExecute += '-f concat -safe 0 -i ${txtFile.path} ';

    if (qualityIndex == '240p') {
      if (isLandscape) {
        newCommandToExecute += '-vf scale=320:240 ';
      } else {
        newCommandToExecute += '-vf scale=240:320 ';
      }
    } else if (qualityIndex == '360p') {
      if (isLandscape) {
        newCommandToExecute += '-vf scale=640:360 ';
      } else {
        newCommandToExecute += '-vf scale=360:640 ';
      }
    } else if (qualityIndex == '480p') {
      if (isLandscape) {
        newCommandToExecute += '-vf scale=854:480 ';
      } else {
        newCommandToExecute += '-vf scale=480:854 ';
      }
    } else if (qualityIndex == '720p') {
      if (isLandscape) {
        newCommandToExecute += '-vf scale=1280:720 ';
      } else {
        newCommandToExecute += '-vf scale=720:1280 ';
      }
    } else if (qualityIndex == '1080p') {
      if (isLandscape) {
        newCommandToExecute += '-vf scale=1920:1080 ';
      } else {
        newCommandToExecute += '-vf scale=1080:1920 ';
      }
    } else if (qualityIndex == 'debug') {
      newCommandToExecute += '-vf scale=896:1152 ';
      // newCommandToExecute += '-vf scale=1024:1024 ';
    }

    newCommandToExecute += '-qscale:v 2 '; // video quality parameter between 1-31

    // Codec ve format ayarları
    newCommandToExecute += '-c:v libx264 -preset ultrafast ';
    newCommandToExecute += '-r 30 '; // frame rate
    newCommandToExecute += '-pix_fmt yuv420p '; // pixel format (uyumluluk için)

    newCommandToExecute += '-y ${(!isAudioNull || isBrandLogoActive) ? faceLogExportVideoRaw : faceLogExportVideo}';

    if (kDebugMode) {
      print('🎬 FFmpeg komutu: $newCommandToExecute');
    }

    final session = await FFmpegKit.execute(newCommandToExecute);
    final returnCode = await session.getReturnCode();

    if (kDebugMode) {
      final logs = await session.getLogs();
      print('🔍 FFmpeg return code: $returnCode');
      print('📋 FFmpeg logs:');
      for (var log in logs) {
        print('  ${log.getMessage()}');
      }
    }

    // Çıktı dosyasını kontrol et
    final outputPath = (!isAudioNull || isBrandLogoActive) ? faceLogExportVideoRaw : faceLogExportVideo;
    final outputFile = File(outputPath);
    if (await outputFile.exists()) {
      final fileSizeMB = (await outputFile.length()) / (1024 * 1024);
      if (kDebugMode) {
        print('✅ Video başarıyla oluşturuldu!');
        print('📁 Konum: $outputPath');
        print('📊 Boyut: ${fileSizeMB.toStringAsFixed(2)} MB');
      }
    } else {
      if (kDebugMode) {
        print('❌ Video oluşturulamadı: $outputPath');
      }
    }
    debugPrint(newCommandToExecute);
    debugPrint('Video oluşturma işlemi başlatılıyor...');

    // Txt dosyasını temizle
    if (await txtFile.exists()) {
      await txtFile.delete();
    }
  }

  // * This function is for exporting the brand logo to the device.
  Future<void> brandLogoExport() async {
    // get asset file
    final byteData = await rootBundle.load(AssetsPath.faceLogBrandLogo);
    final buffer = byteData.buffer;

    // save cache
    Directory cachePath = await getApplicationCacheDirectory();

    brandLogoPath = '${cachePath.path}/Facelog_BrandLogo.png';

    await File(brandLogoPath).writeAsBytes(buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
  }

  Future<void> addBrandLogo({
    required String qualityIndex,
    required bool isAudioNull,
  }) async {
    String newCommandOverlay;

    newCommandOverlay = '-i $faceLogExportVideoRaw ';

    await brandLogoExport();
    debugPrint('brandLogoExport methodu çalıştı bitti.');

    newCommandOverlay += '-i $brandLogoPath ';

    if (isLandscape) {
      if (qualityIndex == '240p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=60:60[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h+15" ';
        newCommandOverlay += '-s 320x240 ';
      } else if (qualityIndex == '360p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=120:120[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h+20" ';
        newCommandOverlay += '-s 640x360 ';
      } else if (qualityIndex == '480p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=170:170[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h+30" ';
        newCommandOverlay += '-s 854x480 ';
      } else if (qualityIndex == '720p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=290:290[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h+50" ';
        newCommandOverlay += '-s 1280x720 ';
      } else if (qualityIndex == '1080p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=400:400[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h+50" ';
        newCommandOverlay += '-s 1920x1080 ';
      }
    } else {
      if (qualityIndex == '240p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=70:70[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h-10" ';
        newCommandOverlay += '-s 240x320 ';
      } else if (qualityIndex == '360p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=120:120[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h-10" ';
        newCommandOverlay += '-s 360x640 ';
      } else if (qualityIndex == '480p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=170:170[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h-10" ';
        newCommandOverlay += '-s 480x854 ';
      } else if (qualityIndex == '720p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=250:250[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h-10" ';
        newCommandOverlay += '-s 720x1280 ';
      } else if (qualityIndex == '1080p') {
        newCommandOverlay += '-filter_complex "[1:v]scale=350:350[watermark];[0:v][watermark]overlay=(main_w-overlay_w)/2:main_h-overlay_h-10" ';
        newCommandOverlay += '-s 1080x1920 ';
      }
    }

    newCommandOverlay += '-qscale:v 2 '; // video quality parameter between 1-31

    // Codec ve format ayarları
    newCommandOverlay += '-c:v libx264 -preset ultrafast ';
    newCommandOverlay += '-r 30 '; // frame rate
    newCommandOverlay += '-pix_fmt yuv420p '; // pixel format

    debugPrint('$newCommandOverlay Bu video overlay fonksiyonuna ait bir komut');
    newCommandOverlay += '-codec:a copy ${isAudioNull ? faceLogExportVideo : faceLogExportVideoLogo} ';

    if (kDebugMode) {
      print('🎬 Brand Logo FFmpeg komutu: $newCommandOverlay');
    }

    final session = await FFmpegKit.execute(newCommandOverlay);
    final returnCode = await session.getReturnCode();

    if (kDebugMode) {
      final logs = await session.getLogs();
      print('🔍 Brand Logo FFmpeg return code: $returnCode');
      print('📋 Brand Logo FFmpeg logs:');
      for (var log in logs) {
        print('  ${log.getMessage()}');
      }
    }

    // Çıktı dosyasını kontrol et
    final outputPath = isAudioNull ? faceLogExportVideo : faceLogExportVideoLogo;
    final outputFile = File(outputPath);
    if (await outputFile.exists()) {
      final fileSizeMB = (await outputFile.length()) / (1024 * 1024);
      if (kDebugMode) {
        print('✅ Brand Logo video başarıyla oluşturuldu!');
        print('📁 Konum: $outputPath');
        print('📊 Boyut: ${fileSizeMB.toStringAsFixed(2)} MB');
      }
    } else {
      if (kDebugMode) {
        print('❌ Brand Logo video oluşturulamadı: $outputPath');
      }
    }

    File(brandLogoPath).deleteSync();
  }

  Future<void> saveMusicToDevice({
    required String audioFilePath,
  }) async {
    final ByteData byteData = await rootBundle.load(audioFilePath);
    final buffer = byteData.buffer;

    Directory cachePath = await getApplicationCacheDirectory();

    cachedAudioPath = '${cachePath.path}/audio.mp3';

    await File(cachedAudioPath).writeAsBytes(buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
  }

  Future<void> addAudioToVideo({
    required bool isBrandLogoActive,
    required String audioFilePath,
    required bool audioIsAssets,
    required int audioStartTime,
  }) async {
    // eğer brand logo var ise brand logolu videoyu ver yoksa raw videoyu ver
    String command = '-i ${isBrandLogoActive ? faceLogExportVideoLogo : faceLogExportVideoRaw} ';

    // selected audio is asset
    if (audioIsAssets) {
      debugPrint(audioFilePath);

      await saveMusicToDevice(audioFilePath: audioFilePath);
      debugPrint('Müzik cihaza kaydedildi.');
      debugPrint(cachedAudioPath);
    }
    if (audioIsAssets) {
      command += '-ss $audioStartTime -i $cachedAudioPath ';
    } else {
      command += '-ss $audioStartTime -i $audioFilePath ';
    }

    command += '-map 0:v -map 1:a ';
    command += '-c:v copy -c:a aac ';

    command += '-shortest $faceLogExportVideo';

    debugPrint('$command: This is a command to add music.');

    if (kDebugMode) {
      print('🎬 Audio FFmpeg komutu: $command');
    }

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();

    if (kDebugMode) {
      final logs = await session.getLogs();
      print('🔍 Audio FFmpeg return code: $returnCode');
      print('📋 Audio FFmpeg logs:');
      for (var log in logs) {
        print('  ${log.getMessage()}');
      }
    }

    // Çıktı dosyasını kontrol et
    final outputFile = File(faceLogExportVideo);
    if (await outputFile.exists()) {
      final fileSizeMB = (await outputFile.length()) / (1024 * 1024);
      if (kDebugMode) {
        print('✅ Audio video başarıyla oluşturuldu!');
        print('📁 Konum: $faceLogExportVideo');
        print('📊 Boyut: ${fileSizeMB.toStringAsFixed(2)} MB');
      }
    } else {
      if (kDebugMode) {
        print('❌ Audio video oluşturulamadı: $faceLogExportVideo');
      }
    }
  }
}

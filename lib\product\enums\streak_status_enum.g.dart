// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'streak_status_enum.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class StreakStatusAdapter extends TypeAdapter<StreakStatus> {
  @override
  final int typeId = 14;

  @override
  StreakStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return StreakStatus.streak;
      case 1:
        return StreakStatus.freeze;
      case 2:
        return StreakStatus.broken;
      case 3:
        return StreakStatus.empty;
      default:
        return StreakStatus.streak;
    }
  }

  @override
  void write(BinaryWriter writer, StreakStatus obj) {
    switch (obj) {
      case StreakStatus.streak:
        writer.writeByte(0);
        break;
      case StreakStatus.freeze:
        writer.writeByte(1);
        break;
      case StreakStatus.broken:
        writer.writeByte(2);
        break;
      case StreakStatus.empty:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreakStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

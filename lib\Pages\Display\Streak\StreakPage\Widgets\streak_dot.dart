import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/models/streak_model.dart';
import 'package:facelog/product/services/streak_helper.dart';
import 'package:facelog/product/state/Provider/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

class StreakDot extends StatelessWidget {
  final StreakModel streakModel;

  const StreakDot({
    super.key,
    required this.streakModel,
  });

  @override
  Widget build(BuildContext context) {
    context.watch<ThemeProvider>().themeMode;

    return Column(
      children: [
        streakModel.status == StreakStatus.freeze
            ? Padding(
                padding: const EdgeInsets.all(4),
                child: SvgPicture.asset(
                  AssetsPath.iceDot,
                  width: 35,
                  height: 35,
                ),
              )
            : getDot(),
        Text(
          streakModel.date.day.toString(),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Container getDot() {
    return Container(
      margin: const EdgeInsets.all(4),
      width: 30,
      height: 30,
      decoration: BoxDecoration(
        color: StreakHelper().getDotColor(streakModel.status),
        borderRadius: AppColors.borderRadiusCircular,
        border: Border.all(
          color: StreakHelper().getDotColor(streakModel.status),
          width: 2,
        ),
      ),
      child: streakModel.status == StreakStatus.empty
          ? const SizedBox()
          : Icon(
              streakModel.status == StreakStatus.streak ? Icons.check : Icons.close,
              color: AppColors.white,
            ),
    );
  }
}

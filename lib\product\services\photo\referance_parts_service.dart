import 'package:facelog/product/models/ReferanceParts/refrance_parts.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:provider/provider.dart';

class ReferancePartsService {
  static const String _referancePartsBOX = 'ReferanceParts';

  Offset eye1Axis = isLandscape ? const Offset(196.5, 243.4) : const Offset(132.2, 266.4);
  Offset eye2Axis = isLandscape ? const Offset(194.0, 329.9) : const Offset(219.8, 264.2);
  Offset mouthAxis = isLandscape ? const Offset(94.2, 285.6) : const Offset(165.7, 365.8);

  Future<Box<ReferanceParts>> get _box async {
    return await Hive.openBox<ReferanceParts>(_referancePartsBOX);
  }

  // Functions ------------------------------
  // Get
  Future<ReferanceParts> getReferanceParts(BuildContext context) async {
    final box = await _box;

    if (box.values.isNotEmpty && context.read<PhotoProvider>().allPhotosList.isNotEmpty) {
      return box.values.first;
    } else {
      await box.clear();
      // Default Referance Parts
      return ReferanceParts(
        eye1: eye1Axis,
        eye2: eye2Axis,
        mouth: mouthAxis,
      );
    }
  }

  // Add
  Future<void> addReferanceParts(ReferanceParts referances) async {
    final box = await _box;
    await box.add(referances);
  }
}

import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class SocialMediaIcons extends StatelessWidget {
  const SocialMediaIcons({
    super.key,
    required this.link,
    required this.iconType,
    required this.name,
  });

  final String link;
  final IconData iconType;
  final String name;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        LogService().socialButton(name);
        _launchURL(Uri.parse(link));
      },
      child: Container(
        padding: const EdgeInsets.all(10),
        child: Icon(
          iconType,
          size: 24,
          color: AppColors.grey,
        ),
      ),
    );
  }

  void _launchURL(Uri url) async {
    await launchUrl(url);
  }
}

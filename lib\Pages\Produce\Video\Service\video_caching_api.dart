import 'dart:typed_data';

import 'package:facelog/Pages/Produce/Video/core/path_constants.dart';
import 'package:facelog/Pages/Produce/Video/core/video_variables.dart';
import 'package:facelog/product/models/Video/video_model.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/video_cache_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class VideoCachingAPI {
  Future<VideoModel> getAndCacheVideoInfo(
    BuildContext context,
    VideoModel? savedVideoModel,
    VideoPlayerController controller,
  ) async {
    await controller.initialize();

    late final VideoModel videoModel;

    if (savedVideoModel == null) {
      late int photoCount;
      late DateTime? firstPhotoDate;
      late DateTime? lastPhotoDate;

      final allPhotosList = context.read<PhotoProvider>().allPhotosList;

      photoCount = allPhotosList.map((e) => e.photos).expand((element) => element).toList().where((element) => element.face != null).length;
      firstPhotoDate = allPhotosList.firstWhere((element) => element.photos.any((element) => element.face != null)).photos.first.date;
      lastPhotoDate = allPhotosList.lastWhere((element) => element.photos.any((element) => element.face != null)).photos.last.date;

      videoModel = VideoModel(
        path: faceLogExportVideo,
        thumbnailByte: await _generateThumbnail(faceLogExportVideo),
        createdAt: DateTime.now(),
        duration: "${controller.value.duration.inMinutes.toString().padLeft(2, '0')}:${((controller.value.duration.inSeconds + 1) % 60).toString().padLeft(2, '0')}",
        durationPerPhoto: "${((controller.value.duration.inSeconds + 1) / photoCount).toStringAsFixed(2)} second",
        photoCount: photoCount,
        dayCount: allPhotosList.length,
        resolution: await _getVideoResolution(controller),
        firstPhotoDate: firstPhotoDate!,
        lastPhotoDate: lastPhotoDate!,
        isBrandLogoActive: isBrandLogoActive,
        musicName: soundModelList.any((soundModel) => soundModel.isSelected) ? soundModelList.firstWhere((soundModel) => soundModel.isSelected).name : null,
      );

      VideoModelCacheService().addVideo(videoModel);
      LogService().exportVideo(videoModel);
    } else {
      videoModel = savedVideoModel;
    }

    return videoModel;
  }

  Future<Uint8List> _generateThumbnail(String videoPath) async {
    final thumbnailBytes = await VideoThumbnail.thumbnailData(
      video: videoPath,
      imageFormat: ImageFormat.PNG,
    );
    return thumbnailBytes;
  }

  Future<String> _getVideoResolution(VideoPlayerController controller) async {
    final size = controller.value.size;
    return '${size.width.toInt()}x${size.height.toInt()}';
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/state/Provider/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class TotalPhotoCount extends StatelessWidget {
  const TotalPhotoCount({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    context.watch<ThemeProvider>().themeMode;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.only(left: 16),
          child: Text(
            '${LocaleKeys.Home_Total.tr()}:',
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(width: 5),
        Text(
          '${context.watch<PhotoProvider>().allPhotosList.expand((element) => element.photos).length} ${LocaleKeys.Home_photo.tr()}',
          style: TextStyle(
            fontSize: 20,
            color: AppColors.main,
          ),
        ),
      ],
    );
  }
}

import 'dart:io';
import 'dart:math';
import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/camera_mixin.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_painter.dart';
import 'package:facelog/Pages/Produce/CameraProgress/components/face_parts.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/photo/referance_parts_service.dart';
import 'package:facelog/Pages/Produce/CameraProgress/components/accelometer.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class CameraPage extends StatefulWidget {
  const CameraPage({
    super.key,
  });

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> with WidgetsBindingObserver, PhotoSaveMixin {
  // Texts
  final String _changeCamera = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_ChangeCamera.tr();
  final String _changeCameraDescription = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_ChangeCameraDesc.tr();
  final String _autoTake = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_AutoTake.tr();
  final String _autoTakeDescription = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_AutoTakeDesc.tr();
  final String _ghostImage = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_GhostImage.tr();
  final String _ghostImageDescription = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_GhostImageDesc.tr();
  final String _refParts = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_ReferanceParts.tr();
  final String _refPartsDescription = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_ReferancePartsDesc.tr();
  final String _grid = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_Grid.tr();
  final String _gridDescription = LocaleKeys.CameraPages_Camera_LocationService_Tutorial_GridDesc.tr();

  late final PhotoProvider photoProvider = context.read<PhotoProvider>();

  bool isInitialized = false;
  // ilk açılışta didChangeAppLifecycleState ile sorun olmaması için
  bool isInitializing = true;
  bool isVertical = true;

  bool isLoading = false;

  // Showcase Keys
  late BuildContext contextForShowcase;
  final GlobalKey changeCameraKey = GlobalKey();
  final GlobalKey autoTakeKey = GlobalKey();
  final GlobalKey ghostImageKey = GlobalKey();
  final GlobalKey refPartsKey = GlobalKey();
  final GlobalKey gridKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    initCamera();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        WakelockPlus.enable();
        getReferancesHIVE();
      },
    );

    LogService().logScreen("CameraPage");
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // CameraController'ı güvenli şekilde dispose et
    if (cameraController != null) {
      cameraController!.dispose();
      cameraController = null;
    }

    isPhotoChecking = false;

    currentFace = null;

    faceDetector.close();

    WakelockPlus.disable();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && !isInitializing && cameraController != null && !isPhotoChecking) {
      _refreshCamera();
    } else if (state == AppLifecycleState.paused && cameraController != null && cameraController!.value.isInitialized && !isPhotoChecking) {
      try {
        isInitialized = false;
        cameraController!.pausePreview();
      } catch (e) {
        debugPrint('Error pausing camera: $e');
      }
    }
  }

  void _refreshCamera() {
    try {
      if (cameraController != null && cameraController!.value.isInitialized) {
        cameraController!.resumePreview();
        if (mounted) {
          setState(() {
            isInitialized = true;
          });
        }
      } else {
        // Kamera controller'ı geçersizse yeniden başlat
        getCamera(isChange: false);
      }
    } catch (e) {
      debugPrint('Error in _refreshCamera: $e');
      // Hata durumunda yeniden başlat
      getCamera(isChange: false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      disableMovingAnimation: true,
      builder: (BuildContext context) {
        contextForShowcase = context;
        return Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            leading: IconButton(
              padding: const EdgeInsets.only(left: 10),
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            actions: [
              IconButton(
                icon: const Icon(
                  Icons.question_mark,
                ),
                onPressed: () {
                  ShowCaseWidget.of(contextForShowcase).startShowCase(
                    [
                      changeCameraKey,
                      autoTakeKey,
                      ghostImageKey,
                      refPartsKey,
                      gridKey,
                    ],
                  );
                },
              ),
              const SizedBox(width: 5)
            ],
            title: const Text(LocaleKeys.CameraPages_Camera_TakePhoto).tr(),
            backgroundColor: AppColors.background,
          ),
          body: !isInitialized
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    Stack(
                      children: [
                        ClipRRect(
                          borderRadius: AppColors.highBorderRadiusAll,
                          child: SizedOverflowBox(
                            size: Size(1.sw, 0.76.sh),
                            child: CameraPreview(
                              cameraController!,
                            ),
                          ),
                        ),
                        if (isRefPartsActive && photoProvider.allPhotosList.isNotEmpty) ...[
                          eyes1(),
                          eyes2(),
                          mouth(),
                        ],
                        if (isGhostImageActive && photoProvider.allPhotosList.isNotEmpty)
                          ClipRRect(
                            borderRadius: AppColors.highBorderRadiusAll,
                            child: SizedOverflowBox(
                              size: Size(1.sw, 0.76.sh),
                              child: Transform.scale(
                                // TODO: burada 1.8 responsive sorun oluşturmuyor gibi duruyor ancak emulatorun kameralarıyla emin olamadım. teste çıkınca gerçek cihazlarda dikkat et.
                                scale: isLandscape ? 1.8 : 1,
                                child: Transform.rotate(
                                  angle: isLandscape ? pi / 2 : 0,
                                  child: Opacity(
                                    opacity: 0.4,
                                    child: Image.file(
                                      File(photoProvider.allPhotosList.last.photos.last.path),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        if (isGridActive)
                          Positioned.fill(
                            child: Image.asset(
                              AssetsPath.grid,
                              scale: 1,
                            ),
                          ),
                        Positioned.fill(
                          bottom: isLandscape ? 0 : 0.6.sh,
                          left: isLandscape ? 0.55.sw : 0,
                          child: AccelometerCheckWidget(
                            onCameraRestart: () {
                              if (mounted) {
                                // Kamerayı yeniden başlat
                                getCamera(isChange: false);
                              }
                            },
                          ),
                        ),
                        if (kDebugMode) ...[
                          if (currentFace != null)
                            Center(
                              child: CustomPaint(
                                size: Size(0.93.sw, 0.76.sh),
                                painter: FacePainter(face: currentFace),
                              ),
                            ),
                        ],
                        Positioned(
                          bottom: 0,
                          child: Container(
                            width: 1.sw,
                            padding: const EdgeInsets.all(5),
                            child: Row(
                              children: [
                                autoTakeButton(),
                                const Spacer(),
                                ghostImageButton(),
                                const SizedBox(width: 10),
                                refPartsButton(),
                                const SizedBox(width: 10),
                                gridButton(),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    !isLoading
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              // TODO: fotoğraf çekme butonu ortada, kamera değiştirme butonu sağda olacak şekilde yapamadım aw. böyle bir şey uydurdum. önceden de böyle çözüyordum bu problemi.
                              IgnorePointer(
                                child: IconButton(
                                  onPressed: () {},
                                  icon: const Icon(
                                    color: AppColors.transparent,
                                    Icons.camera,
                                    size: 28,
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () async {
                                  if (!isPhotoChecking && cameraController != null && cameraController!.value.isInitialized) {
                                    setState(() {
                                      isLoading = true;
                                    });
                                    await takePhoto(context, onCameraRestart: () {
                                      if (mounted) {
                                        // Kamerayı yeniden başlat
                                        getCamera(isChange: false);
                                      }
                                    });

                                    if (mounted) {
                                      setState(() {
                                        isLoading = false;
                                      });
                                    }
                                  }
                                },
                                icon: const Icon(
                                  Icons.center_focus_weak_rounded,
                                  size: 65,
                                ),
                              ),
                              changeCameraButton(),
                            ],
                          )
                        : const Center(
                            child: CircularProgressIndicator(),
                          ),
                    const Spacer(),
                  ],
                ),
        );
      },
    );
  }

  Widget changeCameraButton() {
    return Showcase(
      key: changeCameraKey,
      title: _changeCamera,
      description: _changeCameraDescription,
      overlayOpacity: 0.7,
      targetBorderRadius: AppColors.highBorderRadiusAll,
      targetPadding: AppColors.showcasePadding,
      child: IconButton(
        onPressed: () async {
          if (!isPhotoChecking && cameraController != null) {
            LogService().changeCamera();

            isFrontCamera = !isFrontCamera;

            SharedPreferences prefs = await SharedPreferences.getInstance();
            prefs.setBool('isFrontCamera', isFrontCamera);

            getCamera(isChange: true);
          }
        },
        icon: Transform.rotate(
          angle: isLandscape ? pi / 2 : 0,
          child: const Icon(
            Icons.flip_camera_ios,
            size: 28,
          ),
        ),
      ),
    );
  }

  Widget autoTakeButton() {
    return Showcase(
      key: autoTakeKey,
      title: _autoTake,
      description: _autoTakeDescription,
      overlayOpacity: 0.7,
      targetBorderRadius: AppColors.highBorderRadiusAll,
      targetPadding: AppColors.showcasePadding,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.background.withValues(alpha: 0.7),
        ),
        child: IconButton(
          onPressed: () async {
            accelometerTimerCount = 3;

            setState(() {
              isAutoTake = !isAutoTake;
            });
            SharedPreferences prefs = await SharedPreferences.getInstance();
            prefs.setBool('isAutoTake', isAutoTake);
            LogService().userIsAutoTake();
          },
          icon: Transform.rotate(
            angle: isLandscape ? pi / 2 : 0,
            child: Icon(
              isAutoTake ? Icons.timer_outlined : Icons.timer_off_outlined,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }

  Widget ghostImageButton() {
    return Showcase(
      key: ghostImageKey,
      title: _ghostImage,
      description: _ghostImageDescription,
      overlayOpacity: 0.7,
      targetBorderRadius: AppColors.highBorderRadiusAll,
      targetPadding: AppColors.showcasePadding,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.background.withValues(alpha: 0.7),
        ),
        child: IconButton(
          onPressed: () async {
            if (photoProvider.allPhotosList.isEmpty) {
              await Helper().getDialog(
                message: LocaleKeys.CameraPages_Camera_GhostImage.tr(),
              );
            } else {
              setState(() {
                isGhostImageActive = !isGhostImageActive;
              });
              SharedPreferences prefs = await SharedPreferences.getInstance();
              prefs.setBool('isGhostImageActive', isGhostImageActive);
              LogService().userIsGhostImageActive();
            }
          },
          icon: Transform.rotate(
            angle: isLandscape ? pi / 2 : 0,
            child: Icon(
              (isGhostImageActive && photoProvider.allPhotosList.isNotEmpty) ? Icons.group_outlined : Icons.group_off_outlined,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }

  Widget refPartsButton() {
    return Showcase(
      key: refPartsKey,
      title: _refParts,
      description: _refPartsDescription,
      overlayOpacity: 0.7,
      targetBorderRadius: AppColors.highBorderRadiusAll,
      targetPadding: AppColors.showcasePadding,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.background.withValues(alpha: 0.7),
        ),
        child: IconButton(
          onPressed: () async {
            if (photoProvider.allPhotosList.isEmpty) {
              await Helper().getDialog(
                message: LocaleKeys.CameraPages_Camera_RefParts.tr(),
              );
            } else {
              setState(() {
                isRefPartsActive = !isRefPartsActive;
              });
              SharedPreferences prefs = await SharedPreferences.getInstance();
              prefs.setBool('isRefPartsActive', isRefPartsActive);
              LogService().userIsRefPartsActive();
            }
          },
          icon: Transform.rotate(
            angle: isLandscape ? pi / 2 : 0,
            child: Icon(
              (isRefPartsActive && photoProvider.allPhotosList.isNotEmpty) ? Icons.visibility_outlined : Icons.visibility_off_outlined,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }

  Widget gridButton() {
    return Showcase(
      key: gridKey,
      title: _grid,
      description: _gridDescription,
      overlayOpacity: 0.7,
      targetBorderRadius: AppColors.highBorderRadiusAll,
      targetPadding: AppColors.showcasePadding,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.background.withValues(alpha: 0.7),
        ),
        child: IconButton(
          onPressed: () async {
            setState(() {
              isGridActive = !isGridActive;
            });
            SharedPreferences prefs = await SharedPreferences.getInstance();
            prefs.setBool('isGridActive', isGridActive);
            LogService().userIsGridActive();
          },
          icon: Transform.rotate(
            angle: isLandscape ? pi / 2 : 0,
            child: Icon(
              isGridActive ? Icons.grid_on : Icons.grid_off,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }

  void getCamera({required bool isChange}) {
    try {
      // Eski controller'ı dispose et
      if (isChange && cameraController != null) {
        cameraController!.dispose();
        cameraController = null;
      }

      if (cameras == null || cameras!.isEmpty) {
        debugPrint('No cameras available');
        return;
      }

      CameraDescription? selectedCamera;
      for (var element in cameras!) {
        if (isFrontCamera ? element.lensDirection == CameraLensDirection.front : element.lensDirection == CameraLensDirection.back) {
          selectedCamera = element;
          break;
        }
      }

      if (selectedCamera != null) {
        cameraController = CameraController(
          selectedCamera,
          ResolutionPreset.max,
          enableAudio: false,
        );

        cameraController!.initialize().then(
          (value) {
            if (mounted) {
              setState(() {
                isInitialized = true;
                isInitializing = false;
              });
            }
          },
        ).catchError((error) {
          debugPrint('Error initializing camera: $error');
          if (mounted) {
            setState(() {
              isInitialized = false;
              isInitializing = false;
            });
          }
        });
      }
    } catch (e) {
      debugPrint('Error in getCamera: $e');
      if (mounted) {
        setState(() {
          isInitialized = false;
          isInitializing = false;
        });
      }
    }
  }

  void initCamera() async {
    try {
      PermissionStatus camStatus = await Permission.camera.request();

      if (camStatus.isGranted) {
        cameras = await availableCameras();

        if (cameras!.isNotEmpty) {
          getCamera(isChange: false);
        }
      } else {
        Navigator.pop(context);
        Helper().getMessage(
          message: LocaleKeys.Home_costumDialog.tr(),
          status: StatusEnum.INFO,
        );
      }
    } catch (_) {
      Navigator.pop(context);
      Helper().getMessage(
        message: LocaleKeys.CameraPages_Camera_ErrorCamera.tr(),
        status: StatusEnum.WARNING,
      );
    }
  }

  Future<void> getReferancesHIVE() async {
    refParts = await ReferancePartsService().getReferanceParts(context);
  }
}

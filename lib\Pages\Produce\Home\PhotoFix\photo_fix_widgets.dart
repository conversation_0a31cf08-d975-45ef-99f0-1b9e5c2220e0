import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PhotoFixWidgets {
  /// Fix Photos butonunu oluşturur
  static Widget buildFixPhotosButton({
    required VoidCallback onPressed,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.main.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.main, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Icon(
              Icons.auto_fix_high,
              color: AppColors.main,
              size: 24,
            ),
            SizedBox(height: 8.h),
            Text(
              LocaleKeys.Home_PhotoFix_Title.tr(),
              style: TextStyle(
                color: AppColors.main,
                fontWeight: FontWeight.bold,
                fontSize: 45.sp,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              LocaleKeys.Home_PhotoFix_Description.tr(),
              style: TextStyle(
                color: AppColors.text.withValues(alpha: 0.7),
                fontSize: 35.sp,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.main,
                  foregroundColor: AppColors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  LocaleKeys.Home_PhotoFix_ButtonText.tr(),
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 50.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Fixing Photos progress indicator'ını oluşturur
  static Widget buildFixingPhotosIndicator({
    required int currentFixingPhoto,
    required int totalFixingPhotos,
  }) {
    double progressValue = totalFixingPhotos > 0 ? currentFixingPhoto / totalFixingPhotos : 0.0;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.main.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.main, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Progress Circle
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 60,
                  height: 60,
                  child: CircularProgressIndicator(
                    value: progressValue,
                    color: AppColors.main,
                    backgroundColor: AppColors.main.withValues(alpha: 0.2),
                    strokeWidth: 4,
                  ),
                ),
                Text(
                  "${(progressValue * 100).toInt()}%",
                  style: TextStyle(
                    color: AppColors.main,
                    fontWeight: FontWeight.bold,
                    fontSize: 30.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              '${LocaleKeys.Home_PhotoFix_ProgressTitle.tr()} ($currentFixingPhoto/$totalFixingPhotos)',
              style: TextStyle(
                color: AppColors.main,
                fontWeight: FontWeight.bold,
                fontSize: 45.sp,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              totalFixingPhotos > 0 ? 'Fotoğraf işleniyor... ($currentFixingPhoto/$totalFixingPhotos)' : LocaleKeys.Home_PhotoFix_ProgressDescription.tr(),
              style: TextStyle(
                color: AppColors.text.withValues(alpha: 0.7),
                fontSize: 35.sp,
              ),
              textAlign: TextAlign.center,
            ),
            if (totalFixingPhotos > 0) ...[
              SizedBox(height: 12.h),
              // Progress Bar
              LinearProgressIndicator(
                value: progressValue,
                color: AppColors.main,
                backgroundColor: AppColors.main.withValues(alpha: 0.2),
                minHeight: 4,
              ),
              SizedBox(height: 8.h),
              Text(
                '${(progressValue * 100).toInt()}% tamamlandı',
                style: TextStyle(
                  color: AppColors.main,
                  fontSize: 32.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

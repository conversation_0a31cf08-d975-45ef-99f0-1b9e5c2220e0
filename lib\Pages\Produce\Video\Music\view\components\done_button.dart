import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/Music/view/add_music_view.dart';
import 'package:facelog/Pages/Produce/Video/Preview/preview_video_view.dart';
import 'package:facelog/Pages/Produce/Video/Service/rendering_api.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';

class DoneButton extends StatelessWidget {
  final bool isLoading;
  final AddMusicView widget;
  final double startSecond;

  const DoneButton({
    super.key,
    required this.isLoading,
    required this.widget,
    required this.startSecond,
  });

  final String _doneButtonText = LocaleKeys.AddMusicPage_DoneButton;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        if (isLoading) return;
        await VideoAPI().addAudioToVideo(
          isBrandLogoActive: widget.isBrandLogoActive,
          audioFilePath: widget.audioFilePath,
          audioIsAssets: widget.isAsset,
          audioStartTime: startSecond.toInt(),
        );

        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const VideoPreviewPage(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.fastOutSlowIn;

              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          ),
        );
      },
      child: Container(
        color: Colors.transparent,
        padding: const EdgeInsets.fromLTRB(12, 12, 20, 12),
        child: Text(
          _doneButtonText,
          style: const TextStyle(fontSize: 17),
        ).tr(),
      ),
    );
  }
}

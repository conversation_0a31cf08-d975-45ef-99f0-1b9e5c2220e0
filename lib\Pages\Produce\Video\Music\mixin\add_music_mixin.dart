import 'dart:async';
import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:facelog/Pages/Produce/Video/Music/view/add_music_view.dart';
import 'package:facelog/Pages/Produce/Video/core/path_constants.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

mixin AddMusicMixin on State<AddMusicView> {
  final AudioPlayer audioPlayer = AudioPlayer();
  final ScrollController scrollController = ScrollController();
  double containerCenter = 0.0;
  final double rangerWidth = 135.0;
  Duration currentPosition = Duration.zero;
  double startSecond = 0.0;
  late double endSecond;
  bool isPlaying = false;
  Timer? timer;
  Duration pausedPosition = Duration.zero;
  late Timer? timerScrollListener;
  double lastOffset = 0.0;
  bool isScrolling = false;
  final double spaceBetweenContainers = 6;
  final double singleLineWidth = 3;
  late VideoPlayerController controller;
  late Future<void> initializeVideoPlayerFuture;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    scrollController.addListener(onScroll);
    endSecond = startSecond + widget.videoDuration;

    // Müziğin belirli saniyeler içerisinde hareket etmesini sağlıyoruz.
    audioPlayer.onPositionChanged.listen((position) {
      setState(() {
        currentPosition = position;
      });

      if (currentPosition.inSeconds >= endSecond) {
        // Sync video with audio
        audioPlayer.seek(Duration(seconds: startSecond.round()));
        controller.seekTo(Duration(seconds: currentPosition.inSeconds));
      }
    });

    scrollController.addListener(scrollListener);

    isScrollControl();

    controller = VideoPlayerController.file(File(faceLogExportVideoRaw));
    initializeVideoPlayerFuture = controller.initialize();
    controller.setLooping(true);
  }

  // Kullanıcının scroll hareketini dinliyoruz.
  void scrollListener() {
    timer?.cancel();
    if (scrollController.offset != lastOffset) {
      lastOffset = scrollController.offset;
      setState(() {
        isScrolling = true;
      });

      if (isPlaying) {
        _stopMusic();
      }

      // Kaydırırsa müzik ve ses ranger'ın en başına gidiyor.
      pausedPosition = Duration(seconds: startSecond.round());
      controller.seekTo(Duration.zero);
    }

    setState(() {
      // Ne kadar sürüklediğini kontrol edip başlangıç değerini güncelliyoruz.
      startSecond = (scrollController.offset / (spaceBetweenContainers + singleLineWidth)) * widget.videoDuration / (rangerWidth / (spaceBetweenContainers + singleLineWidth));
      currentPosition = Duration(seconds: startSecond.round());
    });
    endSecond = startSecond + widget.videoDuration;
  }

  void onScroll() {
    timerScrollListener?.cancel();
    setState(() {
      isScrolling = true;
    });

    timerScrollListener = Timer(const Duration(milliseconds: 400), () {
      if (scrollController.offset == scrollController.position.pixels) {
        setState(() {
          isScrolling = false;
        });
      }
    });
  }

  void isScrollControl() {
    timerScrollListener = Timer(
      const Duration(milliseconds: 400),
      () {
        if (scrollController.hasClients && scrollController.offset == lastOffset) {
          setState(() {
            isScrolling = false;
          });
        }
      },
    );
  }

  void playMusicInRange(String filePath) async {
    Duration position = pausedPosition.inSeconds > startSecond ? pausedPosition : Duration(seconds: startSecond.round());

    if (widget.isAsset) {
      await audioPlayer.play(
        AssetSource(filePath.substring(7)),
        position: position,
      );
    } else {
      await audioPlayer.play(
        DeviceFileSource(filePath),
        position: position,
      );
    }

    controller.play();

    setState(() {
      isPlaying = true;
    });

    timer = Timer.periodic(const Duration(milliseconds: 400), (timer) {
      if (currentPosition.inSeconds >= endSecond) {
        audioPlayer.seek(Duration(seconds: startSecond.round()));
        controller.seekTo(Duration(seconds: startSecond.round()));
      }
    });
  }

  void _stopMusic() {
    audioPlayer.pause();
    pausedPosition = currentPosition;
    timer?.cancel();
    controller.pause();
    setState(() {
      isPlaying = false;
    });
  }

  void togglePlayPause() {
    if (isPlaying) {
      _stopMusic();
    } else {
      playMusicInRange(widget.audioFilePath);
    }
    setState(() {});
  }

  @override
  void dispose() {
    audioPlayer.dispose();
    timer?.cancel();
    scrollController.dispose();
    controller.dispose();
    scrollController.removeListener(onScroll);
    scrollController.removeListener(scrollListener);
    audioPlayer.dispose();
    timerScrollListener?.cancel();
    super.dispose();
  }
}

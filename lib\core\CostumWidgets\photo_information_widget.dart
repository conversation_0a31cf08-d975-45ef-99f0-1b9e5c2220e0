import 'package:facelog/core/CostumWidgets/photo_show_widget.dart';
import 'package:facelog/core/CostumWidgets/photo_action_buttons.dart';
import 'package:facelog/core/CostumWidgets/photo_description_area.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/core/components/photo_counter_dots.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class PhotoInformationWidget extends StatefulWidget {
  final OneDayPhotos oneDayPhotos;
  // selectedPhoto slider da tıklanan fotoğrafa otomatik kayması için
  final Photo? selectedPhoto;
  final Function()? setStateParent;
  final Function()? updateCalendarForDelete;

  const PhotoInformationWidget({
    super.key,
    required this.oneDayPhotos,
    this.selectedPhoto,
    this.setStateParent,
    this.updateCalendarForDelete,
  });

  @override
  State<PhotoInformationWidget> createState() => _PhotoInformationWidgetState();
}

class _PhotoInformationWidgetState extends State<PhotoInformationWidget> with SingleTickerProviderStateMixin {
  late final AnimationController likeAnimationController;
  late final Animation<double> likeAnimation;

  final PageController pageController = PageController();
  late int currentPhotoIndex;

  @override
  void initState() {
    super.initState();

    if (widget.selectedPhoto != null) {
      currentPhotoIndex = widget.oneDayPhotos.photos.indexWhere((element) => element == widget.selectedPhoto);
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        pageController.jumpToPage(currentPhotoIndex);
      });
    } else {
      currentPhotoIndex = 0;
    }

    likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    likeAnimation = CurvedAnimation(
      parent: likeAnimationController,
      curve: Curves.elasticOut,
    );
  }

  @override
  void didUpdateWidget(PhotoInformationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.oneDayPhotos != oldWidget.oneDayPhotos) {
      currentPhotoIndex = 0;
    }
  }

  @override
  void dispose() {
    likeAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Builder(
            builder: (context) {
              return SizedBox(
                height: photoSize,
                child: PageView.builder(
                  onPageChanged: (currentPhotoIndexPage) {
                    setState(() {
                      currentPhotoIndex = currentPhotoIndexPage;
                    });
                  },
                  controller: pageController,
                  scrollDirection: Axis.horizontal,
                  itemCount: widget.oneDayPhotos.photos.length,
                  itemBuilder: (BuildContext context, int currentPhotoIndexPage) {
                    return GestureDetector(
                      onDoubleTap: () {
                        if (isFavoriFeatureActive) {
                          context.read<PhotoProvider>().photoChanceFavoriteStates(
                                context: context,
                                oneDayPhotos: widget.oneDayPhotos,
                                selectedPhotoIndex: currentPhotoIndex,
                                likeAnimationController: likeAnimationController,
                              );
                        }
                        if (widget.setStateParent != null) {
                          widget.setStateParent!();
                        }
                      },
                      child: PhotoShowWidget(
                        oneDayPhotos: widget.oneDayPhotos,
                        currentPhotoIndexPage: currentPhotoIndexPage,
                        likeAnimation: likeAnimation,
                        likeAnimationController: likeAnimationController,
                        setStateParent: widget.setStateParent,
                      ),
                    );
                  },
                ),
              );
            },
          ),
          PhotoCounterDots(
            oneDayPhotos: widget.oneDayPhotos,
            currentPhotoIndex: currentPhotoIndex,
          ),
          if (isDescriptionFeatureActive)
            PhotoDescriptionArea(
              oneDayPhoto: widget.oneDayPhotos,
              setStateCalendar: widget.setStateParent,
            ),
          const SizedBox(height: 10),
          PhotoActionButtons(
            oneDayPhotos: widget.oneDayPhotos,
            photo: widget.oneDayPhotos.photos[currentPhotoIndex],
            updateSelectedPhotoForCalendar: widget.updateCalendarForDelete,
            pageController: pageController,
            setState: () {
              setState(() {});
            },
          ),
          SizedBox(height: 0.03.sh)
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:markdown_widget/widget/all.dart';

class PDFviewer extends StatefulWidget {
  const PDFviewer({
    super.key,
    required this.text,
    required this.markdownDataPath,
  });

  final String text;
  final String markdownDataPath;

  @override
  State<PDFviewer> createState() => _PDFviewerState();
}

class _PDFviewerState extends State<PDFviewer> {
  String _markdownData = "";

  @override
  void initState() {
    super.initState();
    _loadMarkdownData();
  }

  Future<void> _loadMarkdownData() async {
    final String data = await rootBundle.loadString(widget.markdownDataPath);
    setState(() {
      _markdownData = data;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          widget.text,
        ),
      ),
      // body: SfPdfViewer.asset(assetPath),
      body: MarkdownWidget(data: _markdownData),
    );
  }
}

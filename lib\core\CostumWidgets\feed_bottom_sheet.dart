import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/core/CostumWidgets/photo_action_buttons.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';

class FeedPageBottomSheet extends StatefulWidget {
  const FeedPageBottomSheet({
    super.key,
    required this.oneDayPhotos,
    required this.currentPhotoIndex,
  });

  final OneDayPhotos oneDayPhotos;
  final int currentPhotoIndex;

  @override
  State<FeedPageBottomSheet> createState() => _FeedPageBottomSheetState();
}

class _FeedPageBottomSheetState extends State<FeedPageBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 8,
        ),
        child: Column(
          children: [
            SizedBox(height: 15),
            PhotoActionButtons(
              oneDayPhotos: widget.oneDayPhotos,
              photo: widget.oneDayPhotos.photos[widget.currentPhotoIndex],
              isFeedPage: true,
            ),
            const SizedBox(height: 16.0),
            if (isDescriptionFeatureActive) ...[
              const Divider(),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  Helper()
                      .showEditDescriptionSheet(
                        context: context,
                        oneDayPhoto: widget.oneDayPhotos,
                      )
                      .then(
                        (_) => {
                          LogService().editNote(widget.oneDayPhotos.notes == null ? "null" : widget.oneDayPhotos.notes!.length.toString()),
                        },
                      );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.edit,
                        size: 32,
                      ),
                      const SizedBox(width: 8.0),
                      const Text(
                        LocaleKeys.FeedPage_BottomSheet_Edit,
                        style: TextStyle(
                          fontSize: 18.0,
                        ),
                      ).tr(),
                      const Spacer()
                    ],
                  ),
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }
}

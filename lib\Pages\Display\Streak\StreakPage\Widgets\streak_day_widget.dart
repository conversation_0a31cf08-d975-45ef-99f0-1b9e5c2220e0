import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/streak_helper.dart';
import 'package:flutter/material.dart';

class StreakDayNumber extends StatelessWidget {
  StreakDayNumber({
    super.key,
  });

  final String day = LocaleKeys.Home_Streak_Day.tr();
  final streakCount = StreakHelper().calculateStreak();

  @override
  Widget build(BuildContext context) {
    return Text(
      "$streakCount $day",
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 40,
      ),
    );
  }
}

import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/services/streak_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class StreakProgressBar extends StatelessWidget {
  const StreakProgressBar({
    super.key,
  });

  double _calculateProgressValue() {
    final streakCount = StreakHelper().calculateStreak();

    if (streakCount == 1 || streakCount == 5 || streakCount == 10) {
      return 1.0;
    } else if (isPremium! && streakCount > 10) {
      return (streakCount % 5 == 0) ? 1.0 : (streakCount % 5) / 5;
    } else if (streakCount > 10) {
      return (streakCount % 10 == 0) ? 1.0 : (streakCount % 10) / 10;
    } else if (streakCount > 5 && streakCount < 10) {
      return (streakCount - 5) / 5;
    } else {
      return streakCount / 5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(width: 120.w),
        SizedBox(
          width: 700.w,
          child: LinearProgressIndicator(
            borderRadius: AppColors.borderRadiusAll,
            color: AppColors.blue,
            backgroundColor: const Color.fromARGB(255, 146, 179, 206),
            value: _calculateProgressValue(),
          ),
        ),
        SizedBox(width: 30.w),
        SvgPicture.asset(
          AssetsPath.iceDot,
          width: 70.w,
        ),
        SizedBox(width: 10.w),
        Text(
          (freezeCount + 1).toString(),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 40.sp,
          ),
        ),
      ],
    );
  }
}

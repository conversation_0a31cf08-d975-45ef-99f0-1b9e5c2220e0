import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/Feed/Widgets/feed_photo_widget.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/navbar_provider.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/core/CostumWidgets/no_photo.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class FeedPage extends StatefulWidget {
  const FeedPage({
    super.key,
    this.controller,
  });

  final ScrollController? controller;

  @override
  FeedPageState createState() => FeedPageState();
}

class FeedPageState extends State<FeedPage> with TickerProviderStateMixin {
  late List<OneDayPhotos> currentList;
  late List<OneDayPhotos> favoritePhotos;

  late bool isAllMode;

  @override
  void initState() {
    super.initState();
    favoritePhotos = context.read<PhotoProvider>().allPhotosList.where((oneDayPhotos) => oneDayPhotos.photos.any((photo) => photo.isFavorite)).toList();
    isAllMode = favoritePhotos.isEmpty;
    currentList = isAllMode ? context.read<PhotoProvider>().allPhotosList : favoritePhotos;

    // Fotoğraf silinince sayfayı güncellemek için
    context.read<PhotoProvider>().addListener(() {
      if (mounted) {
        setState(
          () {
            favoritePhotos = context.read<PhotoProvider>().allPhotosList.where((oneDayPhotos) => oneDayPhotos.photos.any((photo) => photo.isFavorite)).toList();
            currentList = isAllMode ? context.read<PhotoProvider>().allPhotosList : favoritePhotos;
          },
        );
      }
    });

    LogService().logScreen("FeedPage");
  }

  @override
  Widget build(BuildContext context) {
    // TODO: burayı kontrol et gerekli mi bozuk mu
    currentList = isAllMode ? context.watch<PhotoProvider>().allPhotosList : favoritePhotos;
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          navbarIsVisible = true;
          context.read<NavbarProvider>().updateIndex(2);
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        extendBody: true,
        appBar: AppBar(
          title: Text(
            LocaleKeys.FeedPage_AppBar_Title.tr(),
          ),
          actions: [
            if (isFavoriFeatureActive)
              InkWell(
                borderRadius: AppColors.borderRadiusAll,
                onTap: () {
                  showFilterMenu(context);
                },
                child: Container(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      Text(
                        isAllMode ? LocaleKeys.FeedPage_AppBar_Feed_All.tr() : LocaleKeys.FeedPage_AppBar_Feed_Favorite.tr(),
                      ),
                      const Icon(
                        Icons.arrow_drop_down,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        body: currentList.isEmpty
            ? const AnyPhotosWidget()
            : ListView.builder(
                padding: EdgeInsets.fromLTRB(
                  0.0,
                  0.0,
                  0.0,
                  MediaQuery.of(context).viewInsets.bottom + 50,
                ),
                shrinkWrap: true,
                controller: widget.controller,
                itemCount: currentList.length,
                itemBuilder: (context, index) {
                  return FeedPhotoWidget(
                    oneDayPhotos: currentList[currentList.length - index - 1],
                  );
                },
              ),
      ),
    );
  }

  void showFilterMenu(BuildContext context) {
    showMenu(
      context: context,
      color: AppColors.appbar,
      surfaceTintColor: AppColors.background,
      position: RelativeRect.fromRect(
        Rect.fromPoints(
          const Offset(100, 0),
          Offset(MediaQuery.of(context).size.width, kToolbarHeight),
        ),
        Offset.zero & MediaQuery.of(context).size,
      ),
      items: [
        PopupMenuItem(
          value: 'All',
          padding: const EdgeInsets.all(0),
          child: buildFilterMenuItem(
            LocaleKeys.FeedPage_AppBar_Feed_All.tr(),
            !isAllMode,
            Icons.align_horizontal_left_sharp,
          ),
        ),
        PopupMenuItem(
          value: 'Favourites',
          padding: const EdgeInsets.all(0),
          child: buildFilterMenuItem(
            LocaleKeys.FeedPage_AppBar_Feed_Favorite.tr(),
            isAllMode,
            Icons.favorite_border,
          ),
        ),
      ],
    ).then(
      (value) {
        if (value != null) {
          setState(
            () {
              isAllMode = value != LocaleKeys.FeedPage_AppBar_Feed_Favorite.tr();
              currentList = isAllMode ? context.read<PhotoProvider>().allPhotosList : favoritePhotos;
            },
          );
        }
      },
    );
  }

  //icon parameter
  Widget buildFilterMenuItem(String text, bool isSelected, IconData icon) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
      tileColor: AppColors.transparent,
      title: SizedBox(
        width: 60,
        child: AutoSizeText(
          text,
          maxLines: 1,
          style: TextStyle(
            color: isSelected ? AppColors.dirtyWhite : null,
            fontWeight: isSelected ? null : FontWeight.bold,
          ),
        ),
      ),
      trailing: Icon(icon, color: isSelected ? AppColors.dirtyWhite : null),
      onTap: () {
        Navigator.pop(context, text);
      },
    );
  }
}

import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/warning_box_component.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ImportExportTutorialPage extends StatelessWidget {
  ImportExportTutorialPage({super.key});
  // Texts
  final String _importPhotosTitle = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description_Title.tr();
  final String _importPhotosContent = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_description_Content.tr();
  final String _importPhotosButtonText = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_WarningBoxImportPhotos_ButtonText.tr();
  final String _description = LocaleKeys.TutorialPage_WarningBoxTutorialDescription.tr();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              WarningBox(
                textColor: AppColors.white,
                buttonColor: AppColors.deepBlue,
                headerText: _importPhotosTitle,
                description: _importPhotosContent,
                firstButtonText: _importPhotosButtonText,
                onFirstButtonClick: () {},
              ),
              SizedBox(height: 0.1.sh),
              AutoSizeText(
                _description,
                style: const TextStyle(
                  color: AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';

class SoundModel {
  final String name;
  final String path;
  Duration duration;
  Duration position;
  bool isSelected;
  bool isPlaying;
  final bool isObjectPermanent;
  final AudioPlayer audioPlayer;
  AnimationController? soundRotationAnimController;

  SoundModel({
    required this.name,
    required this.path,
    required this.duration,
    required this.position,
    required this.isSelected,
    required this.isPlaying,
    this.isObjectPermanent = false,
    required this.audioPlayer,
    this.soundRotationAnimController,
  });
}

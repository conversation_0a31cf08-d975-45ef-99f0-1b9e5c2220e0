// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'one_day_photos.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OneDayPhotosAdapter extends TypeAdapter<OneDayPhotos> {
  @override
  final int typeId = 4;

  @override
  OneDayPhotos read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OneDayPhotos(
      date: fields[0] as DateTime,
      photos: (fields[1] as List).cast<Photo>(),
      notes: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, OneDayPhotos obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.photos)
      ..writeByte(2)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OneDayPhotosAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

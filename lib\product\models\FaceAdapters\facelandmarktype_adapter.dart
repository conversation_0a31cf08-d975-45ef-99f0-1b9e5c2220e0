import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'facelandmarktype_adapter.g.dart';

// @HiveType(typeId: 1)
// enum FaceLandmarkTypeAdapter {
//   @HiveField(0)
//   bottomMouth,

//   @HiveField(1)
//   leftCheek,

//   @HiveField(2)
//   leftEar,

//   @HiveField(3)
//   leftEye,

//   @HiveField(4)
//   leftMouth,

//   @HiveField(5)
//   noseBase,

//   @HiveField(6)
//   rightCheek,

//   @HiveField(7)
//   rightEar,

//   @HiveField(8)
//   rightEye,

//   @HiveField(9)
//   rightMouth,
// }
@HiveType(typeId: 7)
class FaceLandmarkTypeAdapter extends TypeAdapter<FaceLandmarkType> {
  @override
  final typeId = 7;

  @override
  FaceLandmarkType read(BinaryReader reader) {
    final index = reader.readInt();
    return FaceLandmarkType.values[index];
  }

  @override
  void write(BinaryWriter writer, FaceLandmarkType obj) {
    writer.writeInt(obj.index);
  }
}

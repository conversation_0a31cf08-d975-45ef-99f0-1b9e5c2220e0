import 'package:hive_flutter/hive_flutter.dart';
import 'dart:math';

part 'point_adapter.g.dart';

@HiveType(typeId: 9)
class PointAdapter extends TypeAdapter<Point<int>> {
  @override
  final typeId = 9;

  @override
  Point<int> read(BinaryReader reader) {
    final x = reader.readInt();
    final y = reader.readInt();
    return Point<int>(x, y);
  }

  @override
  void write(BinaryWriter writer, Point<int> obj) {
    writer.writeInt(obj.x);
    writer.writeInt(obj.y);
  }
}

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

mixin DiskCacheMixin {
  /// Cache dizinini oluştur ve döndür
  Future<Directory> getCacheDirectory() async {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final Directory cacheDir = Directory('${appDocDir.path}/PhotoCache');

    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    return cacheDir;
  }

  /// Fotoğrafı cache'e kopyala ve cache path'ini döndür
  Future<String> cachePhoto(String originalPath, String uniqueId) async {
    try {
      final Directory cacheDir = await getCacheDirectory();
      final File originalFile = File(originalPath);

      if (!await originalFile.exists()) {
        throw Exception('Original file not found: $originalPath');
      }

      // Cache dosya adını oluştur
      final String extension = originalPath.split('.').last;
      final String cachedFileName = '${uniqueId}_cached.$extension';
      final String cachedPath = '${cacheDir.path}/$cachedFileName';

      // Eğer cache'de zaten varsa, onu döndür
      final File cachedFile = File(cachedPath);
      if (await cachedFile.exists()) {
        debugPrint('Photo already cached: $cachedPath');
        return cachedPath;
      }

      // Fotoğrafı cache'e kopyala
      await originalFile.copy(cachedPath);
      debugPrint('Photo cached successfully: $cachedPath');

      return cachedPath;
    } catch (e) {
      debugPrint('Error caching photo: $e');
      return originalPath; // Fallback to original path
    }
  }

  /// Cache'deki dosyayı sil
  Future<void> deleteCachedPhoto(String cachedPath) async {
    try {
      final File cachedFile = File(cachedPath);
      if (await cachedFile.exists()) {
        await cachedFile.delete();
        debugPrint('Cached photo deleted: $cachedPath');
      }
    } catch (e) {
      debugPrint('Error deleting cached photo: $e');
    }
  }

  /// Cache'deki tüm dosyaları temizle
  Future<void> clearPhotoCache() async {
    try {
      final Directory cacheDir = await getCacheDirectory();
      if (await cacheDir.exists()) {
        await for (final entity in cacheDir.list()) {
          if (entity is File) {
            await entity.delete();
          }
        }
        debugPrint('Photo cache cleared');
      }
    } catch (e) {
      debugPrint('Error clearing photo cache: $e');
    }
  }

  /// Cache boyutunu hesapla (MB cinsinden)
  Future<double> getCacheSizeInMB() async {
    try {
      final Directory cacheDir = await getCacheDirectory();
      if (!await cacheDir.exists()) return 0.0;

      int totalSize = 0;
      await for (final entity in cacheDir.list()) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }

      return totalSize / (1024 * 1024); // Convert to MB
    } catch (e) {
      debugPrint('Error calculating cache size: $e');
      return 0.0;
    }
  }

  /// Cache boyutu çok büyükse temizle
  Future<void> manageCacheSize({double maxSizeInMB = 500}) async {
    try {
      final double currentSize = await getCacheSizeInMB();

      if (currentSize > maxSizeInMB) {
        debugPrint('Cache size ($currentSize MB) exceeds limit ($maxSizeInMB MB). Clearing...');
        await clearPhotoCache();
      }
    } catch (e) {
      debugPrint('Error managing cache size: $e');
    }
  }
}

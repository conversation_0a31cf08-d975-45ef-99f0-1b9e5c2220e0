import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:flutter/material.dart';

class NoteBottomSheet extends StatefulWidget {
  const NoteBottomSheet({
    super.key,
    required this.oneDayPhoto,
    required this.onDispose,
  });

  final OneDayPhotos? oneDayPhoto;
  final Function? onDispose;

  @override
  NoteBottomSheetState createState() => NoteBottomSheetState();
}

class NoteBottomSheetState extends State<NoteBottomSheet> {
  late TextEditingController noteController;
  @override
  void initState() {
    super.initState();
    noteController = TextEditingController(text: widget.oneDayPhoto?.notes);
  }

  @override
  void dispose() {
    if (widget.onDispose != null) {
      widget.onDispose!(noteController.text);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: AppColors.borderRadiusTop,
          ),
          padding: EdgeInsets.only(
            right: 10,
            left: 10,
            top: 10,
            bottom: MediaQuery.of(context).viewInsets.bottom + 70,
          ),
          child: TextField(
            autocorrect: true,
            textCapitalization: TextCapitalization.sentences,
            style: const TextStyle(
              fontSize: 14,
            ),
            controller: noteController,
            autofocus: true,
            maxLength: 5000,
            maxLines: null,
            buildCounter: (BuildContext context, {int? currentLength, int? maxLength, bool? isFocused}) => null,
            decoration: InputDecoration(
              hintText: LocaleKeys.General_NoteBottomSheetNoteSomethingHintText.tr(),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
            textInputAction: TextInputAction.newline,
            onChanged: (text) {
              widget.oneDayPhoto?.notes = text;
            },
          ),
        ),
        Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom + 10,
          right: 12,
          child: GestureDetector(
            onTap: () {
              widget.oneDayPhoto?.notes = noteController.text;
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              child: ClipRRect(
                borderRadius: AppColors.borderRadiusAll,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.main,
                  ),
                  child: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Icon(
                      Icons.send,
                      size: 20,
                      color: AppColors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

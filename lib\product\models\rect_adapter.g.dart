// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rect_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RectAdapterAdapter extends TypeAdapter<RectAdapter> {
  @override
  final int typeId = 6;

  @override
  RectAdapter read(BinaryReader reader) {
    return RectAdapter();
  }

  @override
  void write(BinaryWriter writer, RectAdapter obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RectAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

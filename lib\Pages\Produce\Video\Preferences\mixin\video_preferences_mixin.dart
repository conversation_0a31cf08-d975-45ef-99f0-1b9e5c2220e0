import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/Video/Music/view/add_music_view.dart';
import 'package:facelog/Pages/Produce/Video/Preview/preview_video_view.dart';
import 'package:facelog/Pages/Produce/Video/core/video_variables.dart';
import 'package:facelog/product/models/sound_model.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/Pages/Produce/Video/core/path_constants.dart';
import 'package:facelog/Pages/Produce/Video/Service/rendering_api.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/video_export_page.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:path/path.dart' as path;

late int allPhotosCountForLoading;
ValueNotifier<int> croppedPhotoIndexForLoading = ValueNotifier<int>(0);

bool isVideoExportProgressLoading = false;

mixin VideoPreferencesMixin on State<VideoPreferencesView> {
  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  late final PhotoProvider photoProvider = context.read<PhotoProvider>();

  // * Pick music file for the video
  Future<void> pickMultiFile() async {
    await Permission.audio.request();
    await Permission.storage.request();
    if (await Permission.audio.isGranted || await Permission.storage.isGranted) {
      FilePickerResult? selectedAudioList = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: true,
      );
      if (selectedAudioList != null && selectedAudioList.files.isNotEmpty) {
        for (PlatformFile file in selectedAudioList.files) {
          if (soundModelList.any((element) => element.name == file.name)) {
            debugPrint('This music is already added');
            // TODO: Kullanıcıya bir uyarı verilecek.
          } else {
            Directory cachePath = await getApplicationCacheDirectory();
            // seçilen dosyanın ismi sorun oluşturmaması için kendimimz bir kopyasını oluşturuyoruz
            var newFile = File(file.path!).copySync('${cachePath.path}/${DateTime.now().millisecondsSinceEpoch}.mp3');

            // get duration
            var audioPlayer = AudioPlayer();
            await audioPlayer.setSourceDeviceFile(newFile.path);
            var duration = await audioPlayer.getDuration();

            soundModelList.add(
              SoundModel(
                name: file.name.substring(0, file.name.length - 4),
                path: newFile.path,
                // TODO: gerçek süreyi al
                duration: duration!,
                position: Duration.zero,
                isSelected: false,
                isPlaying: false,
                audioPlayer: AudioPlayer(),
              ),
            );
          }
          setState(() {});
        }
      } else {
        debugPrint('User canceled the picker');
        return;
      }
    }
    if (await Permission.audio.isPermanentlyDenied && await Permission.storage.isPermanentlyDenied) {
      await Helper().getDialog(
        message: LocaleKeys.VideoPreferencesPage_Body_AudioSource_Permission.tr(),
        onAccept: () async {
          openAppSettings();
        },
      );
    }
  }

  // stop all music and their animations when user click on the render button
  Future<void> stopAllMusic() async {
    for (SoundModel soundModel in soundModelList) {
      soundModel.audioPlayer.pause();
      soundModel.soundRotationAnimController!.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> renderFunction({
    required String qualityIndex,
    required String videoLength,
    String? audioFilePath,
    required bool audioIsAssets,
  }) async {
    stopAllMusic();

    // selectedSoundModel'u kontrol edin
    SoundModel? selectedSoundModel;
    selectedSoundModel = soundModelList.any((soundModel) => soundModel.isSelected) ? soundModelList.firstWhere((soundModel) => soundModel.isSelected) : null;

    late final List<Photo> allphotos;

    // sadece face null olmayanları al
    allphotos = photoProvider.allPhotosList.map((e) => e.photos).expand((element) => element).where((element) => element.face != null).toList();

    if (allphotos.length < 3) {
      final String threePhoto = LocaleKeys.VideoPreferencesPage_AtLeastThreePhoto.tr();

      await Helper().getDialog(
        message: threePhoto,
      );
    } else if (selectedSoundModel != null && double.parse(videoLength) > selectedSoundModel.duration.inSeconds) {
      await Helper().getDialog(
        message: LocaleKeys.AddMusicPage_warning.tr(),
      );
    } else {
      croppedPhotoIndexForLoading.value = 0;
      allPhotosCountForLoading = allphotos.length;
      isVideoExportProgressLoading = true;

      if (mounted) {
        setState(() {});
      }

      final Directory appDocDir = await getApplicationDocumentsDirectory();

      if (Platform.isIOS) {
        final Directory newDir = Directory(path.join(appDocDir.path, 'Facelog'));

        if (!await newDir.exists()) {
          await newDir.create(recursive: true);
          debugPrint('Klasör oluşturuldu.');
        }
      } else if (Platform.isAndroid) {
        Directory(faceLogDownload).createSync(recursive: true);
      }

      Directory cachePath = await getApplicationCacheDirectory();

      String dateString = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());

      faceLogExportVideoRaw = "${cachePath.path}/${dateString}_facelog_raw.mp4";
      faceLogExportVideoLogo = "${cachePath.path}/${dateString}_facelog_logo.mp4";
      if (Platform.isAndroid) {
        faceLogExportVideo = "$faceLogDownload${dateString}_facelog.mp4";
      } else if (Platform.isIOS) {
        faceLogExportVideo = "${appDocDir.path}/Facelog/${dateString}_facelog.mp4";
      }

      await VideoAPI().mergeImagesToVideo(
        context: context,
        qualityIndex: qualityIndex,
        videoLength: videoLength,
        isAudioNull: audioFilePath == null,
        isBrandLogoActive: isBrandLogoActive,
      );
      if (!isVideoExportProgressLoading) return;
      if (isBrandLogoActive) {
        await VideoAPI().addBrandLogo(
          qualityIndex: qualityIndex,
          isAudioNull: audioFilePath == null,
        );
      }
      if (!isVideoExportProgressLoading) return;
      if (audioFilePath != null && selectedSoundModel != null) {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => AddMusicView(
              audioFilePath: selectedSoundModel!.path,
              durationOfMusic: selectedSoundModel.duration,
              videoDuration: double.parse(videoLength),
              isBrandLogoActive: isBrandLogoActive,
              isAsset: audioIsAssets,
            ),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.fastOutSlowIn;

              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          ),
        ).then(
          (_) {
            setState(
              () {
                isVideoExportProgressLoading = false;
              },
            );
          },
        );
      } else {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const VideoPreviewPage(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.fastOutSlowIn;

              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          ),
        ).then(
          (_) {
            setState(
              () {
                isVideoExportProgressLoading = false;
              },
            );
          },
        );
      }
    }
  }
}

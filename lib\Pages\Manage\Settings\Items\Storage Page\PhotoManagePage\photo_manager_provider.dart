import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:flutter/material.dart';

class PhotoManagerProvider with ChangeNotifier {
  List<Photo> importedPhotos = [];
  List<Photo> selectedPhotos = [];
  Photo? currentlyAnalyzingPhoto;

  // Functions ------------------------------
  Future<void> addPhotos(List<Photo> newSelectedPhotos) async {
    debugPrint('Adding ${newSelectedPhotos.length} photos to import list');

    // aynı path e sahip ise sil
    final int beforeCount = newSelectedPhotos.length;
    newSelectedPhotos.removeWhere((element) => importedPhotos.any((photo) => photo.path == element.path));
    final int afterCount = newSelectedPhotos.length;

    if (beforeCount != afterCount) {
      debugPrint('Filtered out ${beforeCount - afterCount} duplicate photos');
    }

    importedPhotos.addAll(newSelectedPhotos);
    debugPrint('Total imported photos: ${importedPhotos.length}');
    notifyListeners();
  }

  void selectPhoto(Photo photo) {
    if (selectedPhotos.contains(photo)) {
      selectedPhotos.remove(photo);
    } else {
      selectedPhotos.add(photo);
    }
    notifyListeners();
  }

  void deleteSelectedPhotos() {
    importedPhotos.removeWhere((element) => selectedPhotos.contains(element));
    selectedPhotos = [];
    notifyListeners();
  }

  void changeDate(BuildContext context) async {
    var selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );
    if (selectedDate == null) return;
    for (var photo in selectedPhotos) {
      photo.date = selectedDate;
    }
    notifyListeners();
  }

  void setCurrentlyAnalyzingPhoto(Photo? photo) {
    currentlyAnalyzingPhoto = photo;
    notifyListeners();
  }
}

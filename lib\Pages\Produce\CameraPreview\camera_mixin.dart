import 'dart:async';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/streak_page.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_preview_view.dart';
import 'package:facelog/core/CostumWidgets/NavBar/navbar.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/streak_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_editor/image_editor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
// ! NOT: Mixin kullanmka yerine bizim PhotoService kullandığımız gibi de kullanabiliyoruz. Anladığım kadarıyla bir fark yok. Bence PhotoService gibi kullanalım buna benzer durumlarda.
// burası kamera ve accelometer arasında ortak kullanıldığı için buraya yazıldı

// ?TODO: Research: Aşağıda da olduğu gibi diışarıdan direkt erişilebilen birçok değişken kullanıyoruz. Bu kullanım yöntemi normal mi? Bir sorun oluşturuyor mu?.
bool isPhotoChecking = false;
int accelometerTimerCount = 3;
Timer? accelometerTimer;
CameraController? cameraController;
late List<CameraDescription>? cameras;
XFile? cameraPhoto;

mixin PhotoSaveMixin {
  Future<void> savePhoto({
    required BuildContext context,
    required String? address,
    required String photoPath,
    required String? dayNoteText,
  }) async {
    late final PhotoProvider photoProvider = context.read<PhotoProvider>();
    Directory directory = await getApplicationDocumentsDirectory();
    directory = Directory("${directory.path}/Photos");
    Directory(directory.path).createSync(recursive: true);

    lastPhotoNumber++;

    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setInt('lastPhotoNumber', lastPhotoNumber);

    if (isLandscape) {
      // 1
      File file = File(photoPath);
      Uint8List? imageBytes = await file.readAsBytes();

      // 2
      final ImageEditorOption option = ImageEditorOption();
      option.addOption(const RotateOption(-90));

      imageBytes = await ImageEditor.editImage(
        image: imageBytes,
        imageEditorOption: option,
      );

      // 3
      await file.delete();
      await file.writeAsBytes(imageBytes!);
    }

    if (photoProvider.allPhotosList.isNotEmpty && photoProvider.allPhotosList.last.date.day == DateTime.now().day) {
      await photoProvider.addPhotoInDay(
        photo: Photo(
          path: path.join(directory.path, "$lastPhotoNumber.jpg"),
          date: DateTime.now(),
          location: address,
          face: currentFace,
        ),
        dayNoteText: dayNoteText,
        cacheImagePath: photoPath,
      );

      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
            builder: (context) => const NavBarAndPages(
                  isFirst: false,
                )),
        (route) => false,
      );
    } else {
      await photoProvider.addOnePhotoDay(
        oneDayPhoto: OneDayPhotos(
          date: DateTime.now(),
          photos: [
            Photo(
              path: path.join(directory.path, "$lastPhotoNumber.jpg"),
              date: DateTime.now(),
              location: address,
              face: currentFace,
            ),
          ],
          notes: dayNoteText,
        ),
        cacheImagePath: photoPath,
      );

      if (!streakList.any((element) => element.status != StreakStatus.empty) || !Helper().isSameDay(streakList.lastWhere((element) => element.status != StreakStatus.empty).date, DateTime.now())) {
        StreakService().addStreak();

        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const StreakPage()),
          (route) => false,
        );
      } else {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
              builder: (context) => const NavBarAndPages(
                    isFirst: false,
                  )),
          (route) => false,
        );
      }
    }

    LogService().savePhoto(context: context);
  }

  Future<String?> getLocationPermissionAndService({
    required BuildContext context,
    required LocationAccuracy accuracy,
  }) async {
    try {
      context = context;
      LocationPermission permission = await Geolocator.checkPermission();
      debugPrint('permission: $permission');

      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      debugPrint('serviceEnabled: $serviceEnabled');

      if (permission == LocationPermission.always || permission == LocationPermission.whileInUse) {
        if (serviceEnabled) {
          Position position = await Geolocator.getCurrentPosition(
              locationSettings: LocationSettings(
            accuracy: accuracy,
          ));

          return await getAddressFromPosition(
            position: position,
            acc: accuracy,
          );
        } else {
          // TODO: burada her seferinde tekrar izin vermek gibi oluyor. kullanıcıya gps'i açmasını söylemek daha iyi olabilir veya otomatik biz açalım. location: ^6.0.1
          await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text(LocaleKeys.CameraPages_Camera_LocationService_AccessLocationDialog_OpenCamera).tr(),
              content: const Text(LocaleKeys.CameraPages_Camera_LocationService_AccessLocationDialog_ExplanationOfPermission).tr(),
              actions: [
                TextButton(
                  onPressed: () async {
                    await Geolocator.openLocationSettings();
                    Navigator.pop(context);
                  },
                  child: const Text(LocaleKeys.CameraPages_Camera_LocationService_AccessLocationDialog_GoSettingsButton).tr(),
                ),
              ],
            ),
          );
        }
      } else {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.always || permission == LocationPermission.whileInUse) {
          return await getLocationPermissionAndService(
            context: context,
            accuracy: accuracy,
          );
        } else {
          Helper().getMessage(message: LocaleKeys.CameraPages_Camera_LocationService_AccessLocationDialog_ExplanationOfPermission.tr());

          return null;
        }
      }
    } catch (e) {
      debugPrint('Error: $e');

      Helper().getMessage(
        message: LocaleKeys.General_AppError.tr(),
        status: StatusEnum.WARNING,
      );
      return null;
    }
    return null;
  }

  Future<String?> getAddressFromPosition({
    required Position position,
    required LocationAccuracy acc,
  }) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(position.latitude, position.longitude);
      // TODO: 39.9845206, 32.7652131 deneme için
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        if (acc == LocationAccuracy.low) {
          debugPrint('address: ${place.locality}, ${place.administrativeArea}, ${place.country}');
          return '${place.locality}, ${place.administrativeArea}, ${place.country}';
          // TODO: Sanırım place.locality eyalet demekmiş. Amerikada falan bu kullanılır. Bunu Amerika'lı müşterilerimiz için düşünmemiz gerekiyor.
        } else {
          debugPrint('address: ${place.locality}, ${place.administrativeArea}, ${place.country}');
          return '${place.thoroughfare}, ${place.subLocality}, ${place.administrativeArea}';
        }
      } else {
        debugPrint('No address found');
        return null;
      }
    } catch (e) {
      debugPrint('Error: $e');
      return null;
    }
  }

  Future<void> takePhoto(BuildContext context, {VoidCallback? onCameraRestart}) async {
    // CameraController'ın geçerli olup olmadığını kontrol et
    if (cameraController == null || !cameraController!.value.isInitialized) {
      debugPrint('Camera controller is not initialized');
      isPhotoChecking = false;
      return;
    }

    HapticFeedback.vibrate();

    isPhotoChecking = true;

    try {
      final XFile picture = await cameraController!.takePicture();

      // 1
      File file = File(picture.path);
      Uint8List imageBytes = await file.readAsBytes();

      // 2
      final ImageEditorOption option = ImageEditorOption();
      option.addOption(
        FlipOption(
          horizontal: isFrontCamera ? true : false,
        ),
      );

      imageBytes = (await ImageEditor.editImage(
        image: imageBytes,
        imageEditorOption: option,
      ))!;

      // 3
      await file.delete();
      await file.writeAsBytes(imageBytes);

      // CameraController'ın hala geçerli olup olmadığını kontrol et
      if (cameraController != null && cameraController!.value.isInitialized) {
        await cameraController!.pausePreview();
      }

      cameraPhoto = XFile(file.path);

      await photoDetectFace();

      // CameraController'ın hala geçerli olup olmadığını kontrol et
      if (cameraController != null && cameraController!.value.isInitialized) {
        await cameraController!.pausePreview();
      }

      LogService().takePhoto(accelometerTimerCount == 0);

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PhotoPreviewPage(
            picture: cameraPhoto!,
          ),
        ),
      ).then((_) {
        isPhotoChecking = false;
        accelometerTimerCount = 3;
        // PhotoPreview'dan dönüldüğünde kamerayı yeniden başlat
        if (cameraController != null && cameraController!.value.isInitialized) {
          cameraController!.resumePreview();
        } else {
          // Kamera controller'ı geçersizse callback ile yeniden başlatma sinyali gönder
          onCameraRestart?.call();
        }
        // Callback'i çağır (UI güncellemesi için)
        onCameraRestart?.call();
      });
    } catch (e) {
      debugPrint('Error taking photo: $e');
      isPhotoChecking = false;
      // Hata durumunda kamera controller'ı yeniden başlat
      onCameraRestart?.call();
    }
  }
}

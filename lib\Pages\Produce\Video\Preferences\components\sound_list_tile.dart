import 'package:audioplayers/audioplayers.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:facelog/Pages/Produce/Video/core/video_variables.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/product/models/sound_model.dart';
import 'package:facelog/product/utility/extension/extensions.dart';
import 'package:flutter/material.dart';

class SoundListTile extends StatefulWidget {
  final Function? onTap;
  final SoundModel soundModel;

  const SoundListTile({
    super.key,
    this.onTap,
    required this.soundModel,
  });

  @override
  State<SoundListTile> createState() => _SoundListTileState();
}

class _SoundListTileState extends State<SoundListTile> with TickerProviderStateMixin {
  // Music note animation
  late AnimationController _soundColorAnimController;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _soundColorAnimController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat(reverse: true);

    widget.soundModel.soundRotationAnimController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );
  }

  @override
  void dispose() {
    _soundColorAnimController.dispose();
    widget.soundModel.soundRotationAnimController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _colorAnimation = ColorTween(
      begin: AppColors.main,
      end: AppColors.deepMain,
    ).animate(_soundColorAnimController);
    return GestureDetector(
      onTap: () {
        widget.onTap!();
      },
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.panelBackground,
            borderRadius: AppColors.borderRadiusAll,
            border: widget.soundModel.isSelected
                ? Border.all(
                    color: AppColors.main,
                    width: 2,
                  )
                : Border.all(
                    color: AppColors.grey,
                    width: 2,
                  ),
          ),
          child: ListTile(
            leading: AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              decoration: BoxDecoration(
                color: widget.soundModel.isPlaying ? _colorAnimation.value : AppColors.background,
                borderRadius: AppColors.borderRadiusAll,
              ),
              padding: const EdgeInsets.all(12.0),
              child: RotationTransition(
                turns: widget.soundModel.soundRotationAnimController!,
                child: const Icon(Icons.music_note),
              ),
            ),
            title: AutoSizeText(
              widget.soundModel.name,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              children: [
                Slider(
                    min: 0,
                    max: widget.soundModel.duration.inSeconds.toDouble(),
                    thumbColor: AppColors.white,
                    value: (widget.soundModel.position.inSeconds.toDouble()),
                    onChanged: (value) {
                      setState(() {
                        Duration newPosition = Duration(seconds: value.toInt());

                        widget.soundModel.audioPlayer.seek(newPosition);
                      });
                    }),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    Text(widget.soundModel.position.formatTime()),
                    Text(widget.soundModel.duration.formatTime()),
                  ]),
                ),
              ],
            ),
            trailing: GestureDetector(
              onTap: () {
                _handlePlayButtonClick();
              },
              child: Icon(
                widget.soundModel.isPlaying ? Icons.pause : Icons.play_arrow,
                color: AppColors.text,
                size: 28,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handlePlayButtonClick() {
    if (widget.soundModel.isPlaying) {
      widget.soundModel.soundRotationAnimController!.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
      widget.soundModel.audioPlayer.pause();
    } else {
      widget.soundModel.soundRotationAnimController!.repeat();
      widget.soundModel.isObjectPermanent ? widget.soundModel.audioPlayer.play(AssetSource('audio/${widget.soundModel.name}.mp3')) : widget.soundModel.audioPlayer.play(DeviceFileSource(widget.soundModel.path));

      // Pause other sounds
      for (var soundModel in soundModelList) {
        if (soundModel != widget.soundModel) {
          soundModel.audioPlayer.pause();
          soundModel.soundRotationAnimController!.animateTo(
            0.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      }
    }
  }
}

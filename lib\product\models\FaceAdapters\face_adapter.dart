import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:hive_flutter/hive_flutter.dart';
part 'face_adapter.g.dart';

@HiveType(typeId: 5)
class FaceAdapter extends TypeAdapter<Face> {
  @override
  final typeId = 5;

  @override
  Face read(BinaryReader reader) {
    // Face properties read logic
    final boundingBox = reader.read();
    final headEulerAngleX = reader.read();
    final headEulerAngleY = reader.read();
    final headEulerAngleZ = reader.read();
    final landmarks = Map<FaceLandmarkType, FaceLandmark?>.from(reader.read());
    final contours = Map<FaceContourType, FaceContour?>.from(reader.read());
    final leftEyeOpenProbability = reader.read();
    final rightEyeOpenProbability = reader.read();
    final smilingProbability = reader.read();
    final trackingId = reader.read();

    return Face(
      boundingBox: boundingBox,
      headEulerAngleX: headEulerAngleX,
      headEulerAngleY: headEulerAngleY,
      headEulerAngleZ: headEulerAngleZ,
      landmarks: landmarks,
      contours: contours,
      leftEyeOpenProbability: leftEyeOpenProbability,
      rightEyeOpenProbability: rightEyeOpenProbability,
      smilingProbability: smilingProbability,
      trackingId: trackingId,
    );
  }

  @override
  void write(BinaryWriter writer, Face obj) {
    // Face properties write logic
    writer.write(obj.boundingBox);
    writer.write(obj.headEulerAngleX);
    writer.write(obj.headEulerAngleY);
    writer.write(obj.headEulerAngleZ);
    writer.write(obj.landmarks);
    writer.write(obj.contours);
    writer.write(obj.leftEyeOpenProbability);
    writer.write(obj.rightEyeOpenProbability);
    writer.write(obj.smilingProbability);
    writer.write(obj.trackingId);
  }
}

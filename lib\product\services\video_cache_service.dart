import 'package:facelog/product/models/Video/video_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

class VideoModelCacheService {
  static const String _videosHiveBox = 'videos';

  Future<Box<VideoModel>> get _box async {
    return await Hive.openBox<VideoModel>(_videosHiveBox);
  }

  Future<List<VideoModel>> getVideoList() async {
    final box = await _box;
    return box.values.toList();
  }

  Future<void> addVideo(VideoModel video) async {
    final box = await _box;
    await box.add(video);
  }

  Future<void> deleteVideo(int index) async {
    final box = await _box;
    await box.deleteAt(index);
  }
}

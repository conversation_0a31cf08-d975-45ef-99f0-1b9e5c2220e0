//! Gelecekte profil oluşturma özelliği aktifleşince burayı açmamız yeterli olacaktır.

// import 'package:facelog/Pages/Produce/Home/home_page.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';

// class AuthService {
//   final firebaseAuth = FirebaseAuth.instance;

//   Future<void> signIn(String email, String password, BuildContext context) async {
//     final navigator = Navigator.of(context);
//      TODO: ScaffoldMessenger kullanılamyacak getMessage kullanılacak
//     final messenger = ScaffoldMessenger.of(context);
//     try {
//       UserCredential userCredential = await firebaseAuth.signInWithEmailAndPassword(email: email, password: password);
//       if (userCredential.user != null) {
//         navigator.pushReplacement(MaterialPageRoute(builder: (context) => const HomePage()));
//       } else {
//         messenger.showSnackBar(const SnackBar(content: Text('Login failed')));
//       }
//     } catch (exception) {
//       messenger.showSnackBar(const SnackBar(content: Text('Login failed')));
//     }
//   }

//   Future<void> signUp(String email, String password, BuildContext context) async {
//     final navigator = Navigator.of(context);
//      TODO: ScaffoldMessenger kullanılamyacak getMessage kullanılacak
//     final messenger = ScaffoldMessenger.of(context);
//     try {
//       UserCredential userCredential = await firebaseAuth.createUserWithEmailAndPassword(email: email, password: password);
//       if (userCredential.user != null) {
//         navigator.pushReplacement(MaterialPageRoute(builder: (context) => const HomePage()));
//       } else {
//         messenger.showSnackBar(
//           const SnackBar(
//             content: Text('Sign up failed'),
//           ),
//         );
//       }
//     } catch (exception) {
//       messenger.showSnackBar(
//         const SnackBar(
//           content: Text('Sign up failed'),
//         ),
//       );
//     }
//   }
// }

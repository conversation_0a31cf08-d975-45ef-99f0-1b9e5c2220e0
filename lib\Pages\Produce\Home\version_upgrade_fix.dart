import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_statistics_container.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/enums/streak_status_enum.dart';
// ignore: unused_import
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/streak_model.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/photo/photo_service.dart';
import 'package:facelog/product/services/streak_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VerisonFix {
  VerisonFix({
    required this.context,
  });

  final BuildContext context;

  late int buildNumber;

  bool isAnyChanged = false;

  late final photoProvider = context.read<PhotoProvider>();

  Future<void> fixAll() async {
    final prefs = await SharedPreferences.getInstance();
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    buildNumber = prefs.getInt('buildNumber') ?? int.parse(packageInfo.buildNumber);

    await isImportedNullFix();
    await detectFaceAndSave();
    await cropExistingPhotos(); // Yeni migration fonksiyonu
    await getRecentStreak();

    if (isAnyChanged) {
      // update photo list
      await PhotoService().updateList(photoProvider.allPhotosList);
    }
    // update last versiyon
    buildNumber = int.parse(packageInfo.buildNumber);
    await prefs.setInt('buildNumber', buildNumber);
  }

  Future<void> detectFaceAndSave() async {
    // 18 den aşağı kimse kalmadığında silinebilir

    if (buildNumber > 18) {
      return;
    }

    isAnyChanged = true;

    // fotoğraf listesinde resolution olmayanların hepsini ayarla
    for (var photo in photoProvider.allPhotosList.expand((element) => element.photos)) {
      if (photo.face == null) {
        await photoDetectFace(photoPath: photo.path);
        photo.face = currentFace;
      }
    }
  }

  // TODO:
  // ! birkaç versiyon sonra kaldırılabilir (galiba eski verisyonları kullanan var mı görebiliyoruz. ona göre ayarlansın)
  // ? Photo modele isImported eklendi. Önceki versiyonlarda olmadığı için hive hata vermesini engellemek için bu kod
  Future<void> isImportedNullFix() async {
    // eğer herhangi biri null ise is imported
    if (photoProvider.allPhotosList.any((element) => element.photos.any((photo) => photo.isImported == null))) {
      isAnyChanged = true;

      for (var element in photoProvider.allPhotosList) {
        for (var photo in element.photos) {
          photo.isImported ??= false;
          try {
            photo.save();
          } catch (e) {
            try {
              LogService().logError("Photo save failed: ${e.toString()}");
            } catch (logError) {
              debugPrint("Log service error: $logError");
            }
          }
        }
      }
    }
  }

  Future<void> getRecentStreak() async {
    if (buildNumber > 23) {
      return;
    }

    if (photoProvider.allPhotosList.isEmpty) {
      return;
    }

    final now = DateTime.now();
    final firstDate = photoProvider.allPhotosList.first.date;
    int photoListIndex = 0;

    for (DateTime date = firstDate; date.isBefore(now); date = date.add(const Duration(days: 1))) {
      if ((photoProvider.allPhotosList.length > photoListIndex) && photoProvider.allPhotosList[photoListIndex].date == date) {
        if (photoProvider.allPhotosList[photoListIndex].photos.any((photo) => photo.isImported == false)) {
          streakList.add(StreakModel(
            date: date,
            status: StreakStatus.streak,
          ));
        }

        photoListIndex++;
      } else {
        streakList.add(StreakModel(
          date: date,
          status: StreakStatus.empty,
        ));
      }
    }

    StreakService().updateList();
  }

  /// Eski kullanıcıların kırpılmamış fotoğraflarını kırpar
  Future<void> cropExistingPhotos() async {
    // Build 24'ten sonraki versiyonlar için bu migration'ı çalıştırma
    if (buildNumber > 24) {
      return;
    }

    bool hasUncropedPhotos = false;

    // Kırpılmamış fotoğrafları kontrol et
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        // isCropped null ise veya false ise, bu fotoğraf kırpılmamış demektir
        if (photo.isCropped == null || photo.isCropped == false) {
          hasUncropedPhotos = true;
          break;
        }
      }
      if (hasUncropedPhotos) break;
    }

    // Eğer kırpılmamış fotoğraf yoksa, migration'ı çalıştırma
    if (!hasUncropedPhotos) {
      return;
    }

    isAnyChanged = true;

    // Kırpılmamış fotoğrafları tespit et ve kırp
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        // isCropped null ise veya false ise, bu fotoğrafı kırp
        if (photo.isCropped == null || photo.isCropped == false) {
          try {
            // Fotoğrafı yüz tespit fonksiyonu ile işle
            await photoDetectFace(photoPath: photo.path);

            // Eğer yüz tespit edildi ve kırpıldıysa
            if (currentFace != null) {
              photo.face = currentFace;
              photo.totalAccuracy = totalAccuracy;
              photo.isCropped = true;

              // Fotoğrafı Hive'a kaydet
              await photo.save();

              debugPrint("Fotoğraf kırpıldı: ${photo.path}");
            } else {
              // Yüz tespit edilemeyen fotoğrafı olduğu gibi bırak (silme)
              // Sadece isCropped = true olarak işaretle ki tekrar işlenmesin
              photo.isCropped = true;
              await photo.save();
              debugPrint("Yüz tespit edilemeyen fotoğraf işaretlendi: ${photo.path}");
            }
          } catch (e) {
            debugPrint("Migration - Fotoğraf işlenirken hata: ${photo.path} - $e");
            try {
              LogService().logError("Migration photo fix failed: ${photo.path} - ${e.toString()}");
            } catch (logError) {
              debugPrint("Log service error: $logError");
            }
            // Hata durumunda fotoğrafı olduğu gibi bırak
            // Sadece isCropped = true olarak işaretle ki tekrar işlenmesin
            photo.isCropped = true;
            try {
              await photo.save();
            } catch (saveError) {
              debugPrint("Photo save error: $saveError");
            }
            debugPrint("Hatalı fotoğraf işaretlendi: ${photo.path}");
          }
        }
      }
    }

    debugPrint("Eski fotoğraflar kırpma migration'ı tamamlandı");

    // UI'ı güncelle - migration sonrasında fotoğrafları yeniden yükle
    if (isAnyChanged) {
      await photoProvider.getAllPhotosInProvider();
    }
  }

  /// Kullanıcının kırpılmamış fotoğrafı olup olmadığını kontrol eder
  bool hasUncroppedPhotos() {
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        // isCropped null ise veya false ise, bu fotoğraf kırpılmamış demektir
        if (photo.isCropped == null || photo.isCropped == false) {
          return true;
        }
      }
    }
    return false;
  }

  /// Sadece kırpılmamış fotoğrafları düzelten fonksiyon (manuel çağırım için)
  Future<bool> fixUncroppedPhotosManually({
    Function(int current, int total)? onProgress,
  }) async {
    if (!hasUncroppedPhotos()) {
      return false; // Düzeltilecek fotoğraf yok
    }

    isAnyChanged = true;

    // Önce toplam kırpılmamış fotoğraf sayısını hesapla
    int totalUncroppedPhotos = 0;
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        if (photo.isCropped == null || photo.isCropped == false) {
          totalUncroppedPhotos++;
        }
      }
    }

    int processedCount = 0;

    // Kırpılmamış fotoğrafları tespit et ve kırp
    for (var oneDayPhotos in photoProvider.allPhotosList) {
      for (var photo in oneDayPhotos.photos) {
        // isCropped null ise veya false ise, bu fotoğrafı kırp
        if (photo.isCropped == null || photo.isCropped == false) {
          processedCount++;

          // Progress callback'i çağır
          onProgress?.call(processedCount, totalUncroppedPhotos);

          try {
            // Fotoğrafı yüz tespit fonksiyonu ile işle
            await photoDetectFace(photoPath: photo.path);

            // Eğer yüz tespit edildi ve kırpıldıysa
            if (currentFace != null) {
              photo.face = currentFace;
              photo.totalAccuracy = totalAccuracy;
              photo.isCropped = true;

              // Fotoğrafı Hive'a kaydet
              await photo.save();

              debugPrint("Fotoğraf kırpıldı: ${photo.path}");
            } else {
              // Yüz tespit edilemeyen fotoğrafı olduğu gibi bırak (silme)
              // Sadece isCropped = true olarak işaretle ki tekrar işlenmesin
              photo.isCropped = true;
              await photo.save();
              debugPrint("Yüz tespit edilemeyen fotoğraf işaretlendi: ${photo.path}");
            }
          } catch (e) {
            // Debug log için önce hata mesajını yazdır
            debugPrint("Manuel fix - Fotoğraf işlenirken hata: ${photo.path} - $e");

            // Firebase Analytics için güvenli log
            try {
              LogService().logError("Manual photo fix failed: ${photo.path} - ${e.toString()}");
            } catch (logError) {
              debugPrint("Log service error: $logError");
            }

            // Hata durumunda fotoğrafı olduğu gibi bırak
            // Sadece isCropped = true olarak işaretle ki tekrar işlenmesin
            photo.isCropped = true;
            try {
              await photo.save();
            } catch (saveError) {
              debugPrint("Photo save error: $saveError");
            }
            debugPrint("Hatalı fotoğraf işaretlendi: ${photo.path}");
          }
        }
      }
    }

    debugPrint("Manuel fotoğraf kırpma işlemi tamamlandı");

    // Fotoğraf listesini güncelle
    if (isAnyChanged) {
      await PhotoService().updateList(photoProvider.allPhotosList);
      // UI'ı güncelle - tüm fotoğrafları yeniden yükle
      await photoProvider.getAllPhotosInProvider();
    }

    return true; // İşlem başarılı
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/core/CostumWidgets/costum_button.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:facelog/product/config/Theme/app_colors.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

final class SelectOrientationPage extends StatefulWidget {
  final bool isTutorialPage;

  const SelectOrientationPage({
    super.key,
    required this.isTutorialPage,
  });

  @override
  State<SelectOrientationPage> createState() => _SelectOrientationPageState();
}

class _SelectOrientationPageState extends State<SelectOrientationPage> {
  bool newIsLandspace = isLandscape;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: widget.isTutorialPage ? 0.08.sh : 0.04.sh),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (!widget.isTutorialPage)
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                    ),
                  if (!widget.isTutorialPage) const Spacer(),
                  const Text(
                    LocaleKeys.SettingsPage_SettingsTiles_Orientation_Title,
                    style: TextStyle(
                      fontSize: 20,
                    ),
                    textAlign: TextAlign.center,
                  ).tr(),
                  if (!widget.isTutorialPage) const Spacer(),
                  if (!widget.isTutorialPage) const Spacer(),
                  // TODO: Bu kullanımın kod kalitesi için iyi olmadığını düşünüyorum.
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  newIsLandspace = true;
                });
                if (widget.isTutorialPage) {
                  isLandscape = newIsLandspace;
                  photoSize = isLandscape ? 0.58.sw : 0.7.sh;
                }
              },
              child: Container(
                color: newIsLandspace ? AppColors.main : AppColors.transparent,
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Container(
                      height: 135,
                      width: 240,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        border: Border.all(
                          width: 2,
                        ),
                      ),
                    ),
                    const Text(
                      '16:9',
                      style: TextStyle(
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  newIsLandspace = false;
                });
                if (widget.isTutorialPage) {
                  isLandscape = newIsLandspace;
                  photoSize = isLandscape ? 0.58.sw : 0.7.sh;
                }
              },
              child: Container(
                color: !newIsLandspace ? AppColors.main : AppColors.transparent,
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Container(
                      height: 240,
                      width: 135,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        border: Border.all(
                          width: 2,
                        ),
                      ),
                    ),
                    const Text(
                      '9:16',
                      style: TextStyle(
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),
            if (!widget.isTutorialPage) ...[
              CustomButton(
                buttonText: LocaleKeys.SettingsPage_SettingsTiles_Orientation_Save.tr(),
                buttonColor: AppColors.main,
                onPressed: () async {
                  if (newIsLandspace != isLandscape && context.read<PhotoProvider>().allPhotosList.isNotEmpty) {
                    await Helper().getDialog(
                        message: LocaleKeys.SettingsPage_SettingsTiles_Orientation_OrientatiionPopUp_Content.tr(),
                        withTimer: true,
                        onAccept: () async {
                          await context.read<PhotoProvider>().deletePhotoList();

                          isLandscape = newIsLandspace;
                          photoSize = isLandscape ? 0.58.sw : 0.7.sh;

                          SharedPreferences prefs = await SharedPreferences.getInstance();
                          await prefs.setBool('isLandscape', isLandscape);

                          LogService().changeOrientation();

                          Navigator.pop(context);
                        });
                  } else {
                    isLandscape = newIsLandspace;
                    photoSize = isLandscape ? 0.58.sw : 0.7.sh;

                    SharedPreferences prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('isLandscape', isLandscape);

                    LogService().changeOrientation();

                    Navigator.pop(context);
                  }
                },
              ),
              const SizedBox(height: 30),
            ]
          ],
        ),
      ),
    );
  }
}

import 'dart:io';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';

class FFmpegDebugService {
  /// FFmpeg ile basit bir video çıkarma testi yapar
  /// Cache'e test videosu kaydeder
  /// Ayrıca input fotoğraflarını da kontrol eder
  Future<void> testFFmpegVideoExport(BuildContext context) async {
    try {
      if (kDebugMode) {
        print('🎬 FFmpeg debug testi başlatılıyor...');
      }

      // Cache dizinini al
      final Directory cacheDir = await getApplicationCacheDirectory();
      final String debugVideoPath = '${cacheDir.path}/debug_test_video.mp4';

      // Mevcut fotoğrafları al
      final photoProvider = context.read<PhotoProvider>();
      final List<Photo> allPhotos = photoProvider.allPhotosList.map((e) => e.photos).expand((element) => element).where((element) => element.face != null).toList();

      if (allPhotos.isEmpty) {
        if (kDebugMode) {
          print('⚠️ Test için fotoğraf bulunamadı');
        }
        return;
      }

      // Sadece ilk 5 fotoğrafı kullan (test için)
      final List<Photo> testPhotos = allPhotos.take(5).toList();
      if (kDebugMode) {
        print('📷 Test için ${testPhotos.length} fotoğraf kullanılıyor');
      }

      // Fotoğrafların varlığını kontrol et
      for (int i = 0; i < testPhotos.length; i++) {
        final photoFile = File(testPhotos[i].path);
        final exists = await photoFile.exists();
        final size = exists ? await photoFile.length() : 0;

        if (kDebugMode) {
          print('📸 Fotoğraf ${i + 1}: ${testPhotos[i].path}');
          print('  Var mı: $exists, Boyut: $size bytes');
        }

        if (!exists) {
          if (kDebugMode) {
            print('❌ Fotoğraf bulunamadı: ${testPhotos[i].path}');
          }
          continue;
        }
      }

      // TXT dosyası oluştur (concat için)
      final txtFile = File("${cacheDir.path}/debug_photolist.txt");
      txtFile.createSync(recursive: true);

      // Her fotoğraf için 1 saniye duration
      const double photoDuration = 1.0;
      String txtContent = "";

      for (Photo photo in testPhotos) {
        // Fotoğraf dosyasının var olduğunu kontrol et
        final photoFile = File(photo.path);
        if (await photoFile.exists()) {
          txtContent += "file '${photo.path}'\n";
          txtContent += "duration $photoDuration\n";
        }
      }

      // Son fotoğraf için extra bir entry (FFmpeg concat sorunu için)
      if (testPhotos.isNotEmpty) {
        final lastPhotoFile = File(testPhotos.last.path);
        if (await lastPhotoFile.exists()) {
          txtContent += "file '${testPhotos.last.path}'\n";
        }
      }

      if (txtContent.isEmpty) {
        if (kDebugMode) {
          print('❌ Hiç geçerli fotoğraf bulunamadı');
        }
        return;
      }

      txtFile.writeAsStringSync(txtContent);

      if (kDebugMode) {
        print('📝 TXT dosyası oluşturuldu: ${txtFile.path}');
        print('📝 TXT içeriği:\n$txtContent');
      }

      // FFmpeg komutunu hazırla
      String ffmpegCommand = '';

      // Input olarak concat dosyasını kullan
      ffmpegCommand += '-f concat -safe 0 -i ${txtFile.path} ';

      // Basit scale (debug için küçük boyut)
      if (isLandscape) {
        ffmpegCommand += '-vf scale=640:360 ';
      } else {
        ffmpegCommand += '-vf scale=360:640 ';
      }

      // Video quality
      ffmpegCommand += '-qscale:v 5 ';

      // Codec settings
      ffmpegCommand += '-c:v libx264 -preset ultrafast ';

      // Output path
      ffmpegCommand += '-y $debugVideoPath';

      if (kDebugMode) {
        print('🎬 FFmpeg komutu: $ffmpegCommand');
      }

      // FFmpeg komutunu çalıştır
      final session = await FFmpegKit.execute(ffmpegCommand);
      final returnCode = await session.getReturnCode();

      if (kDebugMode) {
        final logs = await session.getLogs();
        print('🔍 FFmpeg return code: $returnCode');
        print('📋 FFmpeg logs:');
        for (var log in logs) {
          print('  ${log.getMessage()}');
        }
      }

      // Sonucu kontrol et
      final outputFile = File(debugVideoPath);
      if (await outputFile.exists()) {
        final fileSizeMB = (await outputFile.length()) / (1024 * 1024);
        if (kDebugMode) {
          print('✅ Debug video başarıyla oluşturuldu!');
          print('📁 Konum: $debugVideoPath');
          print('📊 Boyut: ${fileSizeMB.toStringAsFixed(2)} MB');
        }
      } else {
        if (kDebugMode) {
          print('❌ Debug video oluşturulamadı');
        }
      }

      // Geçici dosyaları temizle
      if (await txtFile.exists()) {
        await txtFile.delete();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ FFmpeg debug test hatası: $e');
      }
    }
  }

  /// Cache'deki tüm debug dosyalarını temizler
  Future<void> clearDebugFiles() async {
    try {
      final Directory cacheDir = await getApplicationCacheDirectory();

      // Debug dosyalarını bul ve sil
      final List<String> debugFiles = [
        'debug_test_video.mp4',
        'debug_photolist.txt',
        'debug_export_video.mp4',
      ];

      for (String fileName in debugFiles) {
        final file = File('${cacheDir.path}/$fileName');
        if (await file.exists()) {
          await file.delete();
          if (kDebugMode) {
            print('🗑️ Debug dosyası silindi: $fileName');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Debug dosyaları temizlenirken hata: $e');
      }
    }
  }

  /// Cache'deki video dosyalarını listeler
  Future<List<String>> getDebugVideoFiles() async {
    try {
      final Directory cacheDir = await getApplicationCacheDirectory();
      final List<String> videoFiles = [];

      // Cache dizinindeki tüm dosyaları kontrol et
      await for (FileSystemEntity entity in cacheDir.list()) {
        if (entity is File) {
          final String fileName = entity.path.split('/').last;
          if (fileName.contains('debug') && fileName.endsWith('.mp4')) {
            videoFiles.add(fileName);
          }
        }
      }

      return videoFiles;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Debug video dosyaları listelenirken hata: $e');
      }
      return [];
    }
  }
}

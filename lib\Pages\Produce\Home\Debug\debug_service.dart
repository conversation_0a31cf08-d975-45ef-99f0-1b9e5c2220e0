import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/Home/Debug/test_crop_loading_page.dart';
import 'package:facelog/Pages/Produce/Video/Service/rendering_api.dart';
import 'package:facelog/Pages/Produce/Video/core/path_constants.dart';
import 'package:facelog/Pages/Produce/Video/Preferences/mixin/video_preferences_mixin.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/models/Photo/one_day_photos.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:facelog/product/services/photo/photo_service.dart';
import 'package:facelog/product/state/Provider/photo_provider.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DebugService {
  /// Test crop sayfasına yönlendirir
  static Future<void> testRenderFunction({
    required BuildContext context,
    required String qualityIndex,
    required String videoLength,
    required Function() onLoadingStart,
    required Function() onLoadingEnd,
  }) async {
    // Loading sayfasına git
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => TestCropLoadingPage(
          qualityIndex: qualityIndex,
          videoLength: videoLength,
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.fastOutSlowIn;

          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }

  /// Test için fotoğrafları geçici olarak crop yapar (orijinalleri değiştirmez)
  static Future<void> performTestCropAndVideo({
    required BuildContext context,
    required String qualityIndex,
    required String videoLength,
    required Function(String) onStatusUpdate,
    required Function(String) onProgressUpdate,
  }) async {
    try {
      onStatusUpdate("Fotoğraflar hazırlanıyor...");
      onProgressUpdate("0%");

      // 1. Geçici klasör oluştur
      final Directory tempDir = await getTemporaryDirectory();
      final String testCropFolder = "${tempDir.path}/test_crop_${DateTime.now().millisecondsSinceEpoch}";
      await Directory(testCropFolder).create(recursive: true);

      onStatusUpdate("Fotoğraflar kopyalanıyor...");
      onProgressUpdate("10%");

      // 2. Orijinal fotoğrafları al (hız için sadece son 15 fotoğrafı)
      final allPhotos = await PhotoService().getAllPhotosInHive();
      final List<Photo> allPhotosList = [];

      for (final dayPhotos in allPhotos) {
        allPhotosList.addAll(dayPhotos.photos);
      }

      if (allPhotosList.isEmpty) {
        onStatusUpdate("Fotoğraf bulunamadı");
        return;
      }

      // Hız için sadece son 15 fotoğrafı al
      final limitedPhotos = allPhotosList.length > 15 ? allPhotosList.sublist(allPhotosList.length - 15) : allPhotosList;

      onStatusUpdate("${limitedPhotos.length} fotoğraf işlenecek...");
      onProgressUpdate("20%");

      // 3. Geçici fotoğraf listesi oluştur (zaten crop'lanmış olanları kullan)
      final List<Photo> testPhotos = [];
      int processedCount = 0;

      for (final photo in limitedPhotos) {
        if (File(photo.path).existsSync()) {
          try {
            // Geçici fotoğraf path'i oluştur
            final String tempPhotoPath = "$testCropFolder/temp_$processedCount.jpg";

            // Dosyayı kopyala
            await File(photo.path).copy(tempPhotoPath);

            // Eğer kırpılmamışsa hızlı crop yap
            Face? detectedFace = photo.face; // Orijinal face'i koru

            if (photo.isCropped != true) {
              await photoDetectFace(photoPath: tempPhotoPath);
              detectedFace = currentFace; // Yeni tespit edilen face'i al
            }

            // Test fotoğraf modeli oluştur
            testPhotos.add(Photo(
              path: tempPhotoPath,
              date: photo.date,
              isFavorite: false,
              isImported: true,
              isCropped: true,
              face: detectedFace, // Tespit edilen veya orijinal face'i ata
            ));

            processedCount++;

            // Progress güncelle
            final progress = 20 + ((processedCount / limitedPhotos.length) * 40).round();
            onProgressUpdate("$progress%");

            if (processedCount % 5 == 0) {
              onStatusUpdate("İşleniyor: $processedCount/${limitedPhotos.length}");
            }
          } catch (e) {
            debugPrint('Test crop hatası: ${photo.path} - $e');
          }
        }
      }

      if (testPhotos.isEmpty) {
        onStatusUpdate("İşlenebilir fotoğraf bulunamadı");
        return;
      }

      onStatusUpdate("Test fotoğraf listesi oluşturuluyor...");
      onProgressUpdate("65%");

      // 4. Geçici OneDayPhotos listesi oluştur
      final testOneDayPhotos = [
        OneDayPhotos(
          date: DateTime.now(),
          photos: testPhotos,
        )
      ];

      // 5. Global listeyi geçici olarak güncelle (VideoAPI için)
      await PhotoService().updateList(testOneDayPhotos);

      // 6. VideoAPI için hızlı çözüm: Orijinal fotoğraf listesini de güncelle
      // Bu şekilde rendering_api.dart doğru fotoğrafları bulacak
      if (context.mounted) {
        final photoProvider = context.read<PhotoProvider>();
        final originalList = photoProvider.allPhotosList;
        photoProvider.allPhotosList = testOneDayPhotos;

        // Video işlemi bittikten sonra orijinal listeyi geri yüklemek için kaydet
        debugPrint('✅ PhotoProvider geçici olarak güncellendi. Face olan fotoğraf sayısı: ${testPhotos.where((p) => p.face != null).length}');

        // İşlem bittikten sonra orijinal listeyi geri yükle (video preview sayfası için yeterli süre)
        Timer(Duration(seconds: 10), () {
          if (context.mounted) {
            photoProvider.allPhotosList = originalList;
            debugPrint('✅ PhotoProvider orijinal listeye geri yüklendi');
          }
        });
      }

      onStatusUpdate("Video oluşturuluyor...");
      onProgressUpdate("75%");

      // 6. Video export path'lerini hazırla
      final Directory appDocDir = await getApplicationDocumentsDirectory();

      if (Platform.isIOS) {
        final Directory newDir = Directory(path.join(appDocDir.path, 'Facelog'));
        if (!await newDir.exists()) {
          await newDir.create(recursive: true);
        }
      } else if (Platform.isAndroid) {
        Directory(faceLogDownload).createSync(recursive: true);
      }

      Directory cachePath = await getApplicationCacheDirectory();
      String dateString = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());

      faceLogExportVideoRaw = "${cachePath.path}/${dateString}_test_crop_raw.mp4";
      faceLogExportVideoLogo = "${cachePath.path}/${dateString}_test_crop_logo.mp4";
      if (Platform.isAndroid) {
        faceLogExportVideo = "$faceLogDownload${dateString}_test_crop.mp4";
      } else if (Platform.isIOS) {
        faceLogExportVideo = "${appDocDir.path}/Facelog/${dateString}_test_crop.mp4";
      }

      onProgressUpdate("85%");

      // 7. Video oluştur - Dikkatli şekilde
      onStatusUpdate("Video oluşturuluyor... (Sayfayı kapatmayın!)");

      try {
        // Video API'yi çağırmadan önce state'i hazırla
        isVideoExportProgressLoading = true;

        await VideoAPI().mergeImagesToVideo(
          context: context,
          qualityIndex: qualityIndex,
          videoLength: videoLength,
          isAudioNull: true,
          isBrandLogoActive: false,
        );

        onProgressUpdate("95%");

        // Video dosyasının varlığını kontrol et
        final videoFile = File(faceLogExportVideo);
        if (await videoFile.exists()) {
          final fileSizeMB = (await videoFile.length()) / (1024 * 1024);
          debugPrint('✅ Test video oluşturuldu: $faceLogExportVideo (${fileSizeMB.toStringAsFixed(2)} MB)');
          onStatusUpdate("Video başarıyla oluşturuldu! (${fileSizeMB.toStringAsFixed(1)} MB)");
        } else {
          debugPrint('❌ Test video dosyası bulunamadı: $faceLogExportVideo');
          onStatusUpdate("Video oluşturuldu ama dosya bulunamadı!");
        }
      } catch (videoError) {
        LogService().logError("Video oluşturma hatası: $videoError");
        onStatusUpdate("Video oluşturma hatası: $videoError");
        // Video hatası olsa bile devam et
      } // 8. Orijinal fotoğraf listesini geri yükle
      await PhotoService().updateList(allPhotos);

      // 9. Geçici klasörü temizle
      try {
        await Directory(testCropFolder).delete(recursive: true);
      } catch (e) {
        debugPrint('Geçici klasör temizleme hatası: $e');
      }

      onStatusUpdate("Tamamlandı!");
      onProgressUpdate("100%");
    } catch (e) {
      LogService().logError("Test crop & video hatası: $e");
      onStatusUpdate("Hata oluştu: $e");

      // Hata durumunda da orijinal listeyi geri yükle
      try {
        final allPhotos = await PhotoService().getAllPhotosInHive();
        await PhotoService().updateList(allPhotos);
      } catch (restoreError) {
        debugPrint('Liste geri yükleme hatası: $restoreError');
      }

      rethrow;
    }
  }

  /// Kırpılmamış fotoğraf import etme fonksiyonu
  static Future<void> importUncroppedPhotos({
    required PhotoProvider photoProvider,
    required Function(bool) setLoadingPhotos,
    required Function() onCheckForUncroppedPhotos,
  }) async {
    try {
      // Klasör seçme
      String? selectedFolder = await FilePicker.platform.getDirectoryPath();

      if (selectedFolder == null) {
        Helper().getMessage(
          message: "Klasör seçilmedi.",
          icon: Icons.folder_off,
        );
        return;
      }

      // Klasördeki fotoğrafları bul
      final selectedDirectory = Directory(selectedFolder);
      if (!await selectedDirectory.exists()) {
        Helper().getMessage(
          message: "Seçilen klasör bulunamadı.",
          icon: Icons.error,
        );
        return;
      }

      List<File> photoFiles = [];
      try {
        photoFiles = selectedDirectory.listSync().where((element) => element.path.toLowerCase().endsWith('.jpg') || element.path.toLowerCase().endsWith('.jpeg') || element.path.toLowerCase().endsWith('.png')).map((e) => File(e.path)).toList();
      } catch (e) {
        Helper().getMessage(
          message: "Klasör okunamadı: $e",
          icon: Icons.error,
        );
        return;
      }

      if (photoFiles.isEmpty) {
        Helper().getMessage(
          message: "Klasörde fotoğraf bulunamadı.",
          icon: Icons.info,
        );
        return;
      }

      // Loading durumunu göster
      setLoadingPhotos(true);

      try {
        // Fotoğrafları direkt import et (kırpma yapmadan)
        Directory directory = await getApplicationDocumentsDirectory();
        directory = Directory("${directory.path}/Photos");
        Directory(directory.path).createSync(recursive: true);

        int importedCount = 0;

        for (File photoFile in photoFiles) {
          try {
            lastPhotoNumber++;

            final DateTime photoDate = DateTime.now();
            final String newPhotoPath = "${directory.path}/$lastPhotoNumber.jpg";

            // Fotoğrafı kopyala
            await photoFile.copy(newPhotoPath);

            // Photo modeli oluştur (kırpılmamış olarak)
            final Photo newPhoto = Photo(
              path: newPhotoPath,
              date: photoDate,
              isFavorite: false,
              isImported: true,
              isCropped: false, // Önemli: kırpılmamış olarak işaretle
              face: null, // Yüz tespit edilmemiş
            );

            // Bugünkü tarihte fotoğraf listesi var mı kontrol et
            bool todayExists = photoProvider.allPhotosList.isNotEmpty && Helper().isSameDay(photoProvider.allPhotosList.last.date, photoDate);

            if (todayExists) {
              // Var olan güne ekle
              await photoProvider.addPhotoInDay(
                photo: newPhoto,
                dayNoteText: null,
                cacheImagePath: photoFile.path,
                isImport: true,
              );
            } else {
              // Yeni gün oluştur
              await photoProvider.addOnePhotoDay(
                oneDayPhoto: OneDayPhotos(
                  date: photoDate,
                  photos: [newPhoto],
                ),
                cacheImagePath: photoFile.path,
                isImport: true,
              );
            }

            importedCount++;
          } catch (e) {
            debugPrint('Fotoğraf import hatası: ${photoFile.path} - $e');
          }
        }

        // lastPhotoNumber'ı kaydet
        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setInt('lastPhotoNumber', lastPhotoNumber);

        // Fotoğraf listesini güncelle
        photoProvider.allPhotosList.sort((a, b) => a.date.compareTo(b.date));
        await PhotoService().updateList(photoProvider.allPhotosList);

        Helper().getMessage(
          message: "$importedCount fotoğraf kırpılmadan import edildi!",
          icon: Icons.check_circle,
        );

        // showFixButton'u tekrar kontrol et
        onCheckForUncroppedPhotos();
      } catch (e) {
        LogService().logError("Import hatası: $e");
        Helper().getMessage(
          message: "Import işlemi sırasında hata oluştu: $e",
          icon: Icons.error,
        );
      } finally {
        setLoadingPhotos(false);
      }
    } catch (e) {
      Helper().getMessage(
        message: "Beklenmeyen hata: $e",
        icon: Icons.error,
      );
    }
  }

  /// Premium durumu değiştir
  static Future<void> togglePremiumStatus(bool newStatus) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isPremium', newStatus);

    Helper().getMessage(
      message: newStatus ? 'TEST: Premium Üyelik Aktif' : 'TEST: Premium Üyelik Deaktif',
      icon: newStatus ? Icons.check_circle : Icons.cancel,
    );

    debugPrint('Saved Premium State: $newStatus');
  }
}

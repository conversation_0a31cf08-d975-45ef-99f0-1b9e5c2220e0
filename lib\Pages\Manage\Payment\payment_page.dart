import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:facelog/Pages/Manage/Payment/Market/market_view.dart';
import 'package:facelog/Pages/Manage/Payment/market_not_available_page.dart';
import 'package:facelog/Pages/Manage/Payment/paid_view.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

class PaymentPage extends StatefulWidget {
  const PaymentPage({
    super.key,
  });

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  bool? _isAvailable;

  @override
  void initState() {
    super.initState();
    isMarketAvailable();

    LogService().logScreen("PaymentPage");
  }

  Future<void> isMarketAvailable() async {
    final InAppPurchase inAppPurchase = InAppPurchase.instance;
    final bool isAvailable = await inAppPurchase.isAvailable();
    List<ConnectivityResult> connectivityResults = await (Connectivity().checkConnectivity());

    if (!connectivityResults.contains(ConnectivityResult.none)) {
      setState(() {
        _isAvailable = isAvailable;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isPremium!) {
      return const PaidView();
    } else {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Facelog Pro'),
          leading: IconButton(
            padding: const EdgeInsets.only(left: 10),
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
        body: _isAvailable == null
            ? const Center(child: CircularProgressIndicator())
            : _isAvailable!
                ? const MarketView()
                : TheMarketNotAvailable(),
      );
    }
  }
}

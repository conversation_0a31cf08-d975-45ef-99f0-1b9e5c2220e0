import 'package:easy_localization/easy_localization.dart';
import 'package:facelog/Pages/Manage/Settings/Items/About%20Page/pdf_viewer.dart';
import 'package:facelog/Pages/Manage/Settings/Items/HelpPage/services_tab.dart';
import 'package:facelog/core/accessiblity/assets_path.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/services/log_service.dart';
import 'package:flutter/material.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    LogService().logScreen("AboutPage");

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          LocaleKeys.SettingsPage_SettingsTiles_About_Header.tr(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        child: Column(
          children: [
            NewTabForServices(
              title: LocaleKeys.SettingsPage_SettingsTiles_About_TermsOfService.tr(),
              iconData: Icons.contact_page_rounded,
              onTap: () {
                LogService().logScreen("TermsOfService");
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PDFviewer(
                      text: LocaleKeys.SettingsPage_SettingsTiles_About_TermsOfService.tr(),
                      markdownDataPath: AssetsPath.termsOfServiceFacelog,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 15),
            NewTabForServices(
                title: LocaleKeys.SettingsPage_SettingsTiles_About_PrivacyPolicy.tr(),
                iconData: Icons.find_in_page,
                onTap: () {
                  LogService().logScreen("PrivacyPolicy");
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PDFviewer(
                        text: LocaleKeys.SettingsPage_SettingsTiles_About_PrivacyPolicy.tr(),
                        markdownDataPath: AssetsPath.privacyPolicyFacelog,
                      ),
                    ),
                  );
                }),
          ],
        ),
      ),
    );
  }
}

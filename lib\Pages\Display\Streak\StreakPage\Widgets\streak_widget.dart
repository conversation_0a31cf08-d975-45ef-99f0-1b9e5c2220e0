import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/streak_day_widget.dart';
import 'package:facelog/Pages/Display/Streak/StreakPage/Widgets/streak_dots_widget.dart';
import 'package:facelog/core/accessiblity/accessible_variables.dart';
import 'package:facelog/product/services/streak_service.dart';
import 'package:flutter/material.dart';

class StreakWidget extends StatefulWidget {
  const StreakWidget({super.key});

  @override
  State<StreakWidget> createState() => _StreakWidgetState();
}

class _StreakWidgetState extends State<StreakWidget> {
  @override
  void initState() {
    super.initState();

    getLast7Day();
  }

  @override
  Widget build(BuildContext context) {
    return streakList.isEmpty
        ? const SizedBox(
            height: 150,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          )
        : Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              children: [
                StreakDayNumber(),
                const Si<PERSON><PERSON><PERSON>(height: 5),
                const StreaksDotsWidget(),
              ],
            ),
          );
  }

  Future<void> getLast7Day() async {
    if (streakList.isEmpty) {
      await StreakService().getLastDays();

      setState(() {});
    }
  }
}
